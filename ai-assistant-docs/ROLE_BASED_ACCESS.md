# Lighten Counsel - Role-Based Access Control

**Last Updated**: July 6, 2025  
**Authentication Provider**: Clerk  
**Authorization Model**: Role-Based Access Control (RBAC)  
**Database Security**: Supabase Row Level Security (RLS)

## 🔐 Authentication System

### Clerk Integration

**Files**: `src/middleware.ts`, `src/hooks/use-current-user.tsx`  
**Provider**: Clerk (v6.12.12)

```typescript
// Middleware configuration (src/middleware.ts)
import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

const isProtectedRoute = createRouteMatcher(['/dashboard(.*)']);

export default clerkMiddleware(async (auth, req: NextRequest) => {
  if (isProtectedRoute(req)) await auth.protect();
});
```

### User Session Management

**File**: `src/hooks/use-current-user.tsx`

```typescript
interface User {
  id: string;
  clerk_id: string;
  email: string;
  role: 'student' | 'consultant' | 'admin';
  profile_data: any;
  profile?: StudentProfile | ConsultantProfile;
}

// Hook usage
const { user, loading, error, refetch } = useCurrentUser();
```

### Authentication Flow

1. **User Registration**: Clerk handles OAuth (Google recommended)
2. **Webhook Processing**: `src/app/api/webhooks/clerk/route.ts` creates user in database
3. **Role Assignment**: Default role is 'student', admins can modify roles
4. **Session Management**: Clerk manages sessions, app fetches user data
5. **Profile Creation**: Automatic profile creation based on user role

## 👥 User Roles and Permissions

### Role Hierarchy

```
Admin (Full Access)
├── System Management
├── User Management
├── All Student Data
└── All Consultant Data

Consultant (Limited Access)
├── Assigned Students Only
├── Document Collaboration
└── Student Progress Tracking

Student (Own Data Only)
├── Personal Profile
├── Own Documents
└── Own Application Data
```

### Role Definitions

#### Student Role (`student`)

**Default Role**: Yes  
**Access Scope**: Own data only  
**Navigation**: Student-specific sidebar

**Permissions**:

- ✅ View/edit own profile and academic records
- ✅ Create/edit own documents and essays
- ✅ Collaborate with assigned consultants
- ✅ Access template library
- ❌ View other students' data
- ❌ Administrative functions

#### Consultant Role (`consultant`)

**Default Role**: No (admin assignment required)  
**Access Scope**: Assigned students only  
**Navigation**: Consultant-specific sidebar

**Permissions**:

- ✅ View assigned students' profiles and data
- ✅ Collaborate on assigned students' documents
- ✅ Create documents for assigned students
- ✅ Access analytics for assigned students
- ✅ Recommend templates to students
- ❌ View unassigned students' data
- ❌ User management functions
- ❌ System administration

#### Admin Role (`admin`)

**Default Role**: No (manual assignment only)  
**Access Scope**: Full system access  
**Navigation**: Admin-specific sidebar

**Permissions**:

- ✅ Full user management (create, edit, delete, role changes)
- ✅ Access all student and consultant data
- ✅ Document ownership and permission management
- ✅ Template creation and management
- ✅ System analytics and monitoring
- ✅ Consultant-student assignment management
- ✅ System configuration and settings

## 🛡️ API Security Implementation

### Authentication Middleware

**File**: `src/lib/api-utils.ts`

```typescript
// Standard authentication wrapper
export const withStandardAuth = (handler: AuthenticatedHandler) => {
  return async (request: NextRequest, context?: any) => {
    try {
      const currentUser = await getAuthenticatedUser();
      return handler(request, currentUser, context);
    } catch (error) {
      return createErrorResponse('Authentication required', 401);
    }
  };
};

// Role-based authentication wrapper
export const withStandardRole = (
  allowedRoles: string[],
  handler: RoleHandler
) => {
  return withStandardAuth(async (request, currentUser, context) => {
    requireRole(currentUser, allowedRoles);
    return handler(request, currentUser, context);
  });
};
```

### Permission Checking Functions

```typescript
// Check if user can access student data
export async function canAccessStudent(
  user: User,
  studentId: string
): Promise<boolean> {
  if (user.role === 'admin') return true;
  if (user.role === 'student') return user.profile?.id === studentId;
  if (user.role === 'consultant') {
    // Check if consultant is assigned to student
    const { data } = await supabase
      .from('student_consultant_relations')
      .select('id')
      .eq('student_id', studentId)
      .eq('consultant_id', user.profile?.id)
      .eq('status', 'active')
      .single();
    return !!data;
  }
  return false;
}

// Check if user can access document
export async function canAccessDocument(
  user: User,
  documentId: string
): Promise<boolean> {
  const { data: document } = await supabase
    .from('documents')
    .select('student_id')
    .eq('id', documentId)
    .single();

  if (!document) return false;
  return canAccessStudent(user, document.student_id);
}
```

### API Endpoint Security Examples

#### Student-Only Endpoint

```typescript
// src/app/api/students/me/route.ts
export const GET = withStandardRole(
  ['student'],
  async (request, currentUser) => {
    // Only students can access their own profile
    return getStudentProfile(currentUser.profile.id);
  }
);
```

#### Consultant/Admin Endpoint

```typescript
// src/app/api/students/route.ts
export const GET = withStandardRole(
  ['consultant', 'admin'],
  async (request, currentUser) => {
    if (currentUser.role === 'consultant') {
      // Return only assigned students
      return getAssignedStudents(currentUser.profile.id);
    } else {
      // Admin gets all students
      return getAllStudents();
    }
  }
);
```

#### Admin-Only Endpoint

```typescript
// src/app/api/users/route.ts
export const POST = withStandardRole(
  ['admin'],
  async (request, currentUser) => {
    // Only admins can create users
    return createUser(requestBody);
  }
);
```

## 🗄️ Database Security (Row Level Security)

### RLS Policies

**File**: `database/schema.sql`

```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE template_metadata ENABLE ROW LEVEL SECURITY;

-- Users can only see their own record
CREATE POLICY "Users can view own record" ON users
  FOR SELECT USING (auth.uid()::text = clerk_id);

-- Students can only see their own profile
CREATE POLICY "Students can view own profile" ON student_profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.id = student_profiles.user_id
      AND users.clerk_id = auth.uid()::text
    )
  );

-- Documents are accessible based on student access
CREATE POLICY "Document access based on student access" ON documents
  FOR SELECT USING (
    -- Student can see own documents
    EXISTS (
      SELECT 1 FROM student_profiles sp
      JOIN users u ON sp.user_id = u.id
      WHERE sp.id = documents.student_id
      AND u.clerk_id = auth.uid()::text
    )
    OR
    -- Consultant can see assigned student documents
    EXISTS (
      SELECT 1 FROM student_consultant_relations scr
      JOIN consultants c ON scr.consultant_id = c.id
      JOIN users u ON c.user_id = u.id
      WHERE scr.student_id = documents.student_id
      AND u.clerk_id = auth.uid()::text
      AND scr.status = 'active'
    )
  );
```

### Service Role Access

**File**: `src/lib/supabase.ts`

```typescript
// Client-side Supabase (RLS enforced)
export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// Server-side Supabase (bypasses RLS for admin operations)
export const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
```

## 🧭 Navigation Security

### Role-Based Navigation

**File**: `src/constants/data.ts`

```typescript
// Navigation configuration by role
export const getNavigationByRole = (role: string) => {
  switch (role) {
    case 'student':
      return [
        { title: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
        { title: 'Essays', href: '/dashboard/essays', icon: FileText },
        {
          title: 'Academics',
          href: '/dashboard/academics',
          icon: GraduationCap
        },
        { title: 'Activities', href: '/dashboard/activities', icon: Trophy },
        { title: 'Schools', href: '/dashboard/schools', icon: Building },
        { title: 'Documents', href: '/dashboard/documents', icon: FolderOpen }
      ];

    case 'consultant':
      return [
        { title: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
        { title: 'Students', href: '/dashboard/students', icon: Users },
        { title: 'Documents', href: '/dashboard/documents', icon: FileText },
        { title: 'Analytics', href: '/dashboard/analytics', icon: BarChart3 }
      ];

    case 'admin':
      return [
        { title: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
        { title: 'User Management', href: '/dashboard/users', icon: Users },
        { title: 'Analytics', href: '/dashboard/analytics', icon: BarChart3 },
        { title: 'System Health', href: '/dashboard/system', icon: Activity }
      ];

    default:
      return [];
  }
};
```

### Route Protection

**File**: `src/middleware.ts`

```typescript
// All dashboard routes require authentication
const isProtectedRoute = createRouteMatcher(['/dashboard(.*)']);

export default clerkMiddleware(async (auth, req: NextRequest) => {
  if (isProtectedRoute(req)) await auth.protect();
});
```

## 🔄 User Management Workflow

### Role Assignment Process

1. **New User Registration**: Default role is 'student'
2. **Admin Review**: Admins can change roles via `/dashboard/users`
3. **Consultant Assignment**: Admins assign consultants to students
4. **Permission Propagation**: Changes take effect immediately
5. **Audit Trail**: All role changes are logged

### Consultant-Student Assignment

**File**: `src/lib/permission-manager.ts`

```typescript
// Assign consultant to student
export async function assignConsultantToStudent(
  consultantId: string,
  studentId: string
): Promise<void> {
  // Create relationship record
  await supabaseAdmin.from('student_consultant_relations').insert({
    consultant_id: consultantId,
    student_id: studentId,
    status: 'active',
    assigned_at: new Date().toISOString()
  });

  // Update document permissions
  await updateStudentDocumentPermissions(studentId);
}
```

## 🔍 Security Monitoring

### Authentication Events

- User login/logout tracking
- Failed authentication attempts
- Role change auditing
- Permission escalation monitoring

### Access Control Auditing

- Document access logging
- API endpoint usage tracking
- Permission violation attempts
- Unusual access pattern detection

### Security Best Practices

1. **Principle of Least Privilege**: Users get minimum required access
2. **Defense in Depth**: Multiple security layers (middleware, RLS, API checks)
3. **Regular Audits**: Periodic permission and access reviews
4. **Secure Defaults**: New users start with minimal permissions
5. **Session Management**: Automatic session expiration and refresh

## 🔗 Cross-References

- **API Security**: See `ai-assistant-docs/API_DOCUMENTATION.md`
- **Component Access**: See `ai-assistant-docs/COMPONENT_LIBRARY.md`
- **Feature Permissions**: See `ai-assistant-docs/FEATURE_SPECIFICATIONS.md`
- **Development Setup**: See `ai-assistant-docs/DEVELOPMENT_GUIDE.md`
- **Troubleshooting**: See `ai-assistant-docs/TROUBLESHOOTING.md`
