# Lighten Counsel - Component Library

**Last Updated**: July 6, 2025  
**UI Framework**: shadcn/ui with Radix UI primitives  
**Styling**: Tailwind CSS with consistent design tokens  

## 🎨 Design System

### Color Scheme
- **Primary**: Blue-based theme with purple accents
- **Background**: `bg-background` (white/dark mode adaptive)
- **Cards**: `bg-card` with `border` and `shadow-sm`
- **Text**: `text-foreground` with `text-muted-foreground` for secondary text

### Layout Patterns
```typescript
// Standard page layout pattern
<div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
  <div className="flex items-center justify-between">
    <h1 className="text-3xl font-bold tracking-tight">Page Title</h1>
    <Button>Action Button</Button>
  </div>
  
  {/* Stats cards grid */}
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    {/* Content */}
  </div>
</div>
```

## 🧩 Core UI Components (`src/components/ui/`)

### Button Component (`button.tsx`)
**File**: `src/components/ui/button.tsx`  
**Base**: Radix UI Slot with class-variance-authority  

```typescript
interface ButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  asChild?: boolean
}

// Usage examples
<Button variant="default">Primary Action</Button>
<Button variant="outline" size="sm">Secondary Action</Button>
<Button variant="ghost" size="icon"><Icon /></Button>
```

### Card Component (`card.tsx`)
**File**: `src/components/ui/card.tsx`  
**Purpose**: Container component for content sections  

```typescript
// Card structure
<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
    <CardDescription>Description</CardDescription>
  </CardHeader>
  <CardContent>
    {/* Main content */}
  </CardContent>
</Card>
```

### Input Component (`input.tsx`)
**File**: `src/components/ui/input.tsx`  
**Features**: Focus states, validation styling, responsive design  

```typescript
<Input 
  type="text" 
  placeholder="Enter text..." 
  className="additional-classes"
/>
```

### Dialog Component (`dialog.tsx`)
**File**: `src/components/ui/dialog.tsx`  
**Base**: Radix UI Dialog with custom styling  

```typescript
<Dialog>
  <DialogTrigger asChild>
    <Button>Open Dialog</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Dialog Title</DialogTitle>
      <DialogDescription>Dialog description</DialogDescription>
    </DialogHeader>
    {/* Dialog content */}
  </DialogContent>
</Dialog>
```

## 📊 Dashboard Components (`src/components/dashboard/`)

### DashboardContent (`dashboard-content.tsx`)
**File**: `src/components/dashboard/dashboard-content.tsx`  
**Purpose**: Main dashboard router based on user role  

```typescript
export default function DashboardContent() {
  const { user, loading, error } = useCurrentUser();

  if (loading) return <DashboardSkeleton />;
  if (error) return <ErrorDisplay />;

  switch (user?.role) {
    case 'student': return <StudentDashboard user={user} />;
    case 'consultant': return <ConsultantDashboard user={user} />;
    case 'admin': return <AdminDashboard user={user} />;
    default: return <UnauthorizedAccess />;
  }
}
```

### StudentDashboard (`student-dashboard.tsx`)
**File**: `src/components/dashboard/student-dashboard.tsx`  
**Features**: Progress tracking, quick actions, advisor info  

```typescript
interface StudentDashboardProps {
  user: User;
}

// Key sections:
// - Welcome section with progress overview
// - Statistics cards (essays, applications, deadlines)
// - Quick actions (continue writing, view deadlines)
// - Advisor information and contact
// - Recent activity feed
```

### StatsCards Pattern
```typescript
// Reusable stats card pattern used throughout dashboards
<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">Stat Title</CardTitle>
      <Icon className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      <p className="text-xs text-muted-foreground">
        {change} from last month
      </p>
    </CardContent>
  </Card>
</div>
```

## 📄 Document Components (`src/components/documents/`)

### DocumentDashboard (`document-dashboard.tsx`)
**File**: `src/components/documents/document-dashboard.tsx`  
**Purpose**: Main document management interface  

```typescript
interface DocumentDashboardProps {
  studentId?: string; // For consultant/admin viewing specific student
}

// Key features:
// - Document statistics and analytics
// - Tabbed interface (Documents, Analytics, Activity)
// - Create document functionality
// - Integration with DocumentList component
```

### DocumentList (`document-list.tsx`)
**File**: `src/components/documents/document-list.tsx`  
**Purpose**: Filterable list of documents with actions  

```typescript
interface DocumentListProps {
  studentId?: string;
  showCreateButton?: boolean;
  onCreateDocument?: () => void;
}

// Features:
// - Search and filter functionality
// - Document type filtering
// - Status-based filtering
// - Bulk actions for consultants/admins
// - Google Docs integration links
```

### CreateDocumentForm (`create-document-form.tsx`)
**File**: `src/components/documents/create-document-form.tsx`  
**Purpose**: Document creation with template selection  

```typescript
interface CreateDocumentFormProps {
  studentId?: string;
  onSuccess: (document: Document) => void;
  onCancel: () => void;
}

// Features:
// - Document type selection
// - Template recommendation and selection
// - Metadata input (title, description, deadline)
// - Google Docs integration
// - Real-time validation
```

### GoogleDocsViewer (`google-docs-viewer.tsx`)
**File**: `src/components/documents/google-docs-viewer.tsx`  
**Purpose**: Embedded Google Docs viewer  

```typescript
interface GoogleDocsViewerProps {
  documentId: string;
  googleDocId?: string;
  editable?: boolean;
}

// Features:
// - Embedded Google Docs iframe
// - Permission-based editing controls
// - Loading states and error handling
// - Responsive design for mobile
```

## 🧭 Layout Components (`src/components/layout/`)

### AppSidebar (`app-sidebar.tsx`)
**File**: `src/components/layout/app-sidebar.tsx`  
**Purpose**: Role-based navigation sidebar  

```typescript
// Navigation structure from src/constants/data.ts
const navigation = {
  student: [
    { title: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
    { title: 'Essays', href: '/dashboard/essays', icon: FileText },
    { title: 'Academics', href: '/dashboard/academics', icon: GraduationCap },
    // ...
  ],
  consultant: [
    { title: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
    { title: 'Students', href: '/dashboard/students', icon: Users },
    { title: 'Documents', href: '/dashboard/documents', icon: FileText },
    // ...
  ],
  admin: [
    { title: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
    { title: 'User Management', href: '/dashboard/users', icon: Users },
    { title: 'Analytics', href: '/dashboard/analytics', icon: BarChart3 },
    // ...
  ]
};
```

### Header (`header.tsx`)
**File**: `src/components/layout/header.tsx`  
**Purpose**: Top navigation with user controls  

```typescript
// Header components:
// - SidebarTrigger (mobile menu)
// - Breadcrumbs navigation
// - SearchInput (global search)
// - UserNav (user dropdown)
// - ThemeToggle (dark/light mode)
// - ThemeSelector (color themes)
```

### PageContainer (`page-container.tsx`)
**File**: `src/components/layout/page-container.tsx`  
**Purpose**: Consistent page wrapper with scrolling  

```typescript
interface PageContainerProps {
  children: React.ReactNode;
  scrollable?: boolean;
}

// Usage:
<PageContainer scrollable={true}>
  {/* Page content */}
</PageContainer>
```

## 🎯 Custom Hooks (`src/hooks/`)

### useCurrentUser (`use-current-user.tsx`)
**File**: `src/hooks/use-current-user.tsx`  
**Purpose**: User authentication and role management  

```typescript
interface UseCurrentUserReturn {
  user: User | null;
  loading: boolean;
  error: string | null;
  retryCount: number;
  refetch: () => void;
}

// Usage:
const { user, loading, error, refetch } = useCurrentUser();

// User object includes:
// - Basic user info (id, email, role)
// - Profile data (student profile, consultant info)
// - Permissions and access levels
```

## 🔧 API Integration Hooks (`src/lib/api-client.ts`)

### useApiGet
```typescript
const { data, loading, error, fetch } = useApiGet<T>(endpoint, params);

// Example:
const { data: documents, loading, fetch: fetchDocuments } = 
  useApiGet<Document[]>('/api/documents');
```

### useApiPost
```typescript
const { execute, loading, error } = useApiPost<T>(endpoint);

// Example:
const { execute: createDocument, loading } = 
  useApiPost<Document>('/api/documents');
```

## 🎨 Styling Patterns

### Responsive Design
```typescript
// Mobile-first responsive classes
<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
<div className="flex flex-col gap-4 p-4 pt-6 md:p-8">
<div className="hidden md:flex">
```

### Loading States
```typescript
// Skeleton loading pattern
{loading ? (
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    {[...Array(4)].map((_, i) => (
      <Card key={i}>
        <CardHeader className="pb-2">
          <Skeleton className="h-4 w-20" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-16" />
        </CardContent>
      </Card>
    ))}
  </div>
) : (
  // Actual content
)}
```

### Error States
```typescript
// Error display pattern
{error && (
  <Alert variant="destructive">
    <AlertCircle className="h-4 w-4" />
    <AlertTitle>Error</AlertTitle>
    <AlertDescription>{error}</AlertDescription>
  </Alert>
)}
```

## 🔗 Component Dependencies

### Key Dependencies
- **Radix UI**: Base primitives for accessible components
- **Lucide React**: Icon library
- **class-variance-authority**: Component variant management
- **clsx + tailwind-merge**: Conditional class names
- **React Hook Form**: Form state management
- **Zod**: Schema validation

### Import Patterns
```typescript
// UI components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Custom components
import { DocumentList } from '@/components/documents/document-list';
import { useCurrentUser } from '@/hooks/use-current-user';

// Utilities
import { cn } from '@/lib/utils';
```

## 🔗 Cross-References

- **API Integration**: See `ai-assistant-docs/API_DOCUMENTATION.md`
- **Authentication**: See `ai-assistant-docs/ROLE_BASED_ACCESS.md`
- **File Structure**: See `ai-assistant-docs/CODEBASE_STRUCTURE.md`
- **Development Setup**: See `ai-assistant-docs/DEVELOPMENT_GUIDE.md`
