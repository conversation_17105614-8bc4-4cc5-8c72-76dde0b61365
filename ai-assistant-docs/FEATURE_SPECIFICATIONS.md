# Lighten Counsel - Feature Specifications

**Last Updated**: July 6, 2025
**Version**: 2.0.0
**Status**: Updated with User's 11 Specific Goals

## 🎯 User's 11 Core Requirements

This document reflects the specific goals provided by the user for the Lighten Counsel system. Each feature specification aligns with these requirements:

1. **Essay System**: Main essays (reusable) + Supplemental essays (school-specific with one-click copy)
2. **Mock Test Tracking**: Structured recording with consultant upload capability
3. **Academic Records**: Dual support (manual forms + document uploads)
4. **Activity Forms**: Common App structure with file uploads
5. **Activity Resume**: Unified with multiple file focuses
6. **Document Creation**: Free format with recommended guidelines
7. **Single Main Advisor**: One advisor per student with team additions
8. **Invitation-Based Registration**: Invitation codes and admin confirmation
9. **Admin-Assigned Advisors**: With student confirmation workflow
10. **Competition/Awards**: Integrated as "Honors" in activities
11. **Meeting System**: Scheduling, categorized notes, and next meeting display

## 🎯 Feature Overview by User Role

### Student Features (Role: `student`)

**Primary Users**: High school students applying to college
**Access Level**: Own data only
**Registration**: Invitation-based system with confirmation workflow

### Consultant Features (Role: `consultant`)

**Primary Users**: College application consultants and counselors
**Access Level**: Assigned students' data
**Assignment**: Single main advisor per student with possible team additions

### Administrator Features (Role: `admin`)

**Primary Users**: System administrators and Lighten staff
**Access Level**: Full system access
**Responsibilities**: User management, advisor assignment, invitation management

## 📚 Student Features

### 1. Dashboard (`/dashboard`)

**File**: `src/app/dashboard/page.tsx` → `src/components/dashboard/student-dashboard.tsx`
**Status**: 🔄 **Requires Enhancement for Goal #11**

**Features**:

- Welcome message with personalized greeting
- Application progress overview with statistics
- Quick action buttons (Continue Writing, View Deadlines)
- **NEW**: Next meeting display with advisor information
- **NEW**: Meeting request functionality
- Recent activity feed
- Upcoming deadlines widget

**Implementation Details**:

```typescript
// Enhanced dashboard stats with meeting info
interface StudentDashboardStats {
  essays: {
    main: number;
    supplemental: number;
    completed: number;
    in_progress: number;
  };
  applications: { total: number; submitted: number; pending: number };
  deadlines: { upcoming: number; overdue: number };
  advisor: {
    name: string;
    email: string;
    specialties: string[];
    profile_image?: string; // Goal #9: Build trust
    bio?: string;
  };
  next_meeting?: {
    date: string;
    type:
      | 'first_meetup'
      | 'follow_up'
      | 'essay_review'
      | 'application_strategy'
      | 'college_decision';
    duration: number;
  };
}
```

### 2. Essay Management (`/dashboard/essays`) - **REDESIGNED for Goal #1**

**File**: `src/app/dashboard/essays/page.tsx`
**Status**: 🔄 **Major Redesign Required**

**NEW Features (Goal #1)**:

- **Main Essays (主文书)**: Reusable across multiple schools
- **Supplemental Essays (补充文书)**: School-specific with one-click copy
- Essay categorization and cross-school management
- One-click copy functionality with modification tracking
- School-specific essay binding and requirements

**Implementation Details**:

```typescript
interface Essay {
  id: string;
  type: 'main' | 'supplemental';
  title: string;
  content: string;
  schools: string[]; // For main essays: multiple schools
  school_id?: string; // For supplemental: specific school
  source_essay_id?: string; // For copied essays
  word_limit: number;
  recommended_format: 'PDF' | 'Word' | 'Plain Text';
  status: 'draft' | 'in_review' | 'completed';
  google_doc_id?: string;
  created_at: string;
  updated_at: string;
}

// One-click copy functionality
interface EssayCopyRequest {
  source_essay_id: string;
  target_school_id: string;
  new_title: string;
  modifications_needed?: string;
}
```

### 3. Academic Records (`/dashboard/academics`) - **ENHANCED for Goals #2 & #3**

**File**: `src/app/dashboard/academics/page.tsx`
**Status**: 🔄 **Requires Enhancement**

**Enhanced Sub-features (Goals #2 & #3)**:

- **GPA Management** (`/dashboard/academics/gpa`): Manual form entry + transcript uploads
- **Official Test Scores** (`/dashboard/academics/test-scores`): SAT, ACT, subject tests
- **NEW: Mock Test Tracking** (`/dashboard/academics/mock-tests`): Goal #2 implementation
- **Transcripts** (`/dashboard/academics/transcripts`): Dual support - forms + uploads
- **AP Courses** (`/dashboard/academics/ap-courses`): JSON-based AP classes list

**NEW Mock Test System (Goal #2)**:

```typescript
interface MockTestScore {
  id: string;
  student_id: string;
  exam_type:
    | 'SAT'
    | 'ACT'
    | 'TOEFL'
    | 'IELTS'
    | 'AP'
    | 'Subject_Test'
    | 'Other';
  test_date: string;
  score: number;
  max_score: number;
  source:
    | 'practice_test'
    | 'tutor_feedback'
    | 'official_practice'
    | 'mock_exam'
    | 'prep_course';
  notes?: string;
  document_url?: string; // Scanned results uploaded by consultant
  created_by: string; // Student or consultant
  tutor_feedback?: string;
  improvement_areas?: string[];
}
```

**Enhanced Academic Info (Goal #3)**:

```typescript
interface AcademicInfo {
  // Manual form data (for calculations and visualization)
  gpa: {
    weighted: number;
    unweighted: number;
    scale: number;
    manual_entry: boolean;
  };
  test_scores: {
    sat?: number;
    act?: number;
    subject_tests: TestScore[];
    mock_tests: MockTestScore[]; // NEW
  };
  ap_courses: APCourse[]; // JSON-based list

  // Document uploads (for reference)
  transcripts: TranscriptFile[];
  grade_reports: DocumentFile[];
  test_score_reports: DocumentFile[];

  // Dual support flag
  data_entry_method: 'form_only' | 'upload_only' | 'both';
}
```

### 4. Activities Portfolio (`/dashboard/activities`) - **REDESIGNED for Goals #4, #5, #10**

**File**: `src/app/dashboard/activities/page.tsx`
**Status**: 🔄 **Major Redesign Required**

**NEW Common App Structure (Goal #4)**:

- **Structured Forms**: Activity type, position, duration, description (150 words max)
- **File Uploads**: Certificates, videos (YouTube links), photos
- **Competition/Awards Integration**: "Honors" category (Goal #10)
- **Multiple Resume Versions**: Different focuses for different majors (Goal #5)

**Implementation (Goals #4, #5, #10)**:

```typescript
interface Activity {
  id: string;
  student_id: string;
  // Common App categories
  activity_type:
    | 'academic'
    | 'art'
    | 'athletics'
    | 'community_service'
    | 'cultural'
    | 'debate'
    | 'journalism'
    | 'music'
    | 'religious'
    | 'research'
    | 'robotics'
    | 'student_government'
    | 'work'
    | 'honors'
    | 'other'; // 'honors' for Goal #10

  organization_name: string;
  position_title: string;
  description: string; // Max 150 words (Common App standard)
  participation_grade_levels: ('9' | '10' | '11' | '12' | 'Post-graduate')[];

  time_commitment: {
    hours_per_week: number;
    weeks_per_year: number;
  };

  leadership_role: boolean;
  awards_recognition?: string;

  // File uploads (Goal #4)
  file_uploads: {
    certificates: FileUpload[];
    videos: string[]; // YouTube links
    photos: FileUpload[];
    additional_documents: FileUpload[];
  };

  // For competitions/awards (Goal #10)
  competition_details?: {
    competition_name: string;
    award_level: 'school' | 'regional' | 'state' | 'national' | 'international';
    placement?: string;
    date_received: string;
  };
}

// Multiple resume versions (Goal #5)
interface ActivityResume {
  id: string;
  student_id: string;
  title: string; // e.g., "Engineering Focus", "Liberal Arts Focus"
  description: string;
  selected_activities: string[]; // Activity IDs
  focus_area: string; // Major/field focus
  file_url?: string; // Generated resume file
  created_at: string;
  updated_at: string;
}
```

### 5. Target Schools (`/dashboard/schools`)

**File**: `src/app/dashboard/schools/page.tsx`  
**Status**: ✅ **Fully Implemented**

**Features**:

- School search and selection
- Application deadline tracking
- Requirements checklist per school
- Application status monitoring
- School comparison tools
- Admission statistics and insights

### 6. Document Collaboration (`/dashboard/documents`) - **ENHANCED for Goal #6**

**File**: `src/app/dashboard/documents/page.tsx`
**Status**: 🔄 **Requires Enhancement**

**Enhanced Features (Goal #6)**:

- **Free Document Creation**: Not restricted to templates
- **Format Recommendations**: Guidance (not restrictions) - PDF/Word for essays
- **Word Limit Reminders**: Suggestions, not enforcement (e.g., "Main essay: 650 words recommended")
- Google Docs integration for real-time editing
- Document sharing with consultants
- Version history and collaboration tracking
- Template library as optional starting points

**Implementation (Goal #6)**:

```typescript
interface DocumentCreationOptions {
  type:
    | 'essay'
    | 'personal_statement'
    | 'activity_resume'
    | 'transcript'
    | 'other';
  creation_method: 'free_form' | 'template_based' | 'blank_document';

  // Recommendations (not restrictions)
  recommendations: {
    word_limit?: number;
    suggested_format: ('PDF' | 'Word' | 'Google_Docs')[];
    guidelines?: string;
    examples?: string[];
  };

  // Template is optional
  template_id?: string;
  custom_title: string;
  custom_content?: string;
}
```

### 7. **NEW: Meeting System (`/dashboard/meetings`) - Goal #11**

**File**: `src/app/dashboard/meetings/page.tsx`
**Status**: ❌ **Not Implemented - Critical Missing Feature**

**Complete Meeting System (Goal #11)**:

- **Next Meeting Display**: Prominent display on dashboard
- **Meeting Types**: First-time Meetup, Follow-up, Essay Review, Application Strategy, College Decision
- **Scheduling**: Admin/consultant sets meetings, student can request
- **Meeting Notes**: Post-meeting notes by consultant/assistant
- **Calendar Integration**: Meeting reminders and scheduling

**Implementation (Goal #11)**:

```typescript
interface Meeting {
  id: string;
  student_id: string;
  consultant_id: string;
  meeting_type:
    | 'first_meetup'
    | 'follow_up'
    | 'essay_review'
    | 'application_strategy'
    | 'college_decision';

  scheduled_at: string;
  duration_minutes: number;
  meeting_link?: string; // Zoom/Google Meet
  location?: string; // For in-person meetings

  status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';

  // Meeting content
  agenda?: string;
  meeting_notes?: string;
  action_items?: string[];
  next_meeting_suggested?: string;

  // Workflow tracking
  created_by: string; // Who scheduled it
  requested_by?: string; // If student requested
  confirmed_by_student?: boolean; // Student confirmation

  // Files and resources
  shared_documents?: string[];
  recording_url?: string;

  created_at: string;
  updated_at: string;
}

interface MeetingRequest {
  id: string;
  student_id: string;
  consultant_id: string;
  requested_meeting_type: string;
  preferred_dates: string[];
  message?: string;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
}
```

## 👥 Consultant Features - **ENHANCED for Goals #7, #9, #11**

### 1. Consultant Dashboard (`/dashboard`)

**File**: `src/components/dashboard/consultant-dashboard.tsx`
**Status**: 🔄 **Requires Enhancement**

**Enhanced Features (Goals #7, #9, #11)**:

- **Single Main Advisor Model**: Primary advisor per student (Goal #7)
- **Team Collaboration**: Support for essay teachers, planning assistants
- **Student Confirmation Workflow**: Pending assignments requiring student acceptance (Goal #9)
- **Meeting Management**: Schedule meetings, view upcoming meetings (Goal #11)
- Multi-student overview with progress tracking
- Review queue for documents requiring attention
- Performance metrics and analytics

**Implementation (Goals #7, #9)**:

```typescript
interface ConsultantDashboardData {
  // Single advisor model (Goal #7)
  primary_students: StudentAssignment[];
  team_collaborations: TeamCollaboration[];

  // Student confirmation workflow (Goal #9)
  pending_assignments: {
    student_id: string;
    student_name: string;
    student_email: string;
    assigned_at: string;
    status: 'pending_student_confirmation';
    student_profile_preview: {
      grade_level: string;
      target_schools: string[];
      interests: string[];
    };
  }[];

  // Meeting system (Goal #11)
  upcoming_meetings: Meeting[];
  meeting_requests: MeetingRequest[];

  // Existing features
  review_queue: Document[];
  performance_metrics: ConsultantMetrics;
}

interface StudentAssignment {
  student_id: string;
  assignment_type: 'primary_advisor' | 'essay_teacher' | 'planning_assistant';
  status: 'pending' | 'confirmed' | 'active';
  assigned_at: string;
  confirmed_at?: string;
}
```

### 2. Student Management (`/dashboard/students`)

**File**: `src/app/dashboard/students/page.tsx`  
**Status**: ✅ **Fully Implemented**

**Features**:

- Student list with search and filtering
- Individual student profile access
- Progress tracking across all assigned students
- Communication tools and notes
- Assignment and reassignment capabilities
- Bulk operations for document management

**Implementation**:

```typescript
// Consultant can access assigned students
const { data: students } = useApiGet<Student[]>('/api/students', {
  consultant_id: user.id // Automatically filtered by backend
});
```

### 3. Document Review System

**File**: Integrated across document components  
**Status**: ✅ **Fully Implemented**

**Features**:

- Document review queue with priority sorting
- Collaborative editing via Google Docs
- Comment and feedback system
- Approval workflow management
- Progress tracking and deadline monitoring
- Template recommendation for students

### 4. Analytics and Reporting

**File**: `src/app/dashboard/analytics/page.tsx` (consultant view)  
**Status**: ✅ **Fully Implemented**

**Features**:

- Student progress analytics
- Document collaboration metrics
- Deadline tracking and alerts
- Performance insights and trends
- Custom reporting capabilities

## 🔧 Administrator Features - **ENHANCED for Goals #7, #8, #9**

### 1. Admin Dashboard (`/dashboard`)

**File**: `src/components/dashboard/admin-dashboard.tsx`
**Status**: 🔄 **Requires Enhancement**

**Enhanced Features (Goals #7, #8, #9)**:

- **Invitation Management**: Create and manage invitation codes (Goal #8)
- **Advisor Assignment Workflow**: Assign advisors with student confirmation tracking (Goal #9)
- **Single Advisor Enforcement**: Ensure one primary advisor per student (Goal #7)
- **Team Management**: Manage essay teachers and planning assistants
- System-wide statistics and health monitoring
- User management overview
- Document ownership and permission management

**Implementation (Goals #7, #8, #9)**:

```typescript
interface AdminDashboardData {
  // Invitation system (Goal #8)
  invitation_stats: {
    active_invitations: number;
    pending_registrations: number;
    recent_signups: UserRegistration[];
  };

  // Advisor assignment workflow (Goal #9)
  assignment_workflow: {
    pending_confirmations: StudentAssignment[];
    recent_assignments: StudentAssignment[];
    advisor_capacity: AdvisorCapacity[];
  };

  // Single advisor enforcement (Goal #7)
  advisor_violations: {
    students_without_advisor: Student[];
    students_with_multiple_advisors: Student[];
  };

  // System health
  system_stats: SystemMetrics;
  user_management_overview: UserStats;
}

interface UserRegistration {
  id: string;
  email: string;
  invitation_code: string;
  registered_at: string;
  status: 'pending_admin_approval' | 'approved' | 'rejected';
  role_requested: 'student' | 'consultant';
}
```

### 2. **Invitation Management (`/dashboard/invitations`) - Goal #8**

**File**: `src/app/dashboard/invitations/page.tsx`
**Status**: ✅ **Fully Implemented**

**Complete Invitation System (Goal #8)**:

- **Invitation Code Generation**: Create codes for students/consultants
- **Email-Specific Invitations**: Send invitations to specific email addresses
- **Registration Approval Workflow**: Admin approval for new registrations
- **Invitation Tracking**: Monitor usage, expiration, and success rates

**Implementation (Goal #8)**:

```typescript
interface Invitation {
  id: string;
  code: string;
  email?: string; // For specific invitations
  role: 'student' | 'consultant';
  created_by: string; // Admin user ID
  expires_at: string;
  used_at?: string;
  used_by?: string;
  status: 'pending' | 'used' | 'expired';
  max_uses?: number; // For general invitation codes
  current_uses: number;
  notes?: string;
}

interface RegistrationRequest {
  id: string;
  email: string;
  invitation_code: string;
  role_requested: 'student' | 'consultant';
  profile_data: any;
  status: 'pending_approval' | 'approved' | 'rejected';
  admin_notes?: string;
  created_at: string;
}
```

### 3. User Management (`/dashboard/users`) - **ENHANCED for Goals #7, #9**

**File**: `src/app/dashboard/users/page.tsx`
**Status**: 🔄 **Requires Enhancement**

**Enhanced Features (Goals #7, #9)**:

- **Advisor Assignment Interface**: Assign primary advisors with student confirmation
- **Single Advisor Enforcement**: Prevent multiple primary advisor assignments
- **Team Member Management**: Add essay teachers, planning assistants
- **Student Confirmation Tracking**: Monitor pending advisor assignments
- Complete user directory (students, consultants, admins)
- User role management and permissions
- Account creation and deactivation

**Implementation (Goals #7, #9)**:

```typescript
interface AdvisorAssignmentRequest {
  student_id: string;
  consultant_id: string;
  assignment_type: 'primary_advisor' | 'essay_teacher' | 'planning_assistant';
  message_to_student?: string;
  admin_notes?: string;
}

interface StudentConfirmationWorkflow {
  assignment_id: string;
  student_id: string;
  consultant_id: string;
  status: 'pending_student_confirmation' | 'confirmed' | 'rejected';
  student_response?: string;
  confirmed_at?: string;
  rejection_reason?: string;
}
```

### 3. Document Ownership Management

**File**: `src/lib/permission-manager.ts`  
**Status**: ✅ **Fully Implemented**

**Features**:

- Centralized document ownership (Lighten admin accounts)
- Permission auditing and repair tools
- Bulk permission management
- Document access control
- Consultant assignment automation
- Permission troubleshooting tools

### 4. Template Management

**File**: `src/app/api/templates/route.ts`  
**Status**: ✅ **Fully Implemented**

**Features**:

- Template creation and organization
- Template usage analytics
- Category and tag management
- Featured template selection
- Template recommendation engine
- Usage tracking and optimization

### 5. System Analytics (`/dashboard/analytics`)

**File**: `src/app/dashboard/analytics/page.tsx`  
**Status**: ✅ **Fully Implemented**

**Features**:

- Platform usage statistics
- User engagement metrics
- Document collaboration analytics
- Google Docs integration health
- Performance monitoring
- Custom report generation

### 6. School Database Management

**File**: Integrated in admin tools  
**Status**: ✅ **Fully Implemented**

**Features**:

- College and university database maintenance
- Application requirement updates
- Deadline management
- Admission statistics tracking
- School information accuracy

## 🔗 Cross-Feature Integration

### Google Docs Integration

**Files**: `src/lib/google-apis.ts`, `src/lib/document-management.ts`  
**Status**: ✅ **Fully Implemented**

**Features**:

- Automatic document creation in Google Docs
- Real-time collaboration between students and consultants
- Proper folder organization by student and document type
- Permission management with Lighten admin ownership
- Template copying and customization
- Document sharing and access control

### Authentication and Authorization

**Files**: `src/middleware.ts`, `src/hooks/use-current-user.tsx`  
**Status**: ✅ **Fully Implemented**

**Features**:

- Clerk-based authentication with role management
- Role-based access control (RBAC) throughout the application
- Automatic user creation via webhooks
- Session management and security
- Permission inheritance and delegation

### Database Integration

**Files**: `database/schema.sql`, `src/lib/supabase.ts`  
**Status**: ✅ **Fully Implemented**

**Features**:

- Row Level Security (RLS) for data isolation
- Real-time data synchronization
- Comprehensive audit trails
- Data validation and integrity
- Backup and recovery capabilities

## 📊 Feature Implementation Status - **UPDATED for User's 11 Goals**

### ❌ Critical Missing Features (Must Implement)

- **Meeting System (Goal #11)**: Complete meeting management system
- ✅ **Invitation System (Goal #8)**: **COMPLETED** - Registration control and invitation codes
- ✅ **Admin-Assigned Advisors (Goal #9)**: **COMPLETED** - Direct advisor assignment workflow
- **Essay System Redesign (Goal #1)**: Main vs supplemental essay distinction
- **Mock Test Tracking (Goal #2)**: Structured test score recording

### 🔄 Major Enhancements Required

- **Activity Forms (Goals #4, #5, #10)**: Common App structure with file uploads
- **Student Confirmation Workflow (Goal #9)**: Advisor assignment confirmation
- **Academic Records Enhancement (Goals #2, #3)**: Dual support and mock tests
- **Document Creation Flexibility (Goal #6)**: Free-form creation with guidance

### ✅ Partially Implemented (Needs Enhancement)

- **Authentication System**: Exists but needs invitation system integration
- **Student Dashboard**: Exists but needs meeting display and advisor info
- **Document Management**: Exists but needs free-form creation
- **User Management**: Exists but needs advisor assignment workflow
- **Database Schema**: Exists but needs new tables for meetings, invitations

### 📋 Implementation Priority Order

#### Phase 1: Critical Systems (Goals #8, #11, #1)

1. **Invitation Management System** - Security and access control
2. **Meeting System** - Core consultant-student interaction
3. **Essay System Redesign** - Main vs supplemental essays

#### Phase 2: Enhanced Features (Goals #2, #9, #7)

4. **Mock Test Tracking** - Academic progress monitoring
5. **Student Confirmation Workflow** - Advisor assignment process
6. **Single Advisor Enforcement** - Business logic implementation

#### Phase 3: User Experience (Goals #4, #5, #6, #10)

7. **Activity Forms Enhancement** - Common App compliance
8. **Document Creation Flexibility** - User-friendly creation
9. **Multiple Resume Versions** - Different focus areas
10. **Competition Integration** - Awards and honors tracking

#### Phase 4: Data Management (Goal #3)

11. **Academic Records Dual Support** - Forms + uploads integration

### 📋 Feature Requirements

#### Security Requirements

- All features require proper authentication
- Role-based access control enforced at API and UI levels
- Data encryption in transit and at rest
- Audit logging for administrative actions
- Secure Google Workspace integration

#### Performance Requirements

- Page load times under 2 seconds
- API response times under 500ms
- Real-time collaboration via Google Docs
- Responsive design for mobile devices
- Offline capability for document viewing

#### Compliance Requirements

- FERPA compliance for educational records
- GDPR compliance for data protection
- SOC 2 compliance for security standards
- Regular security audits and updates

## 🔗 Cross-References

- **API Implementation**: See `ai-assistant-docs/API_DOCUMENTATION.md`
- **Component Details**: See `ai-assistant-docs/COMPONENT_LIBRARY.md`
- **Authentication**: See `ai-assistant-docs/ROLE_BASED_ACCESS.md`
- **Development Setup**: See `ai-assistant-docs/DEVELOPMENT_GUIDE.md`
- **Troubleshooting**: See `ai-assistant-docs/TROUBLESHOOTING.md`
