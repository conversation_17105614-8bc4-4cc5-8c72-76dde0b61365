# Lighten Counsel - Troubleshooting Guide

**Last Updated**: July 6, 2025  
**Version**: 1.0.10  
**Status**: Production Ready with Known Issues Documented  

## 🚨 Known Issues and Solutions

### Critical Issues (Resolved)

#### ✅ Google Docs Integration 403 Forbidden Error
**Status**: **RESOLVED** (July 4, 2025)  
**Issue**: Document creation failing with 403 Forbidden errors  
**Root Cause**: Domain-wide delegation configuration  
**Solution**: Updated Google Workspace admin settings and service account permissions  

**Files Involved**:
- `src/lib/google-apis.ts` - Authentication fallback implementation
- `.env.local` - Service account configuration
- Google Admin Console - Domain-wide delegation settings

**Resolution Details**: See `GOOGLE_DOCS_FINAL_SOLUTION.md` for complete solution

#### ✅ Database RLS Infinite Recursion
**Status**: **RESOLVED**  
**Issue**: Row Level Security policies causing infinite recursion  
**Solution**: Simplified RLS policies and proper service role usage  

### Minor Issues (Non-blocking)

#### ⚠️ Admin Navigation Inconsistency
**Status**: **MINOR** - Does not block production  
**Issue**: Admin navigation occasionally shows student menu items  
**Impact**: Cosmetic only, functionality remains intact  
**Workaround**: Refresh page or navigate directly to admin URLs  

**Files to Check**:
- `src/hooks/use-current-user.tsx` - User role state management
- `src/constants/data.ts` - Navigation configuration
- `src/components/layout/app-sidebar.tsx` - Sidebar rendering

#### ⚠️ ESLint Warnings
**Status**: **CODE QUALITY** - Does not affect functionality  
**Issue**: Console statements and unused imports  
**Impact**: Development experience only  

**Common Warnings**:
```bash
# Console statements (for debugging)
console.log statements in development files

# Unused imports
import statements that are not used

# React hook dependencies
useEffect dependency array warnings
```

**Resolution**:
```bash
# Fix automatically
pnpm lint:fix

# Manual cleanup
# Remove console.log statements
# Remove unused imports
# Add missing dependencies to useEffect
```

## 🔧 Common Development Issues

### Authentication Problems

#### Issue: "Authentication required" errors
**Symptoms**: API calls returning 401 errors  
**Causes**:
1. Missing or invalid Clerk session
2. Incorrect environment variables
3. User not found in database

**Debugging Steps**:
```typescript
// Check authentication status
const { user, loading, error } = useCurrentUser();
console.log('Auth state:', { user, loading, error });

// Test authentication endpoint
fetch('/api/test-auth')
  .then(res => res.json())
  .then(data => console.log('Auth test:', data));
```

**Solutions**:
1. **Clear browser cache and cookies**
2. **Check environment variables**:
   ```bash
   # Verify Clerk keys are set
   echo $NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
   echo $CLERK_SECRET_KEY
   ```
3. **Create user in database**:
   ```bash
   # Use test endpoint to create user
   curl -X POST http://localhost:3000/api/test-auth
   ```

#### Issue: Role-based access not working
**Symptoms**: Users seeing wrong navigation or getting permission errors  
**Debugging**:
```typescript
// Check user role in database
SELECT u.email, u.role, sp.id as student_profile_id 
FROM users u 
LEFT JOIN student_profiles sp ON u.id = sp.user_id 
WHERE u.email = '<EMAIL>';
```

**Solutions**:
1. **Verify role assignment in database**
2. **Check RLS policies are enabled**
3. **Clear user session and re-authenticate**

### Google Docs Integration Issues

#### Issue: Document creation fails
**Symptoms**: "Failed to create document" errors  
**Common Causes**:
1. Invalid Google service account credentials
2. Missing domain-wide delegation
3. Incorrect folder permissions

**Debugging Steps**:
```typescript
// Test Google APIs connection
// Check browser console for detailed error messages
// Verify service account email and private key format
```

**Solutions**:
1. **Verify service account setup**:
   ```bash
   # Check environment variables
   echo $GOOGLE_CLIENT_EMAIL
   echo $GOOGLE_PRIVATE_KEY | head -c 50  # First 50 chars
   ```

2. **Test domain-wide delegation**:
   - Go to Google Admin Console
   - Security > API Controls > Domain-wide delegation
   - Verify service account is listed with correct scopes

3. **Check folder permissions**:
   - Verify `GOOGLE_PARENT_FOLDER_ID` exists
   - Ensure service account has access to folder

#### Issue: Document sharing fails
**Symptoms**: Users can't access shared documents  
**Solutions**:
1. **Check email addresses are correct**
2. **Verify Google Workspace domain settings**
3. **Test with direct Google Drive sharing**

### Database Connection Issues

#### Issue: Supabase connection errors
**Symptoms**: Database queries failing, connection timeouts  
**Debugging**:
```typescript
// Test database connection
import { supabase } from '@/lib/supabase';

const testConnection = async () => {
  const { data, error } = await supabase
    .from('users')
    .select('count')
    .limit(1);
  
  console.log('DB test:', { data, error });
};
```

**Solutions**:
1. **Check environment variables**:
   ```bash
   echo $NEXT_PUBLIC_SUPABASE_URL
   echo $NEXT_PUBLIC_SUPABASE_ANON_KEY
   ```
2. **Verify Supabase project status**
3. **Check RLS policies are not blocking queries**

### Build and Deployment Issues

#### Issue: Build failures
**Common Errors**:
```bash
# TypeScript errors
Type 'X' is not assignable to type 'Y'

# Missing dependencies
Module not found: Can't resolve 'package-name'

# Environment variable issues
ReferenceError: process is not defined
```

**Solutions**:
1. **Fix TypeScript errors**:
   ```bash
   # Check types
   pnpm build
   
   # Fix common issues
   # - Add proper type annotations
   # - Import missing types
   # - Fix interface mismatches
   ```

2. **Install missing dependencies**:
   ```bash
   pnpm install
   ```

3. **Environment variables**:
   ```bash
   # Ensure all required variables are set
   # Use NEXT_PUBLIC_ prefix for client-side variables
   ```

## 🔍 Debugging Tools and Techniques

### Development Debugging

#### API Debugging
```typescript
// Enable detailed API logging
console.log('API Request:', { method, url, body });
console.log('API Response:', { status, data, error });

// Use test endpoints
GET /api/test-auth  # Test authentication
POST /api/test-auth # Create user if missing
```

#### Database Debugging
```sql
-- Check user and role data
SELECT u.email, u.role, u.created_at,
       sp.id as student_profile_id,
       c.id as consultant_id
FROM users u
LEFT JOIN student_profiles sp ON u.id = sp.user_id
LEFT JOIN consultants c ON u.id = c.user_id;

-- Check document permissions
SELECT d.id, d.metadata->>'title' as title,
       u.email as student_email,
       d.google_doc_id
FROM documents d
JOIN student_profiles sp ON d.student_id = sp.id
JOIN users u ON sp.user_id = u.id;

-- Check consultant assignments
SELECT u1.email as student_email,
       u2.email as consultant_email,
       scr.status,
       scr.assigned_at
FROM student_consultant_relations scr
JOIN student_profiles sp ON scr.student_id = sp.id
JOIN users u1 ON sp.user_id = u1.id
JOIN consultants c ON scr.consultant_id = c.id
JOIN users u2 ON c.user_id = u2.id;
```

#### Component Debugging
```typescript
// Debug component state
const DebugComponent = ({ data }) => {
  console.log('Component render:', { data, timestamp: new Date() });
  
  useEffect(() => {
    console.log('Component mounted/updated:', data);
  }, [data]);
  
  return <div>{/* component content */}</div>;
};
```

### Production Debugging

#### Error Monitoring
```typescript
// Add error boundaries
class ErrorBoundary extends React.Component {
  componentDidCatch(error, errorInfo) {
    console.error('Component error:', error, errorInfo);
    // Send to error tracking service
  }
}
```

#### Performance Monitoring
```typescript
// Monitor API performance
const startTime = performance.now();
const response = await fetch('/api/endpoint');
const endTime = performance.now();
console.log(`API call took ${endTime - startTime} milliseconds`);
```

## 📋 Troubleshooting Checklist

### Before Reporting Issues
- [ ] Check browser console for error messages
- [ ] Verify environment variables are set correctly
- [ ] Test with different user roles
- [ ] Clear browser cache and cookies
- [ ] Check network connectivity
- [ ] Verify database connection
- [ ] Test API endpoints directly

### Information to Gather
1. **Error Messages**: Exact error text and stack traces
2. **User Context**: Role, email, browser, device
3. **Steps to Reproduce**: Detailed reproduction steps
4. **Environment**: Development vs production
5. **Recent Changes**: Any recent code or config changes

### Quick Fixes

#### Clear Application State
```bash
# Clear browser data
# - Cookies
# - Local storage
# - Session storage
# - Cache

# Restart development server
pnpm dev
```

#### Reset Database Connection
```typescript
// Recreate Supabase client
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);
```

#### Refresh Authentication
```typescript
// Force user data refresh
const { refetch } = useCurrentUser();
refetch();

// Or clear Clerk session
import { useClerk } from '@clerk/nextjs';
const { signOut } = useClerk();
await signOut();
```

## 🆘 Getting Help

### Internal Resources
1. **Documentation**: Check other AI assistant docs
2. **Code Comments**: Look for inline documentation
3. **Git History**: Check recent commits for context
4. **Test Files**: Review test cases for expected behavior

### External Resources
1. **Next.js Documentation**: https://nextjs.org/docs
2. **Clerk Documentation**: https://clerk.com/docs
3. **Supabase Documentation**: https://supabase.com/docs
4. **Google APIs Documentation**: https://developers.google.com/docs

### Support Channels
1. **GitHub Issues**: For bug reports and feature requests
2. **Development Team**: For urgent production issues
3. **Community Forums**: For general questions and discussions

## 🔗 Cross-References

- **API Issues**: See `ai-assistant-docs/API_DOCUMENTATION.md`
- **Authentication Problems**: See `ai-assistant-docs/ROLE_BASED_ACCESS.md`
- **Component Issues**: See `ai-assistant-docs/COMPONENT_LIBRARY.md`
- **Development Setup**: See `ai-assistant-docs/DEVELOPMENT_GUIDE.md`
- **Feature Problems**: See `ai-assistant-docs/FEATURE_SPECIFICATIONS.md`
