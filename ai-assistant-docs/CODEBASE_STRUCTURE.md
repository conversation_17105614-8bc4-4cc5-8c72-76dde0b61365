# Lighten Counsel - Codebase Structure

**Last Updated**: July 6, 2025  
**Purpose**: Detailed file/folder organization and component architecture for AI assistants  

## 📁 Directory Structure

### Root Level
```
lighten-counsel/
├── src/                    # Main source code
├── database/              # Database schema and migrations
├── docs/                  # Project documentation
├── ai-assistant-docs/     # AI assistant-specific documentation
├── tests/                 # Test files and scripts
├── public/                # Static assets
├── package.json           # Dependencies and scripts
├── next.config.js         # Next.js configuration
├── tailwind.config.js     # Tailwind CSS configuration
├── components.json        # shadcn/ui configuration
└── .env.local            # Environment variables (not in repo)
```

### Source Code Organization (`src/`)

#### App Router Structure (`src/app/`)
```
src/app/
├── layout.tsx                 # Root layout with providers
├── page.tsx                   # Landing page (redirects to dashboard)
├── globals.css               # Global styles and CSS variables
├── theme.css                 # Theme-specific styles
├── dashboard/                # Protected dashboard area
│   ├── layout.tsx           # Dashboard layout with sidebar
│   ├── page.tsx             # Role-based dashboard routing
│   ├── essays/              # Essay management (students)
│   │   ├── page.tsx         # Essays list and management
│   │   └── [id]/            # Individual essay pages
│   ├── academics/           # Academic records (students)
│   │   ├── page.tsx         # Academic overview
│   │   ├── gpa/             # GPA management
│   │   ├── test-scores/     # SAT/ACT scores
│   │   ├── transcripts/     # Transcript management
│   │   └── ap-courses/      # AP course tracking
│   ├── activities/          # Extracurricular activities
│   │   └── page.tsx         # Activities management
│   ├── schools/             # Target schools (students)
│   │   └── page.tsx         # School list and applications
│   ├── documents/           # Document collaboration hub
│   │   ├── page.tsx         # Document dashboard
│   │   └── [id]/            # Individual document pages
│   ├── students/            # Student management (consultants)
│   │   ├── page.tsx         # Student list
│   │   └── [id]/            # Individual student profiles
│   ├── users/               # User management (admin)
│   │   └── page.tsx         # User administration
│   └── analytics/           # System analytics (admin)
│       └── page.tsx         # Analytics dashboard
├── api/                      # API routes
│   ├── auth/                # Authentication endpoints
│   │   ├── me/route.ts      # Current user info
│   │   └── session/route.ts # Session management
│   ├── documents/           # Document management API
│   │   ├── route.ts         # List/create documents
│   │   ├── [id]/            # Individual document operations
│   │   │   ├── route.ts     # Get/update/delete document
│   │   │   └── google-doc/  # Google Docs integration
│   │   │       └── route.ts # Google Docs content API
│   │   └── stats/route.ts   # Document statistics
│   ├── students/            # Student profile API
│   │   ├── route.ts         # List/create students
│   │   ├── [id]/route.ts    # Individual student operations
│   │   └── target-schools/route.ts # Target schools API
│   ├── templates/           # Template management
│   │   ├── route.ts         # List/create templates
│   │   └── [id]/route.ts    # Individual template operations
│   ├── users/               # User management API
│   │   ├── route.ts         # List/create users
│   │   └── [id]/route.ts    # Individual user operations
│   ├── stats/               # Statistics and analytics
│   │   └── dashboard/route.ts # Dashboard statistics
│   ├── test-auth/route.ts   # Authentication testing endpoint
│   └── webhooks/            # External service webhooks
│       ├── clerk/route.ts   # Clerk user events
│       └── google-docs/route.ts # Google Docs events
└── auth/                    # Authentication pages
    ├── sign-in/             # Sign-in page
    └── sign-up/             # Sign-up page
```

#### Components Architecture (`src/components/`)
```
src/components/
├── dashboard/               # Dashboard-specific components
│   ├── dashboard-content.tsx    # Main dashboard router
│   ├── student-dashboard.tsx    # Student dashboard layout
│   ├── consultant-dashboard.tsx # Consultant dashboard layout
│   ├── admin-dashboard.tsx      # Admin dashboard layout
│   └── stats-cards.tsx          # Reusable statistics cards
├── documents/               # Document management components
│   ├── document-dashboard.tsx   # Main document dashboard
│   ├── document-list.tsx        # Document listing with filters
│   ├── create-document-form.tsx # Document creation form
│   ├── google-docs-viewer.tsx   # Embedded Google Docs viewer
│   └── document-detail.tsx      # Document detail view
├── layout/                  # Layout and navigation components
│   ├── app-sidebar.tsx          # Main application sidebar
│   ├── header.tsx               # Top navigation header
│   ├── page-container.tsx       # Page wrapper component
│   ├── providers.tsx            # React context providers
│   ├── user-nav.tsx             # User navigation dropdown
│   ├── breadcrumbs.tsx          # Breadcrumb navigation
│   ├── search-input.tsx         # Global search component
│   ├── cta-github.tsx           # GitHub CTA button
│   └── ThemeToggle/             # Theme switching components
│       ├── theme-provider.tsx   # Theme context provider
│       ├── theme-toggle.tsx     # Theme toggle button
│       └── theme-selector.tsx   # Theme selection dropdown
├── ui/                      # Base UI components (shadcn/ui)
│   ├── button.tsx               # Button component with variants
│   ├── card.tsx                 # Card layout component
│   ├── input.tsx                # Input field component
│   ├── dialog.tsx               # Modal dialog component
│   ├── dropdown-menu.tsx        # Dropdown menu component
│   ├── tabs.tsx                 # Tab navigation component
│   ├── badge.tsx                # Badge/tag component
│   ├── progress.tsx             # Progress bar component
│   ├── skeleton.tsx             # Loading skeleton component
│   ├── sonner.tsx               # Toast notification component
│   ├── sidebar.tsx              # Sidebar layout component
│   ├── command.tsx              # Command palette component
│   ├── scroll-area.tsx          # Scrollable area component
│   └── [other-ui-components]    # Additional shadcn/ui components
└── kbar.tsx                 # Command palette integration
```

#### Library and Utilities (`src/lib/`)
```
src/lib/
├── google-apis.ts           # Google Workspace integration
│   ├── GoogleDocsService    # Document creation and management
│   ├── GoogleDriveService   # File and folder operations
│   └── DocumentPermissionService # Permission management
├── document-management.ts   # Document CRUD operations
│   └── DocumentManagementService # Main document service
├── template-service.ts      # Template recommendations and tracking
│   └── TemplateService      # Template management service
├── permission-manager.ts    # Advanced permission management
│   └── AdvancedPermissionManager # Bulk permission operations
├── api-utils.ts            # API utilities and middleware
│   ├── withAuth             # Authentication middleware
│   ├── withStandardAuth     # Standard auth wrapper
│   ├── withStandardRole     # Role-based auth wrapper
│   └── Error classes        # Custom error types
├── api-client.ts           # Frontend API client
│   ├── useApiGet            # GET request hook
│   ├── useApiPost           # POST request hook
│   └── useApiPut            # PUT request hook
├── supabase.ts             # Database client configuration
│   ├── supabase             # Client-side Supabase client
│   └── supabaseAdmin        # Server-side admin client
├── utils.ts                # General utility functions
│   └── cn                   # Class name utility (clsx + tailwind-merge)
├── font.ts                 # Font configuration
└── validations.ts          # Zod validation schemas
```

#### Types and Constants (`src/types/` & `src/constants/`)
```
src/types/
├── application.ts           # Core application types
│   ├── User                 # User interface
│   ├── StudentProfile       # Student profile interface
│   ├── Document             # Document interface
│   ├── DocumentMetadata     # Document metadata interface
│   └── TargetSchool         # Target school interface
└── [other-type-files]       # Additional type definitions

src/constants/
├── data.ts                  # Role-based navigation data
│   ├── studentNavigation    # Student sidebar navigation
│   ├── consultantNavigation # Consultant sidebar navigation
│   └── adminNavigation      # Admin sidebar navigation
└── [other-constants]        # Additional constants
```

#### Hooks (`src/hooks/`)
```
src/hooks/
├── use-current-user.tsx     # User role and session management
│   ├── useCurrentUser       # Main user hook
│   └── User context         # User context provider
└── [other-hooks]            # Additional custom hooks
```

## 🏗️ Component Architecture Patterns

### Layout Pattern
```typescript
// Standard layout pattern used throughout the app
<div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
  <div className="flex items-center justify-between">
    <h1 className="text-3xl font-bold tracking-tight">Page Title</h1>
    <Button>Action Button</Button>
  </div>
  
  {/* Stats cards grid */}
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    {/* Stats cards */}
  </div>
  
  {/* Main content */}
  <div className="space-y-4">
    {/* Page content */}
  </div>
</div>
```

### API Route Pattern
```typescript
// Standard API route structure (src/app/api/*/route.ts)
import { withStandardAuth } from '@/lib/api-utils';

export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // Implementation
    }, 'Error message');
  }
);
```

### Component Naming Conventions
- **Pages**: `page.tsx` (Next.js App Router convention)
- **Components**: `kebab-case.tsx` (e.g., `document-dashboard.tsx`)
- **UI Components**: `kebab-case.tsx` (e.g., `button.tsx`)
- **Hooks**: `use-hook-name.tsx` (e.g., `use-current-user.tsx`)
- **Services**: `service-name.ts` (e.g., `document-management.ts`)
- **Types**: `PascalCase` interfaces (e.g., `User`, `Document`)

## 🔗 Key File References

### Critical Files for AI Assistants
- **Authentication**: `src/middleware.ts`, `src/hooks/use-current-user.tsx`
- **Database**: `database/schema.sql`, `src/lib/supabase.ts`
- **Google Integration**: `src/lib/google-apis.ts`
- **API Layer**: `src/lib/api-utils.ts`
- **Navigation**: `src/constants/data.ts`
- **Main Dashboards**: `src/components/dashboard/`
- **Document System**: `src/components/documents/`, `src/lib/document-management.ts`

### Configuration Files
- **Next.js**: `next.config.js`
- **Tailwind**: `tailwind.config.js`
- **shadcn/ui**: `components.json`
- **TypeScript**: `tsconfig.json`
- **Package Management**: `package.json`

This structure follows Next.js 15 App Router conventions with a clear separation of concerns and consistent naming patterns throughout the codebase.
