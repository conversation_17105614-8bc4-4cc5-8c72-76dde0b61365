# Lighten Counsel - User Goals Implementation Guide

**Created**: July 6, 2025  
**Purpose**: Comprehensive guide for implementing user's 11 specific goals  
**Status**: Ready for AI Assistant Implementation  

## 🎯 Overview

This document provides a complete implementation roadmap for the user's 11 specific goals for the Lighten Counsel system. Each goal has been analyzed, documented, and prioritized for implementation.

## 📋 User's 11 Goals Summary

### Goal #1: Essay System (主文书 + 补充文书)
- **Main Essays (主文书)**: Reusable across multiple schools
- **Supplemental Essays (补充文书)**: School-specific with one-click copy
- **Status**: ❌ Major redesign required
- **Priority**: Phase 1 (Critical)

### Goal #2: Mock Test Score Recording
- **Structured Tables**: Exam type/date/score/source
- **Consultant Upload**: Scanned documents and tutor feedback
- **Status**: ❌ Not implemented
- **Priority**: Phase 2 (High)

### Goal #3: Academic Records Dual Support
- **Manual Forms**: For data visualization and GPA calculation
- **Document Uploads**: For reference when schools differ
- **Status**: 🔄 Needs verification and enhancement
- **Priority**: Phase 4 (Medium)

### Goal #4: Activity Forms (Common App Structure)
- **Structured Forms**: Activity type, position, duration, description
- **File Uploads**: Certificates, videos (YouTube), photos
- **Word Limits**: 150 words for descriptions
- **Status**: 🔄 Needs Common App compliance verification
- **Priority**: Phase 3 (High)

### Goal #5: Activity Resume (Multiple Versions)
- **Unified Base**: Same activities for all universities
- **Multiple Files**: Different titles for different focuses
- **Major-Specific**: Engineering focus vs Liberal Arts focus
- **Status**: ❌ Multiple file system not implemented
- **Priority**: Phase 3 (Medium)

### Goal #6: Document Creation Flexibility
- **Free Creation**: Not restricted to templates
- **Format Guidance**: Recommendations, not restrictions
- **Word Limit Reminders**: Suggestions (e.g., "650 words recommended")
- **Status**: 🔄 May be too template-restrictive currently
- **Priority**: Phase 3 (Medium)

### Goal #7: Single Main Advisor System
- **One Primary Advisor**: Per student
- **Team Additions**: Essay teachers, planning assistants
- **Rare Changes**: Advisor changes should be uncommon
- **Status**: 🔄 Needs single advisor constraint enforcement
- **Priority**: Phase 2 (High)

### Goal #8: Invitation-Based Registration
- **Phase 1**: Invitation-only system
- **Phase 2**: Google login + invitation codes or admin confirmation
- **Status**: ❌ Complete invitation system missing
- **Priority**: Phase 1 (Critical)

### Goal #9: Admin-Assigned Advisors with Student Confirmation
- **Admin Assignment**: Admin assigns advisor to student
- **Student Confirmation**: Student can accept/reject assignment
- **Trust Building**: Display advisor info to build confidence
- **Status**: ❌ Student confirmation workflow missing
- **Priority**: Phase 2 (High)

### Goal #10: Competition/Awards Integration
- **Activity Integration**: Part of activity forms
- **Honors Category**: Match Common App "Honors" classification
- **Status**: 🔄 Needs verification of honors category
- **Priority**: Phase 3 (Low)

### Goal #11: Meeting System
- **Next Meeting Display**: Prominent on dashboard
- **Meeting Types**: First-time, Follow-up, Essay Review, Strategy, Decision
- **Scheduling**: Admin/consultant sets, student can request
- **Meeting Notes**: Post-meeting notes by consultant
- **Status**: ❌ Complete system missing
- **Priority**: Phase 1 (Critical)

## 🚀 Implementation Phases

### Phase 1: Critical Systems (Weeks 1-4)
**Goals**: #8, #11, #1

1. **Invitation Management System (Goal #8)**
   - Create invitation codes and email-specific invitations
   - Registration approval workflow
   - Admin confirmation process

2. **Meeting System (Goal #11)**
   - Meeting scheduling and types
   - Student meeting requests
   - Meeting notes and next meeting display

3. **Essay System Redesign (Goal #1)**
   - Main vs supplemental essay distinction
   - One-click copy functionality
   - Cross-school essay management

### Phase 2: Enhanced Features (Weeks 5-8)
**Goals**: #2, #9, #7

4. **Mock Test Tracking (Goal #2)**
   - Structured test score recording
   - Consultant upload capability
   - Progress tracking and analytics

5. **Student Confirmation Workflow (Goal #9)**
   - Advisor assignment confirmation
   - Student acceptance/rejection process
   - Trust-building advisor profiles

6. **Single Advisor Enforcement (Goal #7)**
   - Business logic for one primary advisor
   - Team member addition system
   - Advisor change workflow

### Phase 3: User Experience (Weeks 9-12)
**Goals**: #4, #5, #6, #10

7. **Activity Forms Enhancement (Goal #4)**
   - Common App structure compliance
   - File upload system (certificates, videos)
   - Word limit enforcement

8. **Multiple Resume Versions (Goal #5)**
   - Different focus area support
   - Title customization system
   - Major-specific resume generation

9. **Document Creation Flexibility (Goal #6)**
   - Free-form document creation
   - Guidance instead of restrictions
   - Format recommendations

10. **Competition Integration (Goal #10)**
    - Honors category in activities
    - Competition-specific fields
    - Award level tracking

### Phase 4: Data Management (Weeks 13-14)
**Goals**: #3

11. **Academic Records Dual Support (Goal #3)**
    - Verify and enhance form + upload integration
    - Data visualization improvements
    - Reference document management

## 📁 Key Files to Create/Modify

### New Files Required
- `src/app/dashboard/meetings/page.tsx`
- `src/app/dashboard/invitations/page.tsx`
- `src/app/api/meetings/route.ts`
- `src/app/api/invitations/route.ts`
- `src/app/api/essays/copy/route.ts`
- `src/app/api/advisor-assignments/route.ts`
- `src/app/api/students/[id]/mock-tests/route.ts`

### Files to Modify
- `src/app/dashboard/essays/page.tsx` - Essay system redesign
- `src/app/dashboard/activities/page.tsx` - Common App structure
- `src/app/dashboard/academics/page.tsx` - Mock test integration
- `src/components/dashboard/student-dashboard.tsx` - Meeting display
- `src/components/dashboard/admin-dashboard.tsx` - Invitation management
- `database/schema.sql` - New tables for meetings, invitations, mock tests

### Database Schema Additions
```sql
-- Meetings table
CREATE TABLE meetings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID NOT NULL REFERENCES student_profiles(id),
  consultant_id UUID NOT NULL REFERENCES consultants(id),
  meeting_type TEXT NOT NULL,
  scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
  status TEXT NOT NULL DEFAULT 'scheduled',
  meeting_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Invitations table
CREATE TABLE invitations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  code TEXT UNIQUE NOT NULL,
  email TEXT,
  role TEXT NOT NULL,
  created_by UUID NOT NULL REFERENCES users(id),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Mock test scores table
CREATE TABLE mock_test_scores (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID NOT NULL REFERENCES student_profiles(id),
  exam_type TEXT NOT NULL,
  test_date DATE NOT NULL,
  score INTEGER NOT NULL,
  max_score INTEGER NOT NULL,
  source TEXT NOT NULL,
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Essay updates (add type column)
ALTER TABLE documents ADD COLUMN essay_type TEXT;
ALTER TABLE documents ADD COLUMN schools JSONB;
ALTER TABLE documents ADD COLUMN source_essay_id UUID REFERENCES documents(id);

-- Advisor assignments table
CREATE TABLE advisor_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID NOT NULL REFERENCES student_profiles(id),
  consultant_id UUID NOT NULL REFERENCES consultants(id),
  assignment_type TEXT NOT NULL DEFAULT 'primary_advisor',
  status TEXT NOT NULL DEFAULT 'pending',
  confirmed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🔗 Cross-References

- **Detailed Gap Analysis**: See `ai-assistant-docs/IMPLEMENTATION_GAPS_ANALYSIS.md`
- **Updated Feature Specs**: See `ai-assistant-docs/FEATURE_SPECIFICATIONS.md`
- **New API Endpoints**: See `ai-assistant-docs/API_DOCUMENTATION.md`
- **Component Updates**: See `ai-assistant-docs/COMPONENT_LIBRARY.md`

## ✅ Success Criteria

Each goal implementation should meet these criteria:
1. **Functional**: Feature works as specified
2. **Tested**: Manual testing with all user roles
3. **Documented**: Updated documentation reflects changes
4. **Secure**: Proper role-based access control
5. **User-Friendly**: Intuitive interface matching existing design patterns

## 📞 Next Steps for AI Assistant

1. **Review this guide** and the updated documentation files
2. **Choose a phase** to start implementation (recommend Phase 1)
3. **Select a specific goal** within that phase
4. **Create detailed implementation tasks** for the chosen goal
5. **Begin implementation** following the existing codebase patterns
6. **Test thoroughly** with different user roles
7. **Update documentation** as changes are made

The documentation has been completely updated to reflect your 11 goals. The system is ready for systematic implementation following this roadmap.
