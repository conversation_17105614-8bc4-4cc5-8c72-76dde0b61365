# Lighten Counsel - Development Guide

**Last Updated**: July 6, 2025  
**Node.js Version**: 18+  
**Package Manager**: pnpm (recommended)  
**Framework**: Next.js 15 with App Router  

## 🚀 Quick Start

### Prerequisites
- Node.js 18 or higher
- pnpm (recommended) or npm
- Git
- Google Cloud Platform account
- Supabase account
- Clerk account

### Initial Setup
```bash
# Clone the repository
git clone https://github.com/jierm2/lighten-Counsel.git
cd lighten-counsel

# Install dependencies
pnpm install

# Copy environment template
cp .env.example .env.local

# Start development server
pnpm dev
```

## 🔧 Environment Configuration

### Required Environment Variables (`.env.local`)
```env
# Application
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_xxxxxxxxxx
CLERK_SECRET_KEY=sk_test_xxxxxxxxxx
CLERK_WEBHOOK_SECRET=whsec_xxxxxxxxxx

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Google Workspace Integration
GOOGLE_CLIENT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
GOOGLE_ADMIN_EMAIL=<EMAIL>
GOOGLE_PARENT_FOLDER_ID=1234567890abcdef
GOOGLE_TEMPLATES_FOLDER_ID=abcdef1234567890

# Optional: Development
NODE_ENV=development
```

### Service Setup

#### 1. Supabase Setup
```bash
# Run database migrations
psql -h your-host -U postgres -d your-db -f database/schema.sql

# Or use Supabase CLI
supabase db reset
```

#### 2. Clerk Setup
1. Create Clerk application
2. Configure OAuth providers (Google recommended)
3. Set up webhooks pointing to `/api/webhooks/clerk`
4. Configure user metadata and roles

#### 3. Google Workspace Setup
1. Create Google Cloud Project
2. Enable Google Docs API and Google Drive API
3. Create service account with domain-wide delegation
4. Configure OAuth scopes in Google Admin Console
5. Create folder structure in Google Drive

## 📦 Package Management

### Available Scripts (`package.json`)
```bash
# Development
pnpm dev              # Start development server with Turbopack
pnpm build            # Build for production
pnpm start            # Start production server
pnpm lint             # Run ESLint
pnpm lint:fix         # Fix ESLint issues and format code
pnpm format           # Format code with Prettier

# Quality Assurance
pnpm lint:strict      # Strict linting (max 0 warnings)
pnpm format:check     # Check code formatting
```

### Key Dependencies
```json
{
  "dependencies": {
    "next": "15.3.2",
    "react": "19.0.0",
    "typescript": "5.7.2",
    "@clerk/nextjs": "^6.12.12",
    "googleapis": "^150.0.1",
    "tailwindcss": "^4.0.0",
    "zustand": "^5.0.2"
  }
}
```

## 🏗️ Development Workflow

### Code Organization
```typescript
// File naming conventions
pages/components: kebab-case.tsx (e.g., document-dashboard.tsx)
hooks: use-hook-name.tsx (e.g., use-current-user.tsx)
services: service-name.ts (e.g., document-management.ts)
types: PascalCase interfaces (e.g., User, Document)
```

### Component Development Pattern
```typescript
// Standard component structure
'use client'; // If using client-side features

import { useState, useEffect } from 'react';
import { useCurrentUser } from '@/hooks/use-current-user';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ComponentProps {
  // Define props with TypeScript
}

export default function Component({ prop }: ComponentProps) {
  // Hooks at the top
  const { user, loading } = useCurrentUser();
  const [state, setState] = useState();

  // Effects
  useEffect(() => {
    // Side effects
  }, []);

  // Early returns for loading/error states
  if (loading) return <LoadingSkeleton />;

  // Main render
  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      {/* Component content */}
    </div>
  );
}
```

### API Route Development Pattern
```typescript
// src/app/api/endpoint/route.ts
import { NextRequest } from 'next/server';
import { withStandardAuth, handleAsyncOperation } from '@/lib/api-utils';
import type { User } from '@/types/application';

export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // Implementation
      return data;
    }, 'Error message for failures');
  }
);

export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await request.json();
      // Validate input
      // Process request
      return result;
    }, 'Error message for failures');
  }
);
```

## 🧪 Testing Procedures

### Manual Testing Protocol
1. **Authentication Testing**
   ```bash
   # Test with different user roles
   # Student: <EMAIL>
   # Consultant: [consultant-email]
   # Admin: [admin-email]
   ```

2. **Feature Testing Checklist**
   - [ ] User authentication and role switching
   - [ ] Dashboard functionality for each role
   - [ ] Document creation and Google Docs integration
   - [ ] Permission management and access controls
   - [ ] API endpoints with different user roles
   - [ ] Mobile responsiveness
   - [ ] Error handling and edge cases

3. **API Testing**
   ```bash
   # Use the test script
   node tests/api-test.js
   
   # Or test individual endpoints
   curl -H "Authorization: Bearer $TOKEN" \
        http://localhost:3000/api/documents
   ```

### Development Testing
```bash
# Run type checking
pnpm build

# Check for linting issues
pnpm lint

# Test API endpoints
pnpm dev
# Navigate to /api/test-auth for authentication testing
```

## 🚀 Deployment Process

### Production Build
```bash
# Ensure all dependencies are installed
pnpm install --frozen-lockfile

# Build the application
pnpm build

# Test the production build locally
pnpm start
```

### Vercel Deployment (Recommended)
1. **Connect Repository**
   - Import GitHub repository to Vercel
   - Configure build settings:
     - Framework: Next.js
     - Build Command: `pnpm build`
     - Output Directory: `.next`

2. **Environment Variables**
   - Copy all production environment variables
   - Ensure Google service account key is properly formatted
   - Set `NODE_ENV=production`

3. **Domain Configuration**
   - Configure custom domain if needed
   - Update `NEXT_PUBLIC_APP_URL` to production URL
   - Update Clerk redirect URLs

### Manual Deployment
```bash
# Using PM2 (Process Manager)
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'lighten-counsel',
    script: 'npm',
    args: 'start',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
EOF

# Deploy
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## 🔍 Debugging and Development Tools

### Development Server Features
```bash
# Start with Turbopack (faster builds)
pnpm dev

# Access development tools
http://localhost:3000/api/test-auth  # Authentication testing
http://localhost:3000/_next/static   # Static assets
```

### Debugging API Issues
```typescript
// Enable debug logging in development
console.log('Debug info:', { user, data, error });

// Use API test endpoints
GET /api/test-auth  # Test authentication
POST /api/test-auth # Create user if missing
```

### Database Debugging
```sql
-- Check user roles and permissions
SELECT u.email, u.role, sp.id as student_profile_id 
FROM users u 
LEFT JOIN student_profiles sp ON u.id = sp.user_id;

-- Check document permissions
SELECT d.id, d.metadata->>'title', u.email as student_email
FROM documents d
JOIN student_profiles sp ON d.student_id = sp.id
JOIN users u ON sp.user_id = u.id;
```

## 🔧 Common Development Tasks

### Adding New API Endpoint
1. Create route file: `src/app/api/new-endpoint/route.ts`
2. Implement with proper authentication middleware
3. Add TypeScript types if needed
4. Test with different user roles
5. Update API documentation

### Adding New Component
1. Create component file in appropriate directory
2. Follow naming conventions and patterns
3. Add TypeScript interfaces for props
4. Implement responsive design
5. Add to component library documentation

### Database Schema Changes
1. Update `database/schema.sql`
2. Create migration script if needed
3. Update TypeScript types
4. Test with existing data
5. Update API endpoints if needed

## 📋 Code Quality Standards

### TypeScript
- Strict mode enabled
- No `any` types (use proper typing)
- Interface definitions for all props and data structures
- Proper error handling with typed errors

### Styling
- Tailwind CSS utility classes
- Mobile-first responsive design
- Consistent spacing using Tailwind scale
- Dark mode support with CSS variables

### Performance
- Use Next.js Image component for images
- Implement proper loading states
- Optimize bundle size with dynamic imports
- Use React.memo for expensive components

## 🔗 Cross-References

- **Project Structure**: See `ai-assistant-docs/CODEBASE_STRUCTURE.md`
- **API Details**: See `ai-assistant-docs/API_DOCUMENTATION.md`
- **Component Usage**: See `ai-assistant-docs/COMPONENT_LIBRARY.md`
- **Authentication**: See `ai-assistant-docs/ROLE_BASED_ACCESS.md`
- **Troubleshooting**: See `ai-assistant-docs/TROUBLESHOOTING.md`
