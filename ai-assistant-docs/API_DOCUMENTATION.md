# Lighten Counsel - API Documentation

**Last Updated**: July 6, 2025
**Base URL**: `/api`
**Authentication**: Clerk session tokens required for all endpoints
**Status**: Updated for User's 11 Goals Implementation

## 🔐 Authentication System

### Authentication Flow

All API endpoints use Clerk authentication with role-based access control implemented in `src/lib/api-utils.ts`.

```typescript
// Authentication middleware patterns
export const withStandardAuth = (handler: AuthenticatedHandler) => {
  return async (request: NextRequest, context?: any) => {
    const currentUser = await getAuthenticatedUser();
    return handler(request, currentUser, context);
  };
};

export const withStandardRole = (
  allowedRoles: string[],
  handler: RoleHandler
) => {
  return withStandardAuth(async (request, currentUser, context) => {
    requireRole(currentUser, allowedRoles);
    return handler(request, currentUser, context);
  });
};
```

### User Roles

- **student**: Access to own data and documents
- **consultant**: Access to assigned students and their data
- **admin**: Full system access

## 📊 Database Schema

### Core Tables (from `database/schema.sql`)

#### Users Table

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clerk_id TEXT UNIQUE NOT NULL,
    email TEXT NOT NULL,
    role user_role NOT NULL DEFAULT 'student',
    profile_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Student Profiles Table

```sql
CREATE TABLE student_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    academic_info JSONB,
    target_schools JSONB,
    application_status JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);
```

#### Documents Table

```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
    google_doc_id TEXT,
    doc_type document_type NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Template Metadata Table

```sql
CREATE TABLE template_metadata (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    google_doc_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    doc_type document_type NOT NULL,
    category TEXT NOT NULL DEFAULT 'general',
    description TEXT,
    word_limit INTEGER,
    tags TEXT[] DEFAULT '{}',
    difficulty_level difficulty_level NOT NULL DEFAULT 'intermediate',
    is_featured BOOLEAN NOT NULL DEFAULT false,
    usage_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🛠️ API Endpoints

### Authentication Endpoints

#### GET /api/auth/me

**File**: `src/app/api/auth/me/route.ts`  
**Purpose**: Get current user information  
**Access**: All authenticated users

```typescript
// Response format
{
  "success": true,
  "data": {
    "id": "uuid",
    "clerk_id": "string",
    "email": "string",
    "role": "student|consultant|admin",
    "profile_data": {},
    "profile": {
      "id": "uuid",
      "academic_info": {},
      "target_schools": [],
      "application_status": {}
    }
  }
}
```

### Document Management Endpoints

#### GET /api/documents

**File**: `src/app/api/documents/route.ts`  
**Purpose**: List documents with role-based filtering  
**Access**: All authenticated users

**Query Parameters**:

- `student_id`: Filter by student (consultants/admins)
- `doc_type`: Filter by document type
- `limit`: Number of results (default: 50)
- `offset`: Pagination offset

```typescript
// Response format
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "student_id": "uuid",
      "google_doc_id": "string",
      "doc_type": "essay|personal_statement|transcript|activity_resume|other",
      "metadata": {
        "title": "string",
        "description": "string",
        "word_count": number,
        "status": "draft|in_review|completed",
        "deadline": "ISO date string"
      },
      "created_at": "ISO date string",
      "updated_at": "ISO date string"
    }
  ],
  "pagination": {
    "total": number,
    "limit": number,
    "offset": number
  }
}
```

#### POST /api/documents

**File**: `src/app/api/documents/route.ts`  
**Purpose**: Create new document with Google Docs integration  
**Access**: Students (own), Consultants/Admins (assigned students)

```typescript
// Request body
{
  "student_id": "uuid",
  "doc_type": "essay|personal_statement|transcript|activity_resume|other",
  "metadata": {
    "title": "string",
    "description": "string",
    "deadline": "ISO date string"
  },
  "template_id": "string", // Optional
  "initial_content": "string" // Optional
}

// Response format
{
  "success": true,
  "data": {
    "id": "uuid",
    "google_doc_id": "string",
    "google_doc_url": "string",
    "folder_id": "string",
    // ... other document fields
  }
}
```

#### GET /api/documents/[id]

**File**: `src/app/api/documents/[id]/route.ts`  
**Purpose**: Get specific document details  
**Access**: Document owner or assigned consultant/admin

#### PUT /api/documents/[id]

**File**: `src/app/api/documents/[id]/route.ts`  
**Purpose**: Update document metadata  
**Access**: Document owner or assigned consultant/admin

#### GET /api/documents/[id]/google-doc

**File**: `src/app/api/documents/[id]/google-doc/route.ts`  
**Purpose**: Get Google Doc content and metadata  
**Access**: Document owner or assigned consultant/admin

### Student Management Endpoints

#### GET /api/students

**File**: `src/app/api/students/route.ts`  
**Purpose**: List students (for consultants/admins)  
**Access**: Consultants (assigned students), Admins (all students)

#### GET /api/students/[id]

**File**: `src/app/api/students/[id]/route.ts`  
**Purpose**: Get student profile details  
**Access**: Student (own), Consultant (assigned), Admin (all)

#### GET /api/students/target-schools

**File**: `src/app/api/students/target-schools/route.ts`  
**Purpose**: Get student's target schools  
**Access**: Student (own), Consultant (assigned), Admin (all)

### Template Management Endpoints

#### GET /api/templates

**File**: `src/app/api/templates/route.ts`  
**Purpose**: List available templates with metadata  
**Access**: All authenticated users

**Query Parameters**:

- `doc_type`: Filter by document type
- `category`: Filter by category
- `featured`: Show only featured templates

```typescript
// Response format
{
  "success": true,
  "data": [
    {
      "google_doc_id": "string",
      "name": "string",
      "doc_type": "essay|personal_statement|transcript|activity_resume|other",
      "category": "string",
      "description": "string",
      "word_limit": number,
      "tags": ["string"],
      "difficulty_level": "beginner|intermediate|advanced",
      "is_featured": boolean,
      "usage_count": number
    }
  ]
}
```

#### POST /api/templates

**File**: `src/app/api/templates/route.ts`  
**Purpose**: Create new template (Admin only)  
**Access**: Admin only

### User Management Endpoints

#### GET /api/users

**File**: `src/app/api/users/route.ts`  
**Purpose**: List all users (Admin only)  
**Access**: Admin only

#### POST /api/users

**File**: `src/app/api/users/route.ts`  
**Purpose**: Create new user (Admin only)  
**Access**: Admin only

### Statistics Endpoints

#### GET /api/stats/dashboard

**File**: `src/app/api/stats/dashboard/route.ts`  
**Purpose**: Get role-specific dashboard statistics  
**Access**: All authenticated users

**Query Parameters**:

- `role`: Specify role for statistics (student|consultant|admin)

### **NEW: Meeting Management Endpoints (Goal #11)**

#### GET /api/meetings

**File**: `src/app/api/meetings/route.ts`
**Purpose**: List meetings for current user
**Access**: All authenticated users

**Query Parameters**:

- `student_id`: Filter by student (consultants/admins)
- `status`: Filter by meeting status
- `upcoming`: Show only upcoming meetings

#### POST /api/meetings

**File**: `src/app/api/meetings/route.ts`
**Purpose**: Create new meeting
**Access**: Consultants/Admins

#### GET /api/meetings/requests

**File**: `src/app/api/meetings/requests/route.ts`
**Purpose**: List meeting requests
**Access**: Consultants/Admins

#### POST /api/meetings/requests

**File**: `src/app/api/meetings/requests/route.ts`
**Purpose**: Student requests meeting
**Access**: Students

### **Invitation Management Endpoints (Goal #8) - ✅ IMPLEMENTED**

#### GET /api/invitations

**File**: `src/app/api/invitations/route.ts`
**Purpose**: List invitations (Admin only)
**Access**: Admin only
**Status**: ✅ Implemented

#### POST /api/invitations

**File**: `src/app/api/invitations/route.ts`
**Purpose**: Create invitation codes
**Access**: Admin only
**Status**: ✅ Implemented

#### GET /api/invitations/[id]

**File**: `src/app/api/invitations/[id]/route.ts`
**Purpose**: Get invitation details
**Access**: Admin only
**Status**: ✅ Implemented

#### PATCH /api/invitations/[id]

**File**: `src/app/api/invitations/[id]/route.ts`
**Purpose**: Update invitation
**Access**: Admin only
**Status**: ✅ Implemented

#### DELETE /api/invitations/[id]

**File**: `src/app/api/invitations/[id]/route.ts`
**Purpose**: Delete invitation
**Access**: Admin only
**Status**: ✅ Implemented

#### POST /api/invitations/validate

**File**: `src/app/api/invitations/validate/route.ts`
**Purpose**: Validate invitation code during registration
**Access**: Public (pre-authentication)
**Status**: ✅ Implemented

#### GET /api/registration-requests

**File**: `src/app/api/registration-requests/route.ts`
**Purpose**: List registration requests
**Access**: Admin only
**Status**: ✅ Implemented

#### POST /api/registration-requests

**File**: `src/app/api/registration-requests/route.ts`
**Purpose**: Submit registration request
**Access**: Public (pre-authentication)
**Status**: ✅ Implemented

#### PATCH /api/registration-requests/[id]

**File**: `src/app/api/registration-requests/[id]/route.ts`
**Purpose**: Approve/reject registration request
**Access**: Admin only
**Status**: ✅ Implemented

### **NEW: Mock Test Endpoints (Goal #2)**

#### GET /api/students/[id]/mock-tests

**File**: `src/app/api/students/[id]/mock-tests/route.ts`
**Purpose**: Get student's mock test scores
**Access**: Student (own), Consultant (assigned), Admin

#### POST /api/students/[id]/mock-tests

**File**: `src/app/api/students/[id]/mock-tests/route.ts`
**Purpose**: Add mock test score
**Access**: Student (own), Consultant (assigned), Admin

### **NEW: Essay Management Endpoints (Goal #1)**

#### GET /api/essays

**File**: `src/app/api/essays/route.ts`
**Purpose**: List essays with main/supplemental categorization
**Access**: All authenticated users

#### POST /api/essays/copy

**File**: `src/app/api/essays/copy/route.ts`
**Purpose**: One-click copy essay for different school
**Access**: Students (own), Consultants (assigned), Admin

### **NEW: Advisor Assignment Endpoints (Goals #7, #9)**

#### GET /api/advisor-assignments

**File**: `src/app/api/advisor-assignments/route.ts`
**Purpose**: List advisor assignments and confirmations
**Access**: All authenticated users

#### POST /api/advisor-assignments

**File**: `src/app/api/advisor-assignments/route.ts`
**Purpose**: Admin assigns advisor to student
**Access**: Admin only

#### PUT /api/advisor-assignments/[id]/confirm

**File**: `src/app/api/advisor-assignments/[id]/confirm/route.ts`
**Purpose**: Student confirms advisor assignment
**Access**: Students only

### Webhook Endpoints

#### POST /api/webhooks/clerk

**File**: `src/app/api/webhooks/clerk/route.ts`
**Purpose**: Handle Clerk user events (creation, updates)
**Access**: Clerk webhook (verified signature)

## 🔗 Google Workspace Integration

### Google APIs Service (`src/lib/google-apis.ts`)

#### GoogleDocsService

```typescript
class GoogleDocsService {
  static async createDocument(
    title: string,
    studentName: string,
    docType: string,
    content?: string,
    templateId?: string
  ): Promise<{ documentId: string; documentUrl: string; folderId: string }>;

  static async getDocumentContent(documentId: string): Promise<string>;

  static async shareDocument(
    documentId: string,
    emails: string[],
    role: 'reader' | 'writer' = 'writer'
  ): Promise<void>;
}
```

#### GoogleDriveService

```typescript
class GoogleDriveService {
  static async ensureStudentFolderStructure(
    studentName: string,
    docType: string
  ): Promise<string>;

  static async getTemplates(): Promise<GoogleDriveFile[]>;

  static async copyTemplate(
    templateId: string,
    newTitle: string,
    folderId: string
  ): Promise<{ documentId: string; documentUrl: string }>;
}
```

## 🔒 Security Implementation

### Row Level Security (RLS)

- All database tables have RLS policies
- Users can only access their own data or data they're authorized to see
- Consultants can only access assigned students
- Admins have full access

### API Security

- All endpoints require authentication via Clerk
- Role-based access control enforced at API level
- Input validation using Zod schemas
- Error handling prevents information leakage

### Google Workspace Security

- Service account authentication for Google APIs
- Domain-wide delegation for admin access
- Proper permission management for shared documents
- Secure folder structure for document organization

## 📝 Error Handling

### Standard Error Response Format

```typescript
{
  "success": false,
  "error": {
    "message": "string",
    "code": "string",
    "details": {}
  }
}
```

### Common Error Codes

- `AUTHENTICATION_ERROR`: Invalid or missing authentication
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `VALIDATION_ERROR`: Invalid input data
- `NOT_FOUND_ERROR`: Resource not found
- `GOOGLE_API_ERROR`: Google Workspace API errors

## 🔗 Cross-References

- **Authentication Details**: See `ai-assistant-docs/ROLE_BASED_ACCESS.md`
- **Component Integration**: See `ai-assistant-docs/COMPONENT_LIBRARY.md`
- **Development Setup**: See `ai-assistant-docs/DEVELOPMENT_GUIDE.md`
- **Troubleshooting**: See `ai-assistant-docs/TROUBLESHOOTING.md`
