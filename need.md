Detailed Functional Requirements Document1.0 Roles & PermissionsThe system will include three primary user roles, each with distinct access and operational permissions.1.1 StudentStudent users can manage and view all materials related to their own applications, including:Essay Management:View, modify, and upload the main personal statement.Manage supplemental essays for specific target schools.View the consultant's suggestions and comments on all essays.Upload and manage official transcripts for various academic stages.Personal Information Management:View and modify basic personal information (e.g., contact details, family background).View GPA and transcripts by academic year and quarter.(Question: Is the transcript format the same for every school? Should this be a structured form for manual entry or a document upload?)Record and update standardized test scores, including:PSAT official score and dateSAT official score and dateACT official score and dateRecord and update the number of AP courses and their scores.Create and edit the activity resume/list.View all historical meeting minutes and future meeting schedules with the consultant. (TBD)1.2 ConsultantPermissions are centered around the students assigned to them:Student Management:Can only view and manage their assigned list of students.View students' "Lighten Test" mock exam scores. (TBD)Essay & Application Collaboration:Core Feature: View, modify, and comment on student application essays, implementing an online collaborative editing experience similar to Google Docs (supports real-time multi-user editing, highlights changes, and auto-saves).View and edit the student's list of target schools.View the student's official transcripts.Core Feature: View and edit the student's activity resume in a structured format, similar to the Common App's activity list.Student Information Access:View the complete profile of their assigned students.View all academic year and quarter grades and transcripts for their students.View and record students' standardized test scores (PSAT, SAT, ACT).View students' AP course and score records.Create, edit, and view meeting minutes and schedules for their students.1.3 AdminThe administrator has the highest level of permissions:System Management:Create, manage, and assign all user accounts (students, consultants).Manage the basic information of all students.Configure and manage permissions for different roles.View the grades, test records, and application progress of all students.2.0 Core Functional Modules2.1 Document ManagementOnline Editor with Auto-Save: When a user is editing, the system automatically saves in the background to prevent data loss.Template Library: (TBD)Provide standard essay templates (e.g., Personal Statement).Provide standard resume templates.Provide standard activity list templates.2.2 School Application ManagementSchool Information Database:(To be confirmed): Does the system need a pre-populated database with information for all U.S. universities?This data can be sourced from the College Scorecard API.School information fields should include:Official school nameApplication deadlines for each round (ED, EA, RD)Checklist of required materialsStudent School Selection & Progress Tracking:Students or consultants can create and maintain a target school list ("My Schools").Track the application status for each target school. Status options should include:In ProgressSubmittedAdmittedDeniedWaitlistedAutomatically generate a checklist of materials for each school and allow setting personal deadline reminders.2.3 Student Information ManagementAcademic Record Management: Store student personal information, semesterly GPAs, and transcript files in a structured manner.Standardized Test Management: Support recording multiple SAT/ACT scores and dates, with the ability to highlight the highest score.Activity Resume Management: Provide a structured form to record extracurricular activities, including dates, positions held, and descriptions.Meeting Minutes System: Allow consultants to create records for each meeting, including the date, discussion points, and action items.2.4 System FeaturesNotifications:Trigger Events:A new comment or message is received.An application deadline is approaching (e.g., 14, 7, and 3 days out).A document you are responsible for has been modified.An application status has changed.Audit Trail:Record key actions to ensure transparency and traceability.Records should include:[User] modified [Document Name] at [Time].[User] updated the status of [Student Name]'s application to [School Name] to [New Status] at [Time].[User] uploaded [File Name] at [Time].3.0 System-Wide RequirementsGoogle Account: All student users must have a personal Google account (not a school-provided one) to facilitate integration with Google Docs.4.0 Open Questions for ClarificationEssay Workflow: Should the system distinguish between a "main essay" and "supplemental essays"?Option A: Each school's essays are independent. A student must create essays for each school but can use a "one-click duplicate" feature to modify existing work.Option B: The system has distinct "main" and "supplemental" categories. A student writes one main essay and can link it to multiple schools, while supplemental essays are tied to specific schools.Mock Exam Scores: Should the system record mock exam scores (e.g., from the "Lighten Test")?GPA/Grade Entry: When students record their GPA and grades, should they upload a scan/PDF of their transcript, fill out a standardized form with their courses and grades, or should both methods be supported?Activity Resume Structure: Should the activity resume be a rich-text editor allowing free-form entry, or should it be a structured form similar to the Common App (with fields for activity type, position, hours, description with word limits, etc.)?Activity Resume Uniformity: Is the activity resume/list a single document used for all school applications, or can it be customized for different schools?