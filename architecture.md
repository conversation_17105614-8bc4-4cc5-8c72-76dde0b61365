# Lighten Counsel - System Architecture

**Last Updated**: 2025-07-04
**Maintainer**: Lighten Counsel Team

## System Architecture Overview

```mermaid
graph TD
    subgraph "Frontend (Vercel)"
        A[User Interface] --> B[Next.js Application]
        B --> C1[Student Dashboard]
        B --> C2[Consultant Workbench]
        B --> C3[Admin Console]
    end

    subgraph "Backend Services"
        D[Vercel Serverless Functions] --> E[API Layer]
        E --> F[Supabase Integration]
        E --> G[Google Workspace Integration]
        E --> H[Clerk Integration]
        E --> I[Document Management Service]
        E --> J[Template Service]
        E --> K[Permission Manager]
    end

    subgraph "Data Storage"
        F --> L[Supabase PostgreSQL]
        L --> M1[User Data]
        L --> M2[Application Progress]
        L --> M3[School Information]
        L --> M4[Document References]
        L --> M5[Template Metadata]
        L --> M6[Usage Analytics]

        G --> N[Google Workspace]
        N --> O1[Document Storage]
        N --> O2[Version History]
        N --> O3[Collaboration Data]
    end

    subgraph "Authentication System"
        H --> P[Clerk Auth]
        P --> Q1[User Authentication]
        P --> Q2[Role Management]
        P --> Q3[Social Login]
        R[Google OAuth] --> S[Document Access Permissions]
    end

    B --> D
    C1 --> E
    C2 --> E
    C3 --> E
```

## Core Tech Stack

### Frontend

- **Framework**: Next.js 15 with App Router
- **Hosting**: Vercel
- **UI Library**: shadcn/ui component library
- **Styling**: Tailwind CSS
- **Language**: TypeScript

### Backend Services

- **Database**: Supabase (PostgreSQL)
- **Authentication**: Clerk (User Management)
- **Document Management**: Google Workspace API integration
- **API**: Vercel Serverless Functions
- **Service Layer**: Custom document management, template, and permission services

### Data Storage

- **Application Data**: Supabase PostgreSQL
- **Document Content**: Google Drive
- **Template Management**: Enhanced database schema with usage tracking

### Authentication System

- **User Auth**: Clerk Auth (Authentication, User Management)
- **Document Access**: Google OAuth (Document Access Permissions)

## Service Layer Architecture

### 1. DocumentManagementService (`src/lib/document-management.ts`)

- **Purpose**: Comprehensive document CRUD operations with Google Docs integration
- **Key Features**:
  - Document creation with automatic Google Docs setup
  - Role-based permission management
  - Template integration and tracking
  - Bulk operations support

### 2. AdvancedPermissionManager (`src/lib/permission-manager.ts`)

- **Purpose**: Advanced permission management for complex scenarios
- **Key Features**:
  - Consultant assignment automation
  - Bulk permission repairs
  - Permission auditing and reporting
  - Consultant deactivation handling

### 3. TemplateService (`src/lib/template-service.ts`)

- **Purpose**: Template management and intelligent recommendations
- **Key Features**:
  - Personalized template recommendations
  - Usage tracking and analytics
  - Template creation from existing documents
  - Category and difficulty-based organization

### 4. GoogleDocsService (`src/lib/google-apis.ts`)

- **Purpose**: Core Google Workspace integration
- **Key Features**:
  - Document creation with admin ownership
  - Permission management (writer/commenter access)
  - Folder organization and management
  - Content synchronization

## Data Model

### Enhanced Database Schema

#### Core Tables

- **users**: User authentication and role management
- **student_profiles**: Student academic information and preferences
- **consultants**: Consultant specialties and availability
- **documents**: Document metadata with Google Docs integration
- **schools**: Target school information and requirements

#### New Template System Tables

- **template_metadata**: Template information, categories, and usage statistics
- **template_usage**: Individual template usage tracking for analytics
- **student_consultant_relations**: Consultant assignments with automatic permission updates

### Google Docs Structure

#### Document Organization

```
📁 Lighten Admin Google Workspace (OWNER)
├── 📁 Students/
│   ├── 📁 John Smith/
│   │   ├── 📄 Personal Statement (Student: Writer, Consultant: Commenter)
│   │   └── 📄 Harvard Essay (Student: Writer, Consultant: Commenter)
└── 📁 Templates/ (Admin managed)
```

#### Permission Model

- **Students**: Writer access to their own documents
- **Consultants**: Commenter access to assigned students' documents
- **Admins**: Owner/Editor access to all documents
- **Automatic Updates**: Permissions adjust when consultants are assigned/reassigned
