# API Response Migration Guide

This document outlines the migration from legacy API response format to the new standardized format.

## Migration Checklist

### ✅ Completed Endpoints

- [x] `/api/users/me` - Updated to use standard format
- [x] `/api/templates` - Updated to use standard format
- [x] `/api/documents` (GET, POST) - Updated to use standard format with enhanced metadata
- [x] `/api/documents/[id]` (GET, PUT, DELETE) - Updated to use standard format with operation tracking
- [x] `/api/documents/stats` (GET) - Updated to use standard format with analytics metadata
- [x] `/api/documents/operations` (POST) - Updated to use standard format with bulk operation tracking
- [x] `/api/students` (GET, POST) - Updated to use standard format with role-based filtering
- [x] `/api/students/[id]` (GET, PUT) - Updated to use standard format with permission validation
- [x] `/api/permissions` (GET, POST) - Updated to use standard format with admin operation tracking
- [x] `/api/schools` (GET, POST) - Updated to use standard format with filtering metadata
- [x] `/api/schools/[id]` (GET, PUT, DELETE) - Updated to use standard format with admin operations
- [x] `/api/consultants` (GET) - Updated to use standard format with role-based access
- [x] `/api/consultants/[id]` (GET, PUT) - Updated to use standard format with assignment status

### 🔄 Endpoints to Migrate

#### Low Priority (Utility/Admin)

- [ ] `/api/users` (GET) - List all users (admin only)
- [ ] `/api/users/[id]` (GET, PUT, DELETE) - User management operations

#### Very Low Priority (Utility/Testing)

- [ ] `/api/templates/recommendations` (GET)
- [ ] `/api/templates/analytics` (GET)
- [ ] `/api/test/*` endpoints
- [ ] `/api/webhooks/clerk` (POST)

## Migration Steps for Each Endpoint

### 1. Update Imports

```typescript
// OLD
import {
  withAuth,
  withRole,
  createSuccessResponse,
  createErrorResponse,
  createPaginatedResponse
} from '@/lib/api-utils';

// NEW
import {
  withStandardAuth,
  withStandardRole,
  createStandardSuccessResponse,
  createStandardErrorResponse,
  createStandardPaginatedResponse,
  handleAsyncOperation
} from '@/lib/api-utils';
```

### 2. Update Handler Wrappers

```typescript
// OLD
export const GET = withAuth(async (request: NextRequest, currentUser: User) => {
  // handler code
});

// NEW
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // handler code
    }, 'Failed to [operation description]');
  }
);
```

### 3. Update Response Creation

```typescript
// OLD
return createSuccessResponse(data, 'Success message');

// NEW
return createStandardSuccessResponse(data, 'Success message', 200, {
  // Additional metadata
  user_role: currentUser.role,
  operation_type: 'read'
  // ... other relevant metadata
});
```

### 4. Update Paginated Responses

```typescript
// OLD
return createPaginatedResponse(data, total, pagination, 'Success message');

// NEW
return createStandardPaginatedResponse(
  data,
  total,
  pagination,
  'Success message',
  {
    // Additional metadata
    applied_filters: {
      /* filters */
    },
    user_role: currentUser.role
  }
);
```

### 5. Error Handling

```typescript
// OLD - Manual error handling
try {
  // operation
} catch (error) {
  console.error('Error:', error);
  throw error;
}

// NEW - Use handleAsyncOperation
return handleAsyncOperation(async () => {
  // operation
}, 'Failed to perform operation');
```

## Benefits of Migration

### Enhanced Response Structure

- **Consistent Format**: All responses follow the same structure
- **Metadata Support**: Additional context in `meta` field
- **Request Tracking**: Unique request IDs for debugging
- **Timestamps**: ISO timestamps for all responses

### Improved Error Handling

- **Structured Errors**: Consistent error object format
- **Validation Errors**: Detailed field-level validation feedback
- **Error Codes**: Standardized error codes for client handling

### Better Developer Experience

- **Type Safety**: Enhanced TypeScript types
- **Debugging**: Request IDs and timestamps for tracing
- **Validation**: Automatic response format validation in development

## Frontend Migration

### Update API Response Handling

```typescript
// OLD
const response = await fetch('/api/endpoint');
const result = await response.json();
if (result.success) {
  setData(result.data);
} else {
  setError(result.error);
}

// NEW
const response = await fetch('/api/endpoint');
const result = await response.json();
if (result.success) {
  setData(result.data);
  // Access additional metadata
  console.log('Request ID:', result.request_id);
  console.log('Metadata:', result.meta);
} else {
  setError(result.error.message);
  // Handle validation errors
  if (result.error.code === 'VALIDATION_ERROR') {
    setValidationErrors(result.error.details);
  }
}
```

### Type Guards Usage

```typescript
import {
  isApiSuccessResponse,
  isApiValidationErrorResponse
} from '@/types/application';

const result = await apiCall();
if (isApiSuccessResponse(result)) {
  // TypeScript knows this is a success response
  setData(result.data);
} else if (isApiValidationErrorResponse(result)) {
  // Handle validation errors specifically
  setValidationErrors(result.error.details);
}
```

## Testing Migration

### Verify Response Format

1. Check that all responses include required fields:

   - `success` (boolean)
   - `timestamp` (ISO string)
   - `request_id` (string)

2. For success responses:

   - `data` field contains the response data
   - `message` field (optional)
   - `meta` field (optional)

3. For error responses:
   - `error.message` contains error description
   - `error.code` contains error code (optional)
   - `error.details` for validation errors

### Development Validation

- Response validation middleware automatically checks format in development
- Console warnings for format violations
- Optional strict mode for throwing errors

## Rollback Plan

If issues arise, endpoints can be rolled back by:

1. Reverting import changes
2. Changing `withStandardAuth` back to `withAuth`
3. Changing `createStandardSuccessResponse` back to `createSuccessResponse`
4. Removing `handleAsyncOperation` wrapper

The legacy functions remain available for backward compatibility.
