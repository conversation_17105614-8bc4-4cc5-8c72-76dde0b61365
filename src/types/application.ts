// User and Profile Types
export interface User {
  id: string;
  clerk_id: string;
  email: string;
  role: 'student' | 'consultant' | 'admin';
  profile_data?: any;
  created_at: string;
  updated_at: string;
}

export interface StudentProfile {
  id: string;
  user_id: string;
  academic_info?: AcademicInfo;
  target_schools?: TargetSchool[];
  application_status?: ApplicationStatus;
  created_at: string;
  updated_at: string;
}

export interface ConsultantProfile {
  id: string;
  user_id: string;
  specialties: string[];
  availability?: ConsultantAvailability;
  created_at: string;
  updated_at: string;
}

// Academic Information Types - Enhanced for Goal #3: Dual Support
export interface AcademicInfo {
  // Manual form data (for calculations and visualization)
  gpa?: {
    weighted?: number;
    unweighted?: number;
    scale?: number;
    manual_entry?: boolean;
  };
  test_scores?: TestScores;
  ap_courses?: APCourse[];
  activities?: Activity[];
  personal_info?: PersonalInfo;
  class_rank?: {
    rank?: number;
    total?: number;
  };
  graduation_date?: string;
  school_info?: {
    name?: string;
    type?: string;
    location?: string;
  };

  // Document uploads (for reference)
  transcripts?: TranscriptFile[];
  grade_reports?: DocumentFile[];
  test_score_reports?: DocumentFile[];

  // Dual support flag
  data_entry_method?: 'form_only' | 'upload_only' | 'both';
}

// Legacy transcript record (keeping for backward compatibility)
export interface TranscriptRecord {
  academic_year: string;
  quarter: string;
  gpa: number;
  courses?: Course[];
  file_url?: string;
}

// Enhanced transcript file for dual support
export interface TranscriptFile {
  id: string;
  filename: string;
  file_url: string;
  upload_date: string;
  academic_year: string;
  quarter?: string;
  status: 'pending' | 'verified' | 'rejected';
  gpa?: number;
  courses?: CourseRecord[];
}

// Generic document file for academic records
export interface DocumentFile {
  id: string;
  filename: string;
  file_url: string;
  upload_date: string;
  document_type: string;
  status: 'pending' | 'verified' | 'rejected';
  metadata?: any;
}

// Enhanced course record
export interface CourseRecord {
  name: string;
  grade: string;
  credits: number;
  semester?: string;
  year?: string;
}

export interface Course {
  name: string;
  grade: string;
  credits?: number;
}

export interface TestScores {
  psat?: TestScore[];
  sat?: TestScore[];
  act?: TestScore[];
}

export interface TestScore {
  score: number;
  date: string;
  is_official: boolean;
  is_highest?: boolean;
  subject_scores?: SubjectScores;
  is_superscore?: boolean;
}

export interface SubjectScores {
  // SAT Subject Scores
  sat_math?: number;
  sat_ebrw?: number; // Evidence-Based Reading and Writing

  // ACT Subject Scores
  act_english?: number;
  act_math?: number;
  act_reading?: number;
  act_science?: number;
  act_writing?: number; // Optional writing section
}

export interface APCourse {
  subject: string;
  score: number;
  year: string;
}

export interface Activity {
  name: string;
  type: string;
  position?: string;
  description: string;
  start_date: string;
  end_date?: string;
  hours_per_week?: number;
  weeks_per_year?: number;
}

export interface PersonalInfo {
  first_name?: string;
  last_name?: string;
  phone?: string;
  address?: Address;
  family_background?: any;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
}

// School and Application Types
export interface School {
  id: string;
  name: string;
  details?: SchoolDetails;
  application_requirements?: ApplicationRequirements;
  created_at: string;
  updated_at: string;
}

export interface SchoolDetails {
  location?: string;
  type?: string;
  ranking?: number;
  acceptance_rate?: number;
  tuition?: number;
  website?: string;
}

export interface ApplicationRequirements {
  essays?: EssayRequirement[];
  test_scores_required?: string[];
  transcript_required?: boolean;
  recommendation_letters?: number;
  deadlines?: ApplicationDeadlines;
}

export interface EssayRequirement {
  prompt: string;
  word_limit: number;
  type: 'personal_statement' | 'supplemental';
}

export interface ApplicationDeadlines {
  early_decision?: string;
  early_action?: string;
  regular_decision?: string;
}

export interface TargetSchool {
  school_id: string;
  school_name: string;
  application_type: 'ED' | 'EA' | 'RD';
  status: 'in_progress' | 'submitted' | 'admitted' | 'denied' | 'waitlisted';
  deadline: string;
  personal_deadline?: string;
  essays?: SchoolEssay[];
  materials_checklist?: MaterialItem[];
}

export interface SchoolEssay {
  prompt: string;
  word_limit: number;
  document_id?: string;
  status: 'not_started' | 'in_progress' | 'completed';
}

export interface MaterialItem {
  name: string;
  required: boolean;
  completed: boolean;
  due_date?: string;
}

export interface ApplicationStatus {
  overall_progress: number;
  schools: TargetSchool[];
  upcoming_deadlines: DeadlineItem[];
}

export interface DeadlineItem {
  school_name: string;
  deadline: string;
  type: 'application' | 'essay' | 'material';
  description: string;
}

// Document Types
export interface Document {
  id: string;
  student_id: string;
  google_doc_id?: string;
  doc_type:
    | 'essay'
    | 'personal_statement'
    | 'transcript'
    | 'activity_resume'
    | 'other';
  metadata?: DocumentMetadata;
  created_at: string;
  updated_at: string;
}

export interface DocumentMetadata {
  title?: string;
  school_id?: string;
  word_count?: number;
  status?: 'draft' | 'in_review' | 'completed';
  version?: number;
  last_edited_by?: string;
  comments_count?: number;
}

// Consultant Types
export interface ConsultantAvailability {
  timezone: string;
  weekly_hours: WeeklyHours;
  blackout_dates?: string[];
}

export interface WeeklyHours {
  monday?: TimeSlot[];
  tuesday?: TimeSlot[];
  wednesday?: TimeSlot[];
  thursday?: TimeSlot[];
  friday?: TimeSlot[];
  saturday?: TimeSlot[];
  sunday?: TimeSlot[];
}

export interface TimeSlot {
  start: string; // HH:MM format
  end: string; // HH:MM format
}

// Relationship Types
export interface StudentConsultantRelation {
  id: string;
  student_id: string;
  consultant_id: string;
  start_date: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

// API Response Types - Enhanced Standard Format
export interface BaseApiResponse {
  success: boolean;
  timestamp: string;
  request_id?: string;
}

export interface ApiSuccessResponse<T = any> extends BaseApiResponse {
  success: true;
  data: T;
  message?: string;
  meta?: {
    [key: string]: any;
  };
}

export interface ApiErrorResponse extends BaseApiResponse {
  success: false;
  error: {
    message: string;
    code?: string;
    details?: any;
    field?: string; // For validation errors
  };
}

export interface ApiValidationErrorResponse extends BaseApiResponse {
  success: false;
  error: {
    message: string;
    code: 'VALIDATION_ERROR';
    details: {
      field: string;
      message: string;
      value?: any;
    }[];
  };
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedApiResponse<T = any> extends ApiSuccessResponse<T[]> {
  data: T[];
  meta: {
    pagination: PaginationMeta;
    [key: string]: any;
  };
}

// Union type for all possible API responses
export type ApiResponse<T = any> =
  | ApiSuccessResponse<T>
  | ApiErrorResponse
  | ApiValidationErrorResponse;

// Legacy type for backward compatibility (will be deprecated)
export interface LegacyApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Type guards for response types
export function isApiSuccessResponse<T>(
  response: ApiResponse<T>
): response is ApiSuccessResponse<T> {
  return response.success === true;
}

export function isApiErrorResponse(
  response: ApiResponse
): response is ApiErrorResponse {
  return response.success === false && 'error' in response;
}

export function isApiValidationErrorResponse(
  response: ApiResponse
): response is ApiValidationErrorResponse {
  return (
    response.success === false &&
    'error' in response &&
    response.error.code === 'VALIDATION_ERROR'
  );
}

export function isPaginatedResponse<T>(
  response: ApiResponse<T[]>
): response is PaginatedApiResponse<T> {
  return (
    isApiSuccessResponse(response) &&
    response.meta !== undefined &&
    'pagination' in response.meta
  );
}
