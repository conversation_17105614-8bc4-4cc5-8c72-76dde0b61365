export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          clerk_id: string;
          email: string;
          role: 'student' | 'consultant' | 'admin';
          profile_data: <PERSON><PERSON> | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          clerk_id: string;
          email: string;
          role: 'student' | 'consultant' | 'admin';
          profile_data?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          clerk_id?: string;
          email?: string;
          role?: 'student' | 'consultant' | 'admin';
          profile_data?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      student_profiles: {
        Row: {
          id: string;
          user_id: string;
          academic_info: Json | null;
          target_schools: Json | null;
          application_status: Json | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          academic_info?: Json | null;
          target_schools?: J<PERSON> | null;
          application_status?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          academic_info?: Json | null;
          target_schools?: Json | null;
          application_status?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      consultants: {
        Row: {
          id: string;
          user_id: string;
          specialties: string[];
          availability: Json | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          specialties: string[];
          availability?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          specialties?: string[];
          availability?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      schools: {
        Row: {
          id: string;
          name: string;
          details: Json | null;
          application_requirements: Json | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          details?: Json | null;
          application_requirements?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          details?: Json | null;
          application_requirements?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      documents: {
        Row: {
          id: string;
          student_id: string;
          google_doc_id: string | null;
          doc_type:
            | 'essay'
            | 'personal_statement'
            | 'transcript'
            | 'activity_resume'
            | 'other';
          metadata: Json | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          student_id: string;
          google_doc_id?: string | null;
          doc_type:
            | 'essay'
            | 'personal_statement'
            | 'transcript'
            | 'activity_resume'
            | 'other';
          metadata?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          student_id?: string;
          google_doc_id?: string | null;
          doc_type?:
            | 'essay'
            | 'personal_statement'
            | 'transcript'
            | 'activity_resume'
            | 'other';
          metadata?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      student_consultant_relations: {
        Row: {
          id: string;
          student_id: string;
          consultant_id: string;
          start_date: string;
          status: 'active' | 'inactive';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          student_id: string;
          consultant_id: string;
          start_date: string;
          status?: 'active' | 'inactive';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          student_id?: string;
          consultant_id?: string;
          start_date?: string;
          status?: 'active' | 'inactive';
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      user_role: 'student' | 'consultant' | 'admin';
      document_type:
        | 'essay'
        | 'personal_statement'
        | 'transcript'
        | 'activity_resume'
        | 'other';
      relation_status: 'active' | 'inactive';
    };
  };
}
