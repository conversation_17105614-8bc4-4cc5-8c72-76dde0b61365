{"categories": {"arts": "Arts", "english": "English", "history_social_science": "History & Social Science", "math_computer_science": "Math & Computer Science", "sciences": "Sciences", "world_languages_cultures": "World Languages & Cultures"}, "classes": [{"id": "ap_art_history", "name": "AP Art History", "category": "arts", "description": "Explore art from around the world and across time"}, {"id": "ap_music_theory", "name": "AP Music Theory", "category": "arts", "description": "Develop musical skills and understanding"}, {"id": "ap_studio_art_2d", "name": "AP Studio Art: 2-D Art and Design", "category": "arts", "description": "Create a portfolio of 2-D artwork"}, {"id": "ap_studio_art_3d", "name": "AP Studio Art: 3-D Art and Design", "category": "arts", "description": "Create a portfolio of 3-D artwork"}, {"id": "ap_studio_art_drawing", "name": "AP Studio Art: Drawing", "category": "arts", "description": "Create a portfolio of drawing artwork"}, {"id": "ap_english_language", "name": "AP English Language and Composition", "category": "english", "description": "Analyze and write various forms of communication"}, {"id": "ap_english_literature", "name": "AP English Literature and Composition", "category": "english", "description": "Read and analyze imaginative literature"}, {"id": "ap_comparative_government", "name": "AP Comparative Government and Politics", "category": "history_social_science", "description": "Compare political systems around the world"}, {"id": "ap_european_history", "name": "AP European History", "category": "history_social_science", "description": "Explore European history from 1450 to present"}, {"id": "ap_human_geography", "name": "AP Human Geography", "category": "history_social_science", "description": "Study human population and its impact on Earth"}, {"id": "ap_macroeconomics", "name": "AP Macroeconomics", "category": "history_social_science", "description": "Study economic principles on a national level"}, {"id": "ap_microeconomics", "name": "AP Microeconomics", "category": "history_social_science", "description": "Study economic principles on an individual level"}, {"id": "ap_psychology", "name": "AP Psychology", "category": "history_social_science", "description": "Explore behavior and mental processes"}, {"id": "ap_us_government", "name": "AP United States Government and Politics", "category": "history_social_science", "description": "Study the U.S. political system"}, {"id": "ap_us_history", "name": "AP United States History", "category": "history_social_science", "description": "Explore U.S. history from 1491 to present"}, {"id": "ap_world_history", "name": "AP World History: Modern", "category": "history_social_science", "description": "Study world history from 1200 CE to present"}, {"id": "ap_calculus_ab", "name": "AP Calculus AB", "category": "math_computer_science", "description": "Study differential and integral calculus"}, {"id": "ap_calculus_bc", "name": "AP Calculus BC", "category": "math_computer_science", "description": "Advanced study of calculus topics"}, {"id": "ap_computer_science_a", "name": "AP Computer Science A", "category": "math_computer_science", "description": "Learn programming using Java"}, {"id": "ap_computer_science_principles", "name": "AP Computer Science Principles", "category": "math_computer_science", "description": "Explore computational thinking and programming"}, {"id": "ap_statistics", "name": "AP Statistics", "category": "math_computer_science", "description": "Study statistical analysis and data interpretation"}, {"id": "ap_biology", "name": "AP Biology", "category": "sciences", "description": "Study living systems and biological processes"}, {"id": "ap_chemistry", "name": "AP Chemistry", "category": "sciences", "description": "Study matter and chemical reactions"}, {"id": "ap_environmental_science", "name": "AP Environmental Science", "category": "sciences", "description": "Study environmental systems and human impact"}, {"id": "ap_physics_1", "name": "AP Physics 1: Algebra-Based", "category": "sciences", "description": "Study mechanics, waves, and electricity"}, {"id": "ap_physics_2", "name": "AP Physics 2: Algebra-Based", "category": "sciences", "description": "Study thermodynamics, optics, and modern physics"}, {"id": "ap_physics_c_electricity", "name": "AP Physics C: Electricity and Magnetism", "category": "sciences", "description": "Calculus-based study of electricity and magnetism"}, {"id": "ap_physics_c_mechanics", "name": "AP Physics C: Mechanics", "category": "sciences", "description": "Calculus-based study of mechanics"}, {"id": "ap_chinese", "name": "AP Chinese Language and Culture", "category": "world_languages_cultures", "description": "Develop Chinese language skills and cultural understanding"}, {"id": "ap_french", "name": "AP French Language and Culture", "category": "world_languages_cultures", "description": "Develop French language skills and cultural understanding"}, {"id": "ap_german", "name": "AP German Language and Culture", "category": "world_languages_cultures", "description": "Develop German language skills and cultural understanding"}, {"id": "ap_italian", "name": "AP Italian Language and Culture", "category": "world_languages_cultures", "description": "Develop Italian language skills and cultural understanding"}, {"id": "ap_japanese", "name": "AP Japanese Language and Culture", "category": "world_languages_cultures", "description": "Develop Japanese language skills and cultural understanding"}, {"id": "ap_latin", "name": "AP Latin", "category": "world_languages_cultures", "description": "Study Latin language and Roman culture"}, {"id": "ap_spanish_language", "name": "AP Spanish Language and Culture", "category": "world_languages_cultures", "description": "Develop Spanish language skills and cultural understanding"}, {"id": "ap_spanish_literature", "name": "AP Spanish Literature and Culture", "category": "world_languages_cultures", "description": "Study Spanish and Latin American literature"}]}