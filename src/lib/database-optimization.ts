/**
 * Database optimization utilities and query helpers
 */

import { supabaseAdmin } from './supabase';
import { log } from './logger';
import { perf } from './performance';

// Query optimization configuration
interface QueryConfig {
  enableProfiling: boolean;
  slowQueryThreshold: number; // milliseconds
  enableCaching: boolean;
  maxCacheSize: number;
}

const DEFAULT_CONFIG: QueryConfig = {
  enableProfiling: process.env.NODE_ENV === 'development',
  slowQueryThreshold: 1000, // 1 second
  enableCaching: true,
  maxCacheSize: 100,
};

// Query profiler
class QueryProfiler {
  private static instance: QueryProfiler;
  private config: QueryConfig;
  private queryCache: Map<string, any> = new Map();

  private constructor(config: Partial<QueryConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  public static getInstance(config?: Partial<QueryConfig>): QueryProfiler {
    if (!QueryProfiler.instance) {
      QueryProfiler.instance = new QueryProfiler(config);
    }
    return QueryProfiler.instance;
  }

  // Profile a query execution
  public async profileQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    cacheKey?: string
  ): Promise<T> {
    // Check cache first if enabled
    if (this.config.enableCaching && cacheKey && this.queryCache.has(cacheKey)) {
      log.debug(`Query cache hit: ${queryName}`, 'DB');
      return this.queryCache.get(cacheKey);
    }

    const timer = perf.timer(`db.${queryName}`);
    
    try {
      const result = await queryFn();
      const duration = timer.end();

      // Log slow queries
      if (duration > this.config.slowQueryThreshold) {
        log.warn(`Slow query detected: ${queryName} took ${duration}ms`, 'DB');
      }

      // Cache result if enabled
      if (this.config.enableCaching && cacheKey) {
        this.queryCache.set(cacheKey, result);
        
        // Enforce cache size limit
        if (this.queryCache.size > this.config.maxCacheSize) {
          const firstKey = this.queryCache.keys().next().value;
          this.queryCache.delete(firstKey);
        }
      }

      return result;
    } catch (error) {
      timer.end();
      log.error(`Query failed: ${queryName}`, 'DB', error as Error);
      throw error;
    }
  }

  // Clear query cache
  public clearCache(): void {
    this.queryCache.clear();
    log.debug('Query cache cleared', 'DB');
  }

  // Get cache statistics
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.queryCache.size,
      keys: Array.from(this.queryCache.keys()),
    };
  }
}

// Optimized query builders
export class OptimizedQueries {
  private profiler = QueryProfiler.getInstance();

  // Optimized user queries with proper indexing
  async getUserById(userId: string) {
    return this.profiler.profileQuery(
      'getUserById',
      async () => {
        const { data, error } = await supabaseAdmin
          .from('users')
          .select(`
            id,
            email,
            role,
            profile_data,
            created_at,
            last_login_at
          `)
          .eq('id', userId)
          .single();

        if (error) throw error;
        return data;
      },
      `user:${userId}`
    );
  }

  // Optimized student queries with joins
  async getStudentWithProfile(studentId: string) {
    return this.profiler.profileQuery(
      'getStudentWithProfile',
      async () => {
        const { data, error } = await supabaseAdmin
          .from('students')
          .select(`
            id,
            user_id,
            current_consultant_id,
            created_at,
            users!inner (
              id,
              email,
              profile_data
            ),
            consultants:current_consultant_id (
              id,
              users!inner (
                id,
                email,
                profile_data
              )
            )
          `)
          .eq('id', studentId)
          .single();

        if (error) throw error;
        return data;
      },
      `student:${studentId}`
    );
  }

  // Optimized document queries with pagination
  async getDocumentsPaginated(
    userId: string,
    page: number = 1,
    limit: number = 10,
    filters?: {
      status?: string;
      type?: string;
      school_id?: string;
    }
  ) {
    return this.profiler.profileQuery(
      'getDocumentsPaginated',
      async () => {
        let query = supabaseAdmin
          .from('documents')
          .select(`
            id,
            title,
            type,
            status,
            created_at,
            updated_at,
            word_limit,
            deadline,
            school_id,
            schools (
              id,
              name
            )
          `, { count: 'exact' })
          .eq('student_id', userId)
          .order('updated_at', { ascending: false });

        // Apply filters
        if (filters?.status) {
          query = query.eq('status', filters.status);
        }
        if (filters?.type) {
          query = query.eq('type', filters.type);
        }
        if (filters?.school_id) {
          query = query.eq('school_id', filters.school_id);
        }

        // Apply pagination
        const from = (page - 1) * limit;
        const to = from + limit - 1;
        query = query.range(from, to);

        const { data, error, count } = await query;

        if (error) throw error;
        return { data, count, page, limit };
      },
      `documents:${userId}:${page}:${limit}:${JSON.stringify(filters)}`
    );
  }

  // Optimized analytics queries
  async getDashboardStats(userId: string, role: string) {
    return this.profiler.profileQuery(
      'getDashboardStats',
      async () => {
        const stats: any = {};

        if (role === 'admin') {
          // Admin stats - use efficient counting
          const [usersCount, studentsCount, consultantsCount, documentsCount] = await Promise.all([
            supabaseAdmin.from('users').select('id', { count: 'exact', head: true }),
            supabaseAdmin.from('students').select('id', { count: 'exact', head: true }),
            supabaseAdmin.from('consultants').select('id', { count: 'exact', head: true }),
            supabaseAdmin.from('documents').select('id', { count: 'exact', head: true }),
          ]);

          stats.totalUsers = usersCount.count || 0;
          stats.totalStudents = studentsCount.count || 0;
          stats.totalConsultants = consultantsCount.count || 0;
          stats.totalDocuments = documentsCount.count || 0;
        } else if (role === 'consultant') {
          // Consultant stats
          const [studentsCount, documentsCount] = await Promise.all([
            supabaseAdmin
              .from('students')
              .select('id', { count: 'exact', head: true })
              .eq('current_consultant_id', userId),
            supabaseAdmin
              .from('documents')
              .select('id', { count: 'exact', head: true })
              .in('student_id', [userId]), // This would need a proper join
          ]);

          stats.assignedStudents = studentsCount.count || 0;
          stats.totalDocuments = documentsCount.count || 0;
        } else {
          // Student stats
          const [documentsCount, activitiesCount, schoolsCount] = await Promise.all([
            supabaseAdmin
              .from('documents')
              .select('id', { count: 'exact', head: true })
              .eq('student_id', userId),
            supabaseAdmin
              .from('activities')
              .select('id', { count: 'exact', head: true })
              .eq('student_id', userId),
            supabaseAdmin
              .from('target_schools')
              .select('id', { count: 'exact', head: true })
              .eq('student_id', userId),
          ]);

          stats.totalDocuments = documentsCount.count || 0;
          stats.totalActivities = activitiesCount.count || 0;
          stats.targetSchools = schoolsCount.count || 0;
        }

        return stats;
      },
      `dashboard-stats:${userId}:${role}`
    );
  }

  // Batch operations for better performance
  async batchUpdateDocuments(updates: Array<{ id: string; data: any }>) {
    return this.profiler.profileQuery(
      'batchUpdateDocuments',
      async () => {
        const promises = updates.map(({ id, data }) =>
          supabaseAdmin
            .from('documents')
            .update(data)
            .eq('id', id)
        );

        const results = await Promise.all(promises);
        return results;
      }
    );
  }

  // Clear all caches
  clearCache() {
    this.profiler.clearCache();
  }
}

// Database indexing recommendations
export const RECOMMENDED_INDEXES = [
  // Users table
  'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);',
  'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);',
  'CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);',
  
  // Students table
  'CREATE INDEX IF NOT EXISTS idx_students_user_id ON students(user_id);',
  'CREATE INDEX IF NOT EXISTS idx_students_consultant_id ON students(current_consultant_id);',
  
  // Documents table
  'CREATE INDEX IF NOT EXISTS idx_documents_student_id ON documents(student_id);',
  'CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);',
  'CREATE INDEX IF NOT EXISTS idx_documents_type ON documents(type);',
  'CREATE INDEX IF NOT EXISTS idx_documents_school_id ON documents(school_id);',
  'CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at);',
  'CREATE INDEX IF NOT EXISTS idx_documents_updated_at ON documents(updated_at);',
  
  // Activities table
  'CREATE INDEX IF NOT EXISTS idx_activities_student_id ON activities(student_id);',
  'CREATE INDEX IF NOT EXISTS idx_activities_category ON activities(category);',
  
  // Target schools table
  'CREATE INDEX IF NOT EXISTS idx_target_schools_student_id ON target_schools(student_id);',
  'CREATE INDEX IF NOT EXISTS idx_target_schools_school_id ON target_schools(school_id);',
  
  // Composite indexes for common queries
  'CREATE INDEX IF NOT EXISTS idx_documents_student_status ON documents(student_id, status);',
  'CREATE INDEX IF NOT EXISTS idx_documents_student_updated ON documents(student_id, updated_at DESC);',
];

// Export singleton instance
export const optimizedQueries = new OptimizedQueries();

// Utility function to apply recommended indexes
export async function applyRecommendedIndexes() {
  log.info('Applying recommended database indexes', 'DB');
  
  for (const indexSql of RECOMMENDED_INDEXES) {
    try {
      await supabaseAdmin.rpc('exec_sql', { sql: indexSql });
      log.debug(`Applied index: ${indexSql}`, 'DB');
    } catch (error) {
      log.warn(`Failed to apply index: ${indexSql}`, 'DB', error);
    }
  }
  
  log.info('Finished applying recommended indexes', 'DB');
}
