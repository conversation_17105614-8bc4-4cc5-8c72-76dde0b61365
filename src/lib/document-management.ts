import { supabaseAdmin } from '@/lib/supabase';
import {
  GoogleDocsService,
  GoogleDriveService,
  DocumentPermissionService
} from '@/lib/google-apis';
import { TemplateService } from '@/lib/template-service';
import { log } from '@/lib/logger';

export interface DocumentMetadata {
  title: string;
  school_id?: string;
  word_limit?: number;
  deadline?: string;
  status?: 'draft' | 'in_review' | 'completed';
  google_doc_url?: string;
  folder_id?: string;
}

export interface CreateDocumentRequest {
  student_id: string;
  doc_type:
    | 'essay'
    | 'personal_statement'
    | 'transcript'
    | 'activity_resume'
    | 'other';
  metadata: DocumentMetadata;
  template_id?: string;
  initial_content?: string;
}

export interface DocumentWithPermissions {
  id: string;
  student_id: string;
  google_doc_id: string | null;
  doc_type: string;
  metadata: DocumentMetadata;
  created_at: string;
  updated_at: string;
  student_name?: string;
  student_email?: string;
  consultant_emails?: string[];
}

/**
 * Comprehensive document management service that integrates Google Docs with Supabase
 * Implements the Lighten admin ownership model with role-based permissions
 */
export class DocumentManagementService {
  private static supabase = supabaseAdmin;

  /**
   * Create a new document with proper Google Docs integration and permissions
   */
  static async createDocument(
    request: CreateDocumentRequest
  ): Promise<DocumentWithPermissions> {
    try {
      // 1. Get student information
      const { data: studentProfile, error: studentError } = await supabaseAdmin
        .from('student_profiles')
        .select(
          `
          id,
          user_id,
          users!inner(email, profile_data)
        `
        )
        .eq('id', request.student_id)
        .single();

      if (studentError || !studentProfile) {
        throw new Error('Student not found');
      }

      const studentEmail =
        (studentProfile.users as any)?.email ||
        (studentProfile.users as any)[0]?.email;
      const studentName =
        (studentProfile.users as any)?.profile_data?.name ||
        (studentProfile.users as any)[0]?.profile_data?.name ||
        ((studentProfile.users as any)?.profile_data?.firstName ||
          (studentProfile.users as any)[0]?.profile_data?.firstName) +
          ' ' +
          ((studentProfile.users as any)?.profile_data?.lastName ||
            (studentProfile.users as any)[0]?.profile_data?.lastName) ||
        'Student';

      // 2. Get assigned consultant(s)
      const { data: consultantRelations } = await this.supabase
        .from('student_consultant_relations')
        .select(
          `
          consultants!inner(
            user_id,
            users!inner(email)
          )
        `
        )
        .eq('student_id', request.student_id)
        .eq('status', 'active');

      const consultantEmails =
        consultantRelations?.map(
          (rel) =>
            (rel.consultants as any)?.users?.email ||
            (rel.consultants as any)?.users?.[0]?.email
        ) || [];

      // 3. Create document record in database first
      const { data: document, error: dbError } = await supabaseAdmin
        .from('documents')
        .insert({
          student_id: request.student_id,
          doc_type: request.doc_type,
          metadata: request.metadata
        })
        .select()
        .single();

      if (dbError || !document) {
        throw new Error('Failed to create document record');
      }

      // 4. Create Google Doc with Lighten admin ownership
      const { documentId, documentUrl, folderId } =
        await GoogleDocsService.createDocument(
          request.metadata.title,
          studentName,
          request.doc_type,
          request.initial_content,
          request.template_id
        );

      // 5. Set up role-based permissions
      await DocumentPermissionService.setupDocumentPermissions(
        documentId,
        studentEmail,
        consultantEmails,
        [] // Additional admin emails can be added here
      );

      // 6. Update document record with Google Docs information
      const updatedMetadata = {
        ...request.metadata,
        google_doc_url: documentUrl,
        folder_id: folderId
      };

      const { data: updatedDocument, error: updateError } = await this.supabase
        .from('documents')
        .update({
          google_doc_id: documentId,
          metadata: updatedMetadata
        })
        .eq('id', document.id)
        .select()
        .single();

      if (updateError) {
        // If database update fails, we should clean up the Google Doc
        // But for now, we'll log the error and continue
        log.error(
          'Failed to update document with Google Docs info',
          'DOCS',
          updateError as Error
        );
      }

      // Track template usage if a template was used
      if (request.template_id) {
        try {
          await TemplateService.trackTemplateUsage(
            request.template_id,
            request.student_id,
            document.id
          );
        } catch (templateError) {
          log.error('Error tracking template usage', 'DOCS', templateError as Error);
          // Don't fail document creation if template tracking fails
        }
      }

      return {
        ...document,
        google_doc_id: documentId,
        metadata: updatedMetadata,
        student_name: studentName,
        student_email: studentEmail,
        consultant_emails: consultantEmails
      };
    } catch (error) {
      log.error('Error creating document', 'DOCS', error as Error);
      throw new Error(
        `Failed to create document: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Get documents for a user based on their role
   */
  static async getDocumentsForUser(
    userId: string,
    userRole: 'student' | 'consultant' | 'admin'
  ): Promise<DocumentWithPermissions[]> {
    try {
      let query = this.supabase.from('documents').select(`
          *,
          student_profiles!inner(
            id,
            users!inner(email, profile_data)
          )
        `);

      if (userRole === 'student') {
        // Students can only see their own documents
        const { data: studentProfile } = await this.supabase
          .from('student_profiles')
          .select('id')
          .eq('user_id', userId)
          .single();

        if (!studentProfile) {
          return [];
        }

        query = query.eq('student_id', studentProfile.id);
      } else if (userRole === 'consultant') {
        // Consultants can see documents of their assigned students
        const { data: consultantProfile } = await this.supabase
          .from('consultants')
          .select('id')
          .eq('user_id', userId)
          .single();

        if (!consultantProfile) {
          return [];
        }

        // Get student IDs for this consultant
        const { data: studentRelations } = await this.supabase
          .from('student_consultant_relations')
          .select('student_id')
          .eq('consultant_id', consultantProfile.id)
          .eq('status', 'active');

        const studentIds = studentRelations?.map((rel) => rel.student_id) || [];

        if (studentIds.length === 0) {
          return [];
        }

        query = query.in('student_id', studentIds);
      }
      // Admin can see all documents (no additional filter needed)

      const { data: documents, error } = await query;

      if (error) {
        throw new Error('Failed to fetch documents');
      }

      return (
        documents?.map((doc) => ({
          ...doc,
          student_name:
            doc.student_profiles.users.profile_data?.name || 'Student',
          student_email: doc.student_profiles.users.email
        })) || []
      );
    } catch (error) {
      console.error('Error fetching documents:', error);
      throw new Error(
        `Failed to fetch documents: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Update consultant assignment for a student's documents
   */
  static async updateConsultantAssignment(
    studentId: string,
    oldConsultantId?: string,
    newConsultantId?: string
  ): Promise<void> {
    try {
      // Get all documents for the student
      const { data: documents } = await this.supabase
        .from('documents')
        .select('google_doc_id')
        .eq('student_id', studentId)
        .not('google_doc_id', 'is', null);

      if (!documents || documents.length === 0) {
        return; // No documents to update
      }

      // Get consultant emails
      let oldConsultantEmail: string | undefined;
      let newConsultantEmail: string | undefined;

      if (oldConsultantId) {
        const { data: oldConsultant } = await this.supabase
          .from('consultants')
          .select('users!inner(email)')
          .eq('id', oldConsultantId)
          .single();
        oldConsultantEmail =
          (oldConsultant?.users as any)?.email ||
          (oldConsultant?.users as any)?.[0]?.email;
      }

      if (newConsultantId) {
        const { data: newConsultant } = await this.supabase
          .from('consultants')
          .select('users!inner(email)')
          .eq('id', newConsultantId)
          .single();
        newConsultantEmail =
          (newConsultant?.users as any)?.email ||
          (newConsultant?.users as any)?.[0]?.email;
      }

      // Update permissions for all student documents
      for (const document of documents) {
        if (document.google_doc_id) {
          await DocumentPermissionService.updateConsultantAccess(
            document.google_doc_id,
            oldConsultantEmail,
            newConsultantEmail
          );
        }
      }
    } catch (error) {
      console.error('Error updating consultant assignment:', error);
      throw new Error('Failed to update consultant assignment');
    }
  }

  /**
   * Delete a document (archives in Google Drive, removes from database)
   */
  static async deleteDocument(
    documentId: string,
    userId: string,
    userRole: 'student' | 'consultant' | 'admin'
  ): Promise<void> {
    try {
      // Get document with permissions check
      const { data: document, error } = await this.supabase
        .from('documents')
        .select(
          `
          *,
          student_profiles!inner(user_id)
        `
        )
        .eq('id', documentId)
        .single();

      if (error || !document) {
        throw new Error('Document not found');
      }

      // Permission check
      if (
        userRole === 'student' &&
        document.student_profiles.user_id !== userId
      ) {
        throw new Error('Permission denied');
      }

      if (userRole === 'consultant') {
        // Check if consultant is assigned to this student
        const { data: consultantProfile } = await this.supabase
          .from('consultants')
          .select('id')
          .eq('user_id', userId)
          .single();

        if (consultantProfile) {
          const { data: relation } = await this.supabase
            .from('student_consultant_relations')
            .select('id')
            .eq('student_id', document.student_id)
            .eq('consultant_id', consultantProfile.id)
            .eq('status', 'active')
            .single();

          if (!relation) {
            throw new Error('Permission denied');
          }
        } else {
          throw new Error('Permission denied');
        }
      }

      // Archive Google Doc (move to archive folder instead of deleting)
      if (document.google_doc_id) {
        // TODO: Implement archive functionality
        // For now, we'll just remove it from the current location
        // await GoogleDriveService.deleteFile(document.google_doc_id);
      }

      // Remove from database
      const { error: deleteError } = await this.supabase
        .from('documents')
        .delete()
        .eq('id', documentId);

      if (deleteError) {
        throw new Error('Failed to delete document record');
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      throw new Error(
        `Failed to delete document: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Copy an existing document for a different school/purpose
   */
  static async copyDocument(
    sourceDocumentId: string,
    newTitle: string,
    studentId: string
  ): Promise<{ google_doc_id: string; google_doc_url: string }> {
    try {
      // Get student information
      const { data: studentProfile, error: studentError } = await supabaseAdmin
        .from('student_profiles')
        .select(
          `
          id,
          user_id,
          users!inner(email, profile_data)
        `
        )
        .eq('id', studentId)
        .single();

      if (studentError || !studentProfile) {
        throw new Error('Student not found');
      }

      const studentName = (studentProfile.users as any)?.profile_data?.name || 'Student';

      // Create a copy of the document using GoogleDocsService approach
      const { documentId, documentUrl } = await GoogleDocsService.createDocument(
        newTitle,
        studentName,
        'essay', // Default to essay type for copied documents
        undefined, // No initial content
        sourceDocumentId // Use source as template
      );

      return {
        google_doc_id: documentId,
        google_doc_url: documentUrl
      };
    } catch (error) {
      console.error('Error copying document:', error);
      throw new Error(
        `Failed to copy document: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Get document by ID with permission check
   */
  static async getDocument(
    documentId: string,
    userId: string,
    userRole: 'student' | 'consultant' | 'admin'
  ): Promise<DocumentWithPermissions | null> {
    try {
      const { data: document, error } = await this.supabase
        .from('documents')
        .select(
          `
          *,
          student_profiles!inner(
            id,
            user_id,
            users!inner(email, profile_data)
          )
        `
        )
        .eq('id', documentId)
        .single();

      if (error || !document) {
        return null;
      }

      // Permission check
      if (
        userRole === 'student' &&
        document.student_profiles.user_id !== userId
      ) {
        throw new Error('Permission denied');
      }

      if (userRole === 'consultant') {
        // Check if consultant is assigned to this student
        const { data: consultantProfile } = await this.supabase
          .from('consultants')
          .select('id')
          .eq('user_id', userId)
          .single();

        if (consultantProfile) {
          const { data: relation } = await this.supabase
            .from('student_consultant_relations')
            .select('id')
            .eq('student_id', document.student_id)
            .eq('consultant_id', consultantProfile.id)
            .eq('status', 'active')
            .single();

          if (!relation) {
            throw new Error('Permission denied');
          }
        } else {
          throw new Error('Permission denied');
        }
      }

      return {
        ...document,
        student_name:
          document.student_profiles.users.profile_data?.name || 'Student',
        student_email: document.student_profiles.users.email
      };
    } catch (error) {
      console.error('Error fetching document:', error);
      throw new Error(
        `Failed to fetch document: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }
}
