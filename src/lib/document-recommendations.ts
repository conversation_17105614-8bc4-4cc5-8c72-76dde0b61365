/**
 * Document Recommendations Service
 * Provides flexible guidance and recommendations for document creation
 * Part of Goal #6: Document Creation Flexibility
 */

export type DocumentType = 
  | 'essay' 
  | 'personal_statement' 
  | 'transcript' 
  | 'activity_resume' 
  | 'other';

export type CreationMethod = 'free_form' | 'template_based' | 'blank_document';

export interface DocumentRecommendations {
  word_limit?: number;
  word_limit_note?: string;
  suggested_formats: ('PDF' | 'Word' | 'Google_Docs')[];
  guidelines: string[];
  examples: string[];
  tips: string[];
  common_mistakes: string[];
}

export interface DocumentCreationOptions {
  type: DocumentType;
  creation_method: CreationMethod;
  recommendations: DocumentRecommendations;
  template_id?: string;
  custom_title: string;
  custom_content?: string;
}

export class DocumentRecommendationService {
  /**
   * Get recommendations for a specific document type
   */
  static getRecommendations(docType: DocumentType): DocumentRecommendations {
    const recommendations: Record<DocumentType, DocumentRecommendations> = {
      personal_statement: {
        word_limit: 650,
        word_limit_note: "Most colleges prefer 500-650 words, but check specific requirements",
        suggested_formats: ['Google_Docs', 'Word', 'PDF'],
        guidelines: [
          "Tell a compelling personal story that reveals who you are",
          "Show, don't just tell - use specific examples and anecdotes",
          "Connect your experiences to your future goals",
          "Be authentic and write in your own voice",
          "Address the prompt directly while staying true to your story"
        ],
        examples: [
          "A challenge you overcame and what you learned",
          "A moment that changed your perspective",
          "A passion project that defines you",
          "How your background shapes your worldview"
        ],
        tips: [
          "Start with a hook that draws the reader in",
          "Use active voice and varied sentence structure",
          "End with a strong conclusion that ties back to the beginning",
          "Have multiple people review your draft"
        ],
        common_mistakes: [
          "Writing what you think admissions wants to hear",
          "Focusing too much on achievements rather than personal growth",
          "Using clichés or overly formal language",
          "Not proofreading for grammar and spelling errors"
        ]
      },
      
      essay: {
        word_limit: 250,
        word_limit_note: "Supplemental essays typically range from 150-500 words - check specific prompts",
        suggested_formats: ['Google_Docs', 'Word', 'PDF'],
        guidelines: [
          "Answer the specific question asked",
          "Research the school to show genuine interest",
          "Be specific about programs, professors, or opportunities",
          "Connect your interests to what the school offers",
          "Keep it concise and focused"
        ],
        examples: [
          "Why this major/school appeals to you",
          "How you'll contribute to campus community",
          "A specific program or research opportunity you're excited about",
          "How the school aligns with your career goals"
        ],
        tips: [
          "Avoid generic statements that could apply to any school",
          "Use specific names of programs, professors, or facilities",
          "Show enthusiasm and genuine interest",
          "Connect back to your personal experiences"
        ],
        common_mistakes: [
          "Writing generic responses that could work for any school",
          "Focusing only on rankings or prestige",
          "Not doing enough research about the school",
          "Exceeding the word limit"
        ]
      },

      activity_resume: {
        word_limit_note: "Focus on quality over quantity - highlight your most meaningful activities",
        suggested_formats: ['Google_Docs', 'PDF', 'Word'],
        guidelines: [
          "List activities in order of importance to you",
          "Use action verbs to describe your role and impact",
          "Quantify your achievements when possible",
          "Show progression and leadership development",
          "Include both school and outside activities"
        ],
        examples: [
          "Leadership positions with specific accomplishments",
          "Community service with hours and impact",
          "Work experience with responsibilities and skills gained",
          "Creative pursuits with recognition or growth"
        ],
        tips: [
          "Use bullet points for easy reading",
          "Start each description with a strong action verb",
          "Include awards, recognition, and measurable outcomes",
          "Show variety in your interests and commitments"
        ],
        common_mistakes: [
          "Listing activities without describing impact",
          "Including too many similar activities",
          "Not showing leadership or growth over time",
          "Forgetting to include hours per week and weeks per year"
        ]
      },

      transcript: {
        word_limit_note: "Official transcripts are typically provided by your school",
        suggested_formats: ['PDF', 'Word', 'Google_Docs'],
        guidelines: [
          "Request official transcripts from your school registrar",
          "Include all high school coursework and grades",
          "Note any special circumstances affecting grades",
          "Highlight challenging coursework (AP, IB, Honors)",
          "Ensure all information is accurate and complete"
        ],
        examples: [
          "Official transcript from school registrar",
          "Mid-year grade report for senior year",
          "Final transcript after graduation",
          "International transcript with grade conversion"
        ],
        tips: [
          "Request transcripts well in advance of deadlines",
          "Check that all courses and grades are correct",
          "Include explanation for any grade drops or gaps",
          "Provide context for your school's grading system if needed"
        ],
        common_mistakes: [
          "Waiting too long to request official transcripts",
          "Not checking transcript accuracy before submission",
          "Forgetting to send updated transcripts with senior grades",
          "Not providing context for unique grading systems"
        ]
      },

      other: {
        word_limit_note: "Word limits vary greatly - check specific requirements for your document type",
        suggested_formats: ['Google_Docs', 'Word', 'PDF'],
        guidelines: [
          "Clearly define the purpose of your document",
          "Follow any specific formatting requirements",
          "Organize information logically and clearly",
          "Use appropriate tone for your audience",
          "Proofread carefully for errors"
        ],
        examples: [
          "Research abstract or portfolio",
          "Creative writing sample",
          "Additional information document",
          "Letter of continued interest"
        ],
        tips: [
          "Research the specific requirements for your document type",
          "Ask for clarification if requirements are unclear",
          "Follow standard formatting unless told otherwise",
          "Have someone review your work before submitting"
        ],
        common_mistakes: [
          "Not following specific formatting guidelines",
          "Including irrelevant or excessive information",
          "Using inappropriate tone for the context",
          "Not proofreading thoroughly"
        ]
      }
    };

    return recommendations[docType] || recommendations.other;
  }

  /**
   * Get creation method descriptions
   */
  static getCreationMethodInfo(method: CreationMethod): {
    title: string;
    description: string;
    icon: string;
  } {
    const methodInfo = {
      free_form: {
        title: "Free-Form Creation",
        description: "Start with a blank document and write freely. Perfect for unique ideas or when you prefer complete creative control.",
        icon: "edit"
      },
      template_based: {
        title: "Template-Based",
        description: "Use a proven template as your starting point. Great for structure and guidance while maintaining flexibility.",
        icon: "fileText"
      },
      blank_document: {
        title: "Blank Document",
        description: "Create a completely empty document with just a title. Ideal for when you want to start fresh.",
        icon: "file"
      }
    };

    return methodInfo[method];
  }

  /**
   * Get flexible document type options (allows custom types)
   */
  static getDocumentTypeOptions(): Array<{
    value: string;
    label: string;
    description: string;
  }> {
    return [
      {
        value: 'personal_statement',
        label: 'Personal Statement',
        description: 'Main college application essay'
      },
      {
        value: 'essay',
        label: 'Supplemental Essay',
        description: 'School-specific essay questions'
      },
      {
        value: 'activity_resume',
        label: 'Activity Resume',
        description: 'Summary of extracurricular activities'
      },
      {
        value: 'transcript',
        label: 'Academic Transcript',
        description: 'Official record of coursework and grades'
      },
      {
        value: 'other',
        label: 'Other Document',
        description: 'Custom document type'
      }
    ];
  }
}
