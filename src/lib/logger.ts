/**
 * Centralized logging utility for the Lighten Counsel application
 * Provides structured logging with different levels and environment-aware output
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: string;
  data?: any;
  error?: Error;
}

class Logger {
  private static instance: Logger;
  private logLevel: LogLevel;

  private constructor() {
    // Set log level based on environment
    this.logLevel = process.env.NODE_ENV === 'production' ? LogLevel.WARN : LogLevel.DEBUG;
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private formatLogEntry(entry: LogEntry): string {
    const levelName = LogLevel[entry.level];
    let message = `[${entry.timestamp}] ${levelName}`;
    
    if (entry.context) {
      message += ` [${entry.context}]`;
    }
    
    message += `: ${entry.message}`;
    
    return message;
  }

  private createLogEntry(level: LogLevel, message: string, context?: string, data?: any, error?: Error): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      data,
      error,
    };
  }

  private log(entry: LogEntry): void {
    if (!this.shouldLog(entry.level)) {
      return;
    }

    const formattedMessage = this.formatLogEntry(entry);

    // In development, use console methods for better formatting
    if (process.env.NODE_ENV !== 'production') {
      switch (entry.level) {
        case LogLevel.DEBUG:
          console.debug(formattedMessage, entry.data || '');
          break;
        case LogLevel.INFO:
          console.info(formattedMessage, entry.data || '');
          break;
        case LogLevel.WARN:
          console.warn(formattedMessage, entry.data || '');
          break;
        case LogLevel.ERROR:
          console.error(formattedMessage, entry.data || '', entry.error || '');
          break;
      }
    } else {
      // In production, you might want to send logs to a service
      // For now, we'll still use console but could be replaced with external logging
      switch (entry.level) {
        case LogLevel.WARN:
          console.warn(formattedMessage);
          break;
        case LogLevel.ERROR:
          console.error(formattedMessage, entry.error);
          break;
      }
    }
  }

  public debug(message: string, context?: string, data?: any): void {
    const entry = this.createLogEntry(LogLevel.DEBUG, message, context, data);
    this.log(entry);
  }

  public info(message: string, context?: string, data?: any): void {
    const entry = this.createLogEntry(LogLevel.INFO, message, context, data);
    this.log(entry);
  }

  public warn(message: string, context?: string, data?: any): void {
    const entry = this.createLogEntry(LogLevel.WARN, message, context, data);
    this.log(entry);
  }

  public error(message: string, context?: string, error?: Error, data?: any): void {
    const entry = this.createLogEntry(LogLevel.ERROR, message, context, data, error);
    this.log(entry);
  }

  // Convenience methods for common use cases
  public apiRequest(method: string, url: string, data?: any): void {
    this.debug(`${method} ${url}`, 'API', data);
  }

  public apiResponse(method: string, url: string, status: number, data?: any): void {
    const level = status >= 400 ? LogLevel.ERROR : LogLevel.DEBUG;
    const message = `${method} ${url} - ${status}`;
    
    if (level === LogLevel.ERROR) {
      this.error(message, 'API', undefined, data);
    } else {
      this.debug(message, 'API', data);
    }
  }

  public apiError(method: string, url: string, error: Error): void {
    this.error(`${method} ${url} failed`, 'API', error);
  }

  public dbQuery(query: string, params?: any): void {
    this.debug(`Database query: ${query}`, 'DB', params);
  }

  public dbError(query: string, error: Error): void {
    this.error(`Database query failed: ${query}`, 'DB', error);
  }

  public authEvent(event: string, userId?: string, data?: any): void {
    this.info(`Auth event: ${event}`, 'AUTH', { userId, ...data });
  }

  public documentEvent(event: string, documentId: string, userId?: string, data?: any): void {
    this.info(`Document event: ${event}`, 'DOCS', { documentId, userId, ...data });
  }

  public googleApiEvent(event: string, data?: any): void {
    this.debug(`Google API event: ${event}`, 'GOOGLE', data);
  }

  public googleApiError(event: string, error: Error): void {
    this.error(`Google API error: ${event}`, 'GOOGLE', error);
  }
}

// Export singleton instance
export const logger = Logger.getInstance();

// Export convenience functions for easier usage
export const log = {
  debug: (message: string, context?: string, data?: any) => logger.debug(message, context, data),
  info: (message: string, context?: string, data?: any) => logger.info(message, context, data),
  warn: (message: string, context?: string, data?: any) => logger.warn(message, context, data),
  error: (message: string, context?: string, error?: Error, data?: any) => logger.error(message, context, error, data),
  
  // Convenience methods
  apiRequest: (method: string, url: string, data?: any) => logger.apiRequest(method, url, data),
  apiResponse: (method: string, url: string, status: number, data?: any) => logger.apiResponse(method, url, status, data),
  apiError: (method: string, url: string, error: Error) => logger.apiError(method, url, error),
  dbQuery: (query: string, params?: any) => logger.dbQuery(query, params),
  dbError: (query: string, error: Error) => logger.dbError(query, error),
  authEvent: (event: string, userId?: string, data?: any) => logger.authEvent(event, userId, data),
  documentEvent: (event: string, documentId: string, userId?: string, data?: any) => logger.documentEvent(event, documentId, userId, data),
  googleApiEvent: (event: string, data?: any) => logger.googleApiEvent(event, data),
  googleApiError: (event: string, error: Error) => logger.googleApiError(event, error),
};
