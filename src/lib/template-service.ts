import { supabase as supabaseClient } from '@/lib/supabase';
import { GoogleDriveService, GoogleDocsService } from '@/lib/google-apis';

export interface TemplateUsageData {
  template_id: string;
  student_id: string;
  document_id: string;
  used_at: string;
}

export interface TemplateRecommendation {
  template_id: string;
  name: string;
  description: string;
  category: string;
  doc_type: string;
  usage_count: number;
  difficulty_level: string;
  match_score: number;
  reasons: string[];
}

/**
 * Service for managing templates, tracking usage, and providing recommendations
 */
export class TemplateService {
  private static supabase = supabaseClient;

  /**
   * Track template usage when a document is created from a template
   */
  static async trackTemplateUsage(
    templateId: string,
    studentId: string,
    documentId: string
  ): Promise<void> {
    try {
      // Increment usage count in template metadata
      // First get the current usage count
      const { data: currentTemplate } = await this.supabase
        .from('template_metadata')
        .select('usage_count')
        .eq('google_doc_id', templateId)
        .single();

      const newUsageCount = (currentTemplate?.usage_count || 0) + 1;

      const { error: updateError } = await this.supabase
        .from('template_metadata')
        .update({
          usage_count: newUsageCount,
          updated_at: new Date().toISOString()
        })
        .eq('google_doc_id', templateId);

      if (updateError) {
        console.error('Error updating template usage count:', updateError);
      }

      // Record individual usage for analytics
      const { error: insertError } = await this.supabase
        .from('template_usage')
        .insert({
          template_id: templateId,
          student_id: studentId,
          document_id: documentId,
          used_at: new Date().toISOString()
        });

      if (insertError) {
        console.error('Error recording template usage:', insertError);
      }
    } catch (error) {
      console.error('Error tracking template usage:', error);
      // Don't throw error as this is a tracking operation
    }
  }

  /**
   * Get template recommendations for a student based on their profile and document type
   */
  static async getTemplateRecommendations(
    studentId: string,
    docType:
      | 'essay'
      | 'personal_statement'
      | 'transcript'
      | 'activity_resume'
      | 'other',
    limit: number = 5
  ): Promise<TemplateRecommendation[]> {
    try {
      // Get student profile for personalization
      const { data: studentProfile } = await this.supabase
        .from('student_profiles')
        .select(
          `
          academic_info,
          target_schools,
          users!inner(profile_data)
        `
        )
        .eq('id', studentId)
        .single();

      // Get templates for the specified document type
      const { data: templates } = await this.supabase
        .from('template_metadata')
        .select('*')
        .eq('doc_type', docType)
        .order('usage_count', { ascending: false });

      if (!templates || templates.length === 0) {
        return [];
      }

      // Get student's previous template usage
      const { data: previousUsage } = await this.supabase
        .from('template_usage')
        .select('template_id')
        .eq('student_id', studentId);

      const usedTemplateIds = new Set(
        previousUsage?.map((usage) => usage.template_id) || []
      );

      // Calculate recommendation scores
      const recommendations = templates.map((template) => {
        let matchScore = 0;
        const reasons: string[] = [];

        // Base score from popularity
        matchScore += Math.min(template.usage_count / 10, 5);

        // Boost featured templates
        if (template.is_featured) {
          matchScore += 3;
          reasons.push('Featured template');
        }

        // Difficulty level matching (simplified logic)
        const studentLevel = this.estimateStudentLevel(studentProfile);
        if (template.difficulty_level === studentLevel) {
          matchScore += 2;
          reasons.push(`Matches your ${studentLevel} level`);
        }

        // Category matching based on target schools
        if (
          studentProfile?.target_schools &&
          Array.isArray(studentProfile.target_schools)
        ) {
          const hasCompetitiveSchools = studentProfile.target_schools.some(
            (school: any) =>
              school.tier === 'highly_competitive' ||
              school.tier === 'most_competitive'
          );

          if (hasCompetitiveSchools && template.category === 'competitive') {
            matchScore += 2;
            reasons.push('Suitable for competitive schools');
          }
        }

        // Penalize if already used
        if (usedTemplateIds.has(template.google_doc_id)) {
          matchScore -= 1;
          reasons.push('Previously used');
        }

        // Word limit consideration
        if (template.word_limit) {
          reasons.push(`${template.word_limit} word limit`);
        }

        return {
          template_id: template.google_doc_id,
          name: template.name,
          description: template.description || '',
          category: template.category,
          doc_type: template.doc_type,
          usage_count: template.usage_count,
          difficulty_level: template.difficulty_level,
          match_score: Math.round(matchScore * 10) / 10,
          reasons: reasons.slice(0, 3) // Limit to top 3 reasons
        };
      });

      // Sort by match score and return top recommendations
      return recommendations
        .sort((a, b) => b.match_score - a.match_score)
        .slice(0, limit);
    } catch (error) {
      console.error('Error getting template recommendations:', error);
      return [];
    }
  }

  /**
   * Get template analytics for admin dashboard
   */
  static async getTemplateAnalytics(): Promise<{
    total_templates: number;
    total_usage: number;
    most_popular: Array<{
      template_id: string;
      name: string;
      usage_count: number;
      category: string;
    }>;
    usage_by_type: Record<string, number>;
    usage_by_category: Record<string, number>;
    recent_usage: Array<{
      template_name: string;
      student_name: string;
      used_at: string;
    }>;
  }> {
    try {
      // Get template counts and usage
      const [templatesResult, usageResult] = await Promise.all([
        this.supabase.from('template_metadata').select('*'),
        this.supabase
          .from('template_usage')
          .select(
            `
          *,
          template_metadata!inner(name, category, doc_type),
          student_profiles!inner(
            users!inner(profile_data)
          )
        `
          )
          .order('used_at', { ascending: false })
          .limit(10)
      ]);

      const templates = templatesResult.data || [];
      const recentUsage = usageResult.data || [];

      // Calculate statistics
      const totalUsage = templates.reduce(
        (sum, template) => sum + template.usage_count,
        0
      );

      const mostPopular = templates
        .sort((a, b) => b.usage_count - a.usage_count)
        .slice(0, 5)
        .map((template) => ({
          template_id: template.google_doc_id,
          name: template.name,
          usage_count: template.usage_count,
          category: template.category
        }));

      const usageByType = templates.reduce(
        (acc, template) => {
          acc[template.doc_type] =
            (acc[template.doc_type] || 0) + template.usage_count;
          return acc;
        },
        {} as Record<string, number>
      );

      const usageByCategory = templates.reduce(
        (acc, template) => {
          acc[template.category] =
            (acc[template.category] || 0) + template.usage_count;
          return acc;
        },
        {} as Record<string, number>
      );

      const recentUsageFormatted = recentUsage.map((usage) => ({
        template_name: usage.template_metadata.name,
        student_name:
          usage.student_profiles.users.profile_data?.name || 'Student',
        used_at: usage.used_at
      }));

      return {
        total_templates: templates.length,
        total_usage: totalUsage,
        most_popular: mostPopular,
        usage_by_type: usageByType,
        usage_by_category: usageByCategory,
        recent_usage: recentUsageFormatted
      };
    } catch (error) {
      console.error('Error getting template analytics:', error);
      throw new Error('Failed to get template analytics');
    }
  }

  /**
   * Create a new template from an existing document
   */
  static async createTemplateFromDocument(
    documentId: string,
    templateData: {
      name: string;
      category: string;
      description?: string;
      doc_type:
        | 'essay'
        | 'personal_statement'
        | 'transcript'
        | 'activity_resume'
        | 'other';
      word_limit?: number;
      tags?: string[];
      difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
      is_featured?: boolean;
    }
  ): Promise<string> {
    try {
      // Get the original document
      const { data: document } = await this.supabase
        .from('documents')
        .select('google_doc_id, metadata')
        .eq('id', documentId)
        .single();

      if (!document || !document.google_doc_id) {
        throw new Error('Document not found or has no Google Doc');
      }

      // Get document content
      const content = await GoogleDocsService.getDocumentContent(
        document.google_doc_id
      );

      // Create new template document
      const { documentId: templateDocId } =
        await GoogleDocsService.createDocument(
          templateData.name,
          'Template',
          templateData.doc_type,
          content
        );

      // Move to templates folder
      const templatesFolderId =
        await GoogleDriveService.getOrCreateTemplatesFolder();
      const { google } = await import('googleapis');

      // Note: This would need proper auth setup
      // await drive.files.update({
      //   fileId: templateDocId,
      //   addParents: templatesFolderId,
      //   fields: 'id, parents'
      // });

      // Store template metadata
      const { error } = await this.supabase.from('template_metadata').insert({
        google_doc_id: templateDocId,
        name: templateData.name,
        doc_type: templateData.doc_type,
        category: templateData.category,
        description: templateData.description || '',
        word_limit: templateData.word_limit,
        tags: templateData.tags || [],
        difficulty_level: templateData.difficulty_level || 'intermediate',
        is_featured: templateData.is_featured || false,
        usage_count: 0
      });

      if (error) {
        throw error;
      }

      return templateDocId;
    } catch (error) {
      console.error('Error creating template from document:', error);
      throw new Error('Failed to create template from document');
    }
  }

  /**
   * Estimate student level based on profile data (simplified logic)
   */
  private static estimateStudentLevel(
    studentProfile: any
  ): 'beginner' | 'intermediate' | 'advanced' {
    if (!studentProfile) return 'intermediate';

    // Simple heuristic based on academic info and target schools
    const academicInfo = studentProfile.academic_info || {};
    const targetSchools = studentProfile.target_schools || [];

    let score = 0;

    // GPA consideration
    if (academicInfo.gpa >= 3.8) score += 2;
    else if (academicInfo.gpa >= 3.5) score += 1;

    // Test scores consideration
    if (academicInfo.sat_score >= 1500 || academicInfo.act_score >= 34)
      score += 2;
    else if (academicInfo.sat_score >= 1400 || academicInfo.act_score >= 30)
      score += 1;

    // Target school competitiveness
    const competitiveSchools = targetSchools.filter(
      (school: any) =>
        school.tier === 'highly_competitive' ||
        school.tier === 'most_competitive'
    ).length;

    if (competitiveSchools >= 3) score += 2;
    else if (competitiveSchools >= 1) score += 1;

    if (score >= 4) return 'advanced';
    if (score >= 2) return 'intermediate';
    return 'beginner';
  }
}
