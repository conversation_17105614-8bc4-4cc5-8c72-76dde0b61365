import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { getUserByClerkId } from './supabase';
import type {
  ApiResponse,
  ApiSuccessResponse,
  ApiErrorResponse,
  ApiValidationErrorResponse,
  PaginatedApiResponse,
  PaginationMeta,
  LegacyApiResponse,
  User
} from '@/types/application';

// API Error Classes
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class ValidationError extends ApiError {
  constructor(
    message: string,
    public field?: string
  ) {
    super(message, 400, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends ApiError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends ApiError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends ApiError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR');
    this.name = 'NotFoundError';
  }
}

// Enhanced API Response Helpers
export function createStandardSuccessResponse<T>(
  data: T,
  message?: string,
  statusCode: number = 200,
  meta?: { [key: string]: any }
): NextResponse<ApiSuccessResponse<T>> {
  return NextResponse.json(
    {
      success: true,
      data,
      message,
      meta,
      timestamp: new Date().toISOString(),
      request_id: generateRequestId()
    },
    { status: statusCode }
  );
}

export function createStandardErrorResponse(
  error: string | Error | ApiError,
  statusCode?: number,
  code?: string,
  details?: any
): NextResponse<ApiErrorResponse> {
  let message: string;
  let errorCode: string | undefined;
  let status: number;

  if (error instanceof ApiError) {
    message = error.message;
    errorCode = error.code;
    status = error.statusCode;
  } else {
    message = typeof error === 'string' ? error : error.message;
    errorCode = code;
    status = statusCode || 500;
  }

  return NextResponse.json(
    {
      success: false,
      error: {
        message,
        code: errorCode,
        details
      },
      timestamp: new Date().toISOString(),
      request_id: generateRequestId()
    },
    { status }
  );
}

export function createValidationErrorResponse(
  validationErrors: Array<{ field: string; message: string; value?: any }>,
  statusCode: number = 400
): NextResponse<ApiValidationErrorResponse> {
  return NextResponse.json(
    {
      success: false,
      error: {
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: validationErrors
      },
      timestamp: new Date().toISOString(),
      request_id: generateRequestId()
    },
    { status: statusCode }
  );
}

// Legacy response helpers for backward compatibility
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  statusCode: number = 200
): NextResponse<LegacyApiResponse<T>> {
  return NextResponse.json(
    {
      success: true,
      data,
      message
    },
    { status: statusCode }
  );
}

export function createErrorResponse(
  error: string | Error,
  statusCode: number = 500,
  code?: string
): NextResponse<LegacyApiResponse> {
  const message = typeof error === 'string' ? error : error.message;

  return NextResponse.json(
    {
      success: false,
      error: message,
      code
    },
    { status: statusCode }
  );
}

// Utility function to generate request IDs
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// API Response Utilities
export class ApiResponseBuilder<T = any> {
  private data?: T;
  private message?: string;
  private meta?: { [key: string]: any };
  private statusCode: number = 200;

  static success<T>(data: T): ApiResponseBuilder<T> {
    const builder = new ApiResponseBuilder<T>();
    builder.data = data;
    return builder;
  }

  static error(message: string, code?: string): ApiResponseBuilder {
    const builder = new ApiResponseBuilder();
    return builder.withError(message, code);
  }

  withMessage(message: string): this {
    this.message = message;
    return this;
  }

  withMeta(meta: { [key: string]: any }): this {
    this.meta = { ...this.meta, ...meta };
    return this;
  }

  withStatus(statusCode: number): this {
    this.statusCode = statusCode;
    return this;
  }

  withError(message: string, code?: string): this {
    return this;
  }

  build(): NextResponse<ApiSuccessResponse<T>> {
    return createStandardSuccessResponse(
      this.data!,
      this.message,
      this.statusCode,
      this.meta
    );
  }
}

// Utility for handling async operations with standard error handling
export async function handleAsyncOperation<T>(
  operation: () => Promise<T>,
  errorMessage: string = 'Operation failed'
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    console.error(`${errorMessage}:`, error);

    if (error instanceof ApiError) {
      throw error;
    }

    // Handle Supabase errors
    if (error && typeof error === 'object' && 'code' in error) {
      const supabaseError = error as any;
      throw new ApiError(
        supabaseError.message || errorMessage,
        500,
        supabaseError.code
      );
    }

    throw new ApiError(errorMessage, 500);
  }
}

// Utility for standardizing API endpoint responses
export function standardizeApiEndpoint(
  handler: (request: NextRequest, context?: any) => Promise<any>,
  options: {
    requireAuth?: boolean;
    allowedRoles?: string[];
    validateResponse?: boolean;
  } = {}
) {
  let wrappedHandler: (
    request: NextRequest,
    context?: any
  ) => Promise<NextResponse> = async (request: NextRequest, context?: any) => {
    const result = await handler(request, context);

    // If result is already a NextResponse, return it
    if (result instanceof NextResponse) {
      return result;
    }

    // Otherwise, wrap in standard success response
    return createStandardSuccessResponse(result);
  };

  // Apply authentication if required
  if (options.requireAuth) {
    if (options.allowedRoles) {
      wrappedHandler = withStandardRole(
        options.allowedRoles,
        async (request: NextRequest, user: User, context?: any) => {
          const result = await handler(request, context);

          // If result is already a NextResponse, return it
          if (result instanceof NextResponse) {
            return result;
          }

          // Otherwise, wrap in standard success response
          return createStandardSuccessResponse(result);
        }
      );
    } else {
      wrappedHandler = withStandardAuth(
        async (request: NextRequest, user: User, context?: any) => {
          const result = await handler(request, context);

          // If result is already a NextResponse, return it
          if (result instanceof NextResponse) {
            return result;
          }

          // Otherwise, wrap in standard success response
          return createStandardSuccessResponse(result);
        }
      );
    }
  } else {
    wrappedHandler = withStandardErrorHandler(wrappedHandler);
  }

  // Apply response validation if requested
  if (options.validateResponse && process.env.NODE_ENV === 'development') {
    const {
      withDevResponseValidation
    } = require('./response-validation-middleware');
    wrappedHandler = withDevResponseValidation(wrappedHandler);
  }

  return wrappedHandler;
}

// Authentication Helper
export async function getAuthenticatedUser(): Promise<User> {
  const { userId } = await auth();

  if (!userId) {
    throw new AuthenticationError();
  }

  const user = await getUserByClerkId(userId);

  if (!user) {
    throw new AuthenticationError('User not found in database');
  }

  return user;
}

// Authorization Helpers
export function requireRole(user: User, allowedRoles: string[]): void {
  if (!allowedRoles.includes(user.role)) {
    throw new AuthorizationError(
      `Access denied. Required roles: ${allowedRoles.join(', ')}`
    );
  }
}

export function requireAdmin(user: User): void {
  requireRole(user, ['admin']);
}

export function requireConsultantOrAdmin(user: User): void {
  requireRole(user, ['consultant', 'admin']);
}

// Request Validation Helpers
export async function parseRequestBody<T>(request: NextRequest): Promise<T> {
  try {
    const body = await request.json();
    return body as T;
  } catch (error) {
    throw new ValidationError('Invalid JSON in request body');
  }
}

export function validateRequiredFields(
  data: Record<string, any>,
  requiredFields: string[]
): void {
  const missingFields = requiredFields.filter(
    (field) =>
      data[field] === undefined || data[field] === null || data[field] === ''
  );

  if (missingFields.length > 0) {
    throw new ValidationError(
      `Missing required fields: ${missingFields.join(', ')}`
    );
  }
}

// Enhanced validation with detailed error information
export function validateFieldsWithDetails(
  data: Record<string, any>,
  validationRules: Array<{
    field: string;
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'array' | 'object';
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: any) => string | null; // Return error message or null if valid
  }>
): Array<{ field: string; message: string; value?: any }> {
  const errors: Array<{ field: string; message: string; value?: any }> = [];

  for (const rule of validationRules) {
    const value = data[rule.field];

    // Check required fields
    if (
      rule.required &&
      (value === undefined || value === null || value === '')
    ) {
      errors.push({
        field: rule.field,
        message: `${rule.field} is required`,
        value
      });
      continue;
    }

    // Skip further validation if field is not required and empty
    if (
      !rule.required &&
      (value === undefined || value === null || value === '')
    ) {
      continue;
    }

    // Type validation
    if (rule.type) {
      const actualType = Array.isArray(value) ? 'array' : typeof value;
      if (actualType !== rule.type) {
        errors.push({
          field: rule.field,
          message: `${rule.field} must be of type ${rule.type}`,
          value
        });
        continue;
      }
    }

    // String length validation
    if (rule.type === 'string' && typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        errors.push({
          field: rule.field,
          message: `${rule.field} must be at least ${rule.minLength} characters long`,
          value
        });
      }
      if (rule.maxLength && value.length > rule.maxLength) {
        errors.push({
          field: rule.field,
          message: `${rule.field} must be no more than ${rule.maxLength} characters long`,
          value
        });
      }
    }

    // Pattern validation
    if (
      rule.pattern &&
      typeof value === 'string' &&
      !rule.pattern.test(value)
    ) {
      errors.push({
        field: rule.field,
        message: `${rule.field} format is invalid`,
        value
      });
    }

    // Custom validation
    if (rule.custom) {
      const customError = rule.custom(value);
      if (customError) {
        errors.push({
          field: rule.field,
          message: customError,
          value
        });
      }
    }
  }

  return errors;
}

// Enhanced Error Handler Wrapper with Standard Responses
export function withStandardErrorHandler(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      const response = await handler(request, context);

      // Validate response format in development
      if (process.env.NODE_ENV === 'development') {
        validateResponseFormat(response);
      }

      return response;
    } catch (error) {
      console.error('API Error:', error);

      if (error instanceof ValidationError) {
        return createValidationErrorResponse([
          {
            field: error.field || 'unknown',
            message: error.message
          }
        ]);
      }

      if (error instanceof ApiError) {
        return createStandardErrorResponse(error);
      }

      // Handle Supabase errors
      if (error && typeof error === 'object' && 'code' in error) {
        const supabaseError = error as any;
        return createStandardErrorResponse(
          supabaseError.message || 'Database error',
          500,
          supabaseError.code
        );
      }

      // Generic error
      return createStandardErrorResponse(
        'Internal server error',
        500,
        'INTERNAL_ERROR'
      );
    }
  };
}

// Legacy Error Handler for backward compatibility
export function withErrorHandler(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      return await handler(request, context);
    } catch (error) {
      console.error('API Error:', error);

      if (error instanceof ApiError) {
        return createErrorResponse(error.message, error.statusCode, error.code);
      }

      // Handle Supabase errors
      if (error && typeof error === 'object' && 'code' in error) {
        const supabaseError = error as any;
        return createErrorResponse(
          supabaseError.message || 'Database error',
          500,
          supabaseError.code
        );
      }

      // Generic error
      return createErrorResponse(
        'Internal server error',
        500,
        'INTERNAL_ERROR'
      );
    }
  };
}

// Response format validation (development only)
function validateResponseFormat(response: NextResponse): void {
  try {
    // This is a basic validation - in a real implementation,
    // you might want to parse the response body and validate its structure
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      // Response appears to be JSON, which is expected
      return;
    }
  } catch (error) {
    console.warn('Response validation failed:', error);
  }
}

// Enhanced Authentication Wrapper with Standard Responses
export function withStandardAuth(
  handler: (
    request: NextRequest,
    user: User,
    context?: any
  ) => Promise<NextResponse>
) {
  return withStandardErrorHandler(
    async (request: NextRequest, context?: any) => {
      const user = await getAuthenticatedUser();
      return handler(request, user, context);
    }
  );
}

// Enhanced Role-based Authorization Wrapper
export function withStandardRole(
  allowedRoles: string[],
  handler: (
    request: NextRequest,
    user: User,
    context?: any
  ) => Promise<NextResponse>
) {
  return withStandardAuth(
    async (request: NextRequest, user: User, context?: any) => {
      requireRole(user, allowedRoles);
      return handler(request, user, context);
    }
  );
}

// Legacy Authentication Wrapper for backward compatibility
export function withAuth(
  handler: (
    request: NextRequest,
    user: User,
    context?: any
  ) => Promise<NextResponse>
) {
  return withErrorHandler(async (request: NextRequest, context?: any) => {
    const user = await getAuthenticatedUser();
    return handler(request, user, context);
  });
}

// Legacy Role-based Authorization Wrapper
export function withRole(
  allowedRoles: string[],
  handler: (
    request: NextRequest,
    user: User,
    context?: any
  ) => Promise<NextResponse>
) {
  return withAuth(async (request: NextRequest, user: User, context?: any) => {
    requireRole(user, allowedRoles);
    return handler(request, user, context);
  });
}

// Pagination Helpers
export interface PaginationParams {
  page: number;
  limit: number;
  offset: number;
}

export function parsePaginationParams(request: NextRequest): PaginationParams {
  const url = new URL(request.url);
  const page = Math.max(1, parseInt(url.searchParams.get('page') || '1'));
  const limit = Math.min(
    100,
    Math.max(1, parseInt(url.searchParams.get('limit') || '10'))
  );
  const offset = (page - 1) * limit;

  return { page, limit, offset };
}

export function createStandardPaginatedResponse<T>(
  data: T[],
  total: number,
  pagination: PaginationParams,
  message?: string,
  additionalMeta?: { [key: string]: any }
): NextResponse<PaginatedApiResponse<T>> {
  const paginationMeta: PaginationMeta = {
    page: pagination.page,
    limit: pagination.limit,
    total,
    totalPages: Math.ceil(total / pagination.limit),
    hasNext: pagination.page < Math.ceil(total / pagination.limit),
    hasPrev: pagination.page > 1
  };

  return NextResponse.json(
    {
      success: true,
      data,
      message,
      meta: {
        pagination: paginationMeta,
        ...additionalMeta
      },
      timestamp: new Date().toISOString(),
      request_id: generateRequestId()
    },
    { status: 200 }
  );
}

// Legacy paginated response for backward compatibility
export function createPaginatedResponse<T>(
  data: T[],
  total: number,
  pagination: PaginationParams,
  message?: string
) {
  return createSuccessResponse(
    {
      data,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages: Math.ceil(total / pagination.limit)
      }
    },
    message
  );
}
