import type {
  ApiResponse,
  ApiSuccessResponse,
  ApiErrorResponse,
  PaginatedApiResponse
} from '@/types/application';

import {
  isApiSuccessResponse,
  isApiErrorResponse,
  isApiValidationErrorResponse,
  isPaginatedResponse
} from '@/types/application';

import { memoryCache, sessionCache } from './cache';
import { perf } from './performance';
import { useState, useEffect, useMemo, useCallback } from 'react';
import { toast } from 'sonner';

// API Client Configuration
interface ApiClientConfig {
  baseUrl?: string;
  defaultHeaders?: Record<string, string>;
  timeout?: number;
}

// API Client Error Classes
export class ApiClientError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiClientError';
  }
}

export class ValidationError extends ApiClientError {
  constructor(
    message: string,
    public validationErrors: Array<{
      field: string;
      message: string;
      value?: any;
    }>
  ) {
    super(message, 400, 'VALIDATION_ERROR', validationErrors);
    this.name = 'ValidationError';
  }
}

// Enhanced API Client
export class ApiClient {
  private config: ApiClientConfig;

  constructor(config: ApiClientConfig = {}) {
    this.config = {
      baseUrl: '',
      defaultHeaders: {
        'Content-Type': 'application/json'
      },
      timeout: 30000,
      ...config
    };
  }

  // Generic API call method
  async call<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiSuccessResponse<T>> {
    const url = `${this.config.baseUrl}${endpoint}`;

    const requestOptions: RequestInit = {
      ...options,
      headers: {
        ...this.config.defaultHeaders,
        ...options.headers
      }
    };

    try {
      const response = await fetch(url, requestOptions);
      const result: ApiResponse<T> = await response.json();

      if (isApiSuccessResponse(result)) {
        return result;
      } else if (isApiErrorResponse(result)) {
        if (isApiValidationErrorResponse(result)) {
          throw new ValidationError(result.error.message, result.error.details);
        } else {
          throw new ApiClientError(
            result.error.message,
            response.status,
            result.error.code,
            result.error.details
          );
        }
      } else {
        throw new ApiClientError(
          'Invalid API response format',
          response.status
        );
      }
    } catch (error) {
      if (error instanceof ApiClientError || error instanceof ValidationError) {
        throw error;
      }

      // Handle network errors, JSON parsing errors, etc.
      throw new ApiClientError(
        error instanceof Error ? error.message : 'Network error',
        0
      );
    }
  }

  // Convenience methods
  async get<T = any>(
    endpoint: string,
    params?: Record<string, string>
  ): Promise<ApiSuccessResponse<T>> {
    const url = params
      ? `${endpoint}?${new URLSearchParams(params).toString()}`
      : endpoint;
    return this.call<T>(url, { method: 'GET' });
  }

  async post<T = any>(
    endpoint: string,
    data?: any
  ): Promise<ApiSuccessResponse<T>> {
    return this.call<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  async put<T = any>(
    endpoint: string,
    data?: any
  ): Promise<ApiSuccessResponse<T>> {
    return this.call<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  async delete<T = any>(endpoint: string): Promise<ApiSuccessResponse<T>> {
    return this.call<T>(endpoint, { method: 'DELETE' });
  }

  // Paginated request helper
  async getPaginated<T = any>(
    endpoint: string,
    params?: Record<string, string>
  ): Promise<PaginatedApiResponse<T>> {
    const response = await this.get<T[]>(endpoint, params);

    if (isPaginatedResponse(response)) {
      return response;
    } else {
      throw new ApiClientError('Response is not paginated', 500);
    }
  }
}

// Default API client instance
export const apiClient = new ApiClient();

// React Hook for API calls
3export interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  validationErrors: Array<{
    field: string;
    message: string;
    value?: any;
  }> | null;
}

export function useApi<T = any>() {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
    validationErrors: null
  });

  const execute = useCallback(
    async (apiCall: () => Promise<ApiSuccessResponse<T>>) => {
      setState((prev) => ({
        ...prev,
        loading: true,
        error: null,
        validationErrors: null
      }));

      try {
        const response = await apiCall();
        setState({
          data: response.data,
          loading: false,
          error: null,
          validationErrors: null
        });
        return response;
      } catch (error) {
        if (error instanceof ValidationError) {
          setState((prev) => ({
            ...prev,
            loading: false,
            error: error.message,
            validationErrors: error.validationErrors
          }));
        } else if (error instanceof ApiClientError) {
          setState((prev) => ({
            ...prev,
            loading: false,
            error: error.message,
            validationErrors: null
          }));
        } else {
          setState((prev) => ({
            ...prev,
            loading: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            validationErrors: null
          }));
        }
        throw error;
      }
    },
    []
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      validationErrors: null
    });
  }, []);

  return {
    ...state,
    execute,
    reset
  };
}

// Specialized hooks for common operations
export function useApiGet<T = any>(
  endpoint: string,
  params?: Record<string, string>
) {
  const { data, loading, error, validationErrors, execute, reset } =
    useApi<T>();

  // Memoize params to prevent infinite loops
  const stableParams = useMemo(() => params, [JSON.stringify(params)]);

  const fetch = useCallback(() => {
    return execute(() => apiClient.get<T>(endpoint, stableParams));
  }, [endpoint, stableParams, execute]);

  return { data, loading, error, validationErrors, fetch, reset };
}

export function useApiPost<T = any>(endpoint?: string) {
  const api = useApi<T>();

  const post = useCallback(
    (endpointOrData?: string | any, data?: any) => {
      // Handle both patterns: useApiPost(endpoint) and useApiPost()
      if (typeof endpointOrData === 'string') {
        // Called with endpoint: post(endpoint, data)
        return api.execute(() => apiClient.post<T>(endpointOrData, data));
      } else {
        // Called with preset endpoint: post(data)
        if (!endpoint) {
          throw new Error('Endpoint must be provided either in hook or function call');
        }
        return api.execute(() => apiClient.post<T>(endpoint, endpointOrData));
      }
    },
    [endpoint, api]
  );

  return { ...api, post };
}

export function useApiPut<T = any>(endpoint?: string) {
  const api = useApi<T>();

  const put = useCallback(
    (endpointOrData?: string | any, data?: any) => {
      // Handle both patterns: useApiPut(endpoint) and useApiPut()
      if (typeof endpointOrData === 'string') {
        // Called with endpoint: put(endpoint, data)
        return api.execute(() => apiClient.put<T>(endpointOrData, data));
      } else {
        // Called with preset endpoint: put(data)
        if (!endpoint) {
          throw new Error('Endpoint must be provided either in hook or function call');
        }
        return api.execute(() => apiClient.put<T>(endpoint, endpointOrData));
      }
    },
    [endpoint, api]
  );

  return { ...api, put };
}

export function useApiDelete<T = any>(endpoint?: string) {
  const api = useApi<T>();

  const del = useCallback(
    (endpointParam?: string) => {
      const targetEndpoint = endpointParam || endpoint;
      if (!targetEndpoint) {
        throw new Error('Endpoint must be provided either in hook or function call');
      }
      return api.execute(() => apiClient.delete<T>(targetEndpoint));
    },
    [endpoint, api]
  );

  return { ...api, delete: del };
}

// Legacy API response handler for backward compatibility
export function handleLegacyApiResponse<T>(response: any): T {
  // Handle both old and new response formats
  if (response.success !== undefined) {
    if (response.success) {
      // Handle paginated responses
      if (
        response.data &&
        typeof response.data === 'object' &&
        Array.isArray(response.data.data)
      ) {
        return response.data.data;
      }
      return response.data;
    } else {
      throw new Error(response.error || 'API request failed');
    }
  }

  // Assume it's raw data if no success field
  return response;
}

// Utility function to extract data from API response
export function extractApiData<T>(response: ApiResponse<T>): T {
  if (isApiSuccessResponse(response)) {
    return response.data;
  } else {
    throw new ApiClientError(
      response.error.message,
      500,
      response.error.code,
      response.error.details
    );
  }
}
