/**
 * Performance monitoring and optimization utilities
 */

import { log } from './logger';

// Performance metrics interface
interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

// Performance monitoring class
class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];

  private constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers();
    }
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private initializeObservers(): void {
    try {
      // Observe navigation timing
      if ('PerformanceObserver' in window) {
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric({
              name: `navigation.${entry.name}`,
              value: entry.duration,
              timestamp: Date.now(),
              metadata: {
                type: entry.entryType,
                startTime: entry.startTime,
              },
            });
          }
        });

        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navObserver);

        // Observe resource loading
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 100) { // Only log slow resources
              this.recordMetric({
                name: `resource.${entry.name.split('/').pop() || 'unknown'}`,
                value: entry.duration,
                timestamp: Date.now(),
                metadata: {
                  type: entry.entryType,
                  transferSize: (entry as any).transferSize,
                  encodedBodySize: (entry as any).encodedBodySize,
                },
              });
            }
          }
        });

        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);

        // Observe largest contentful paint
        const lcpObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric({
              name: 'lcp',
              value: entry.startTime,
              timestamp: Date.now(),
              metadata: {
                element: (entry as any).element?.tagName,
                url: (entry as any).url,
              },
            });
          }
        });

        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      }
    } catch (error) {
      log.warn('Failed to initialize performance observers', 'PERF', error);
    }
  }

  public recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Log slow operations
    if (metric.value > 1000) {
      log.warn(`Slow operation detected: ${metric.name} took ${metric.value}ms`, 'PERF');
    }

    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }
  }

  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  public getAverageMetric(name: string): number {
    const relevantMetrics = this.metrics.filter(m => m.name === name);
    if (relevantMetrics.length === 0) return 0;
    
    const sum = relevantMetrics.reduce((acc, m) => acc + m.value, 0);
    return sum / relevantMetrics.length;
  }

  public clearMetrics(): void {
    this.metrics = [];
  }

  public destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics = [];
  }
}

// Timing utilities
export class Timer {
  private startTime: number;
  private name: string;

  constructor(name: string) {
    this.name = name;
    this.startTime = performance.now();
  }

  public end(): number {
    const duration = performance.now() - this.startTime;
    PerformanceMonitor.getInstance().recordMetric({
      name: this.name,
      value: duration,
      timestamp: Date.now(),
    });
    return duration;
  }
}

// Debounce utility for performance optimization
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle utility for performance optimization
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Memoization utility
export function memoize<T extends (...args: any[]) => any>(
  func: T,
  getKey?: (...args: Parameters<T>) => string
): T {
  const cache = new Map<string, ReturnType<T>>();
  
  return ((...args: Parameters<T>) => {
    const key = getKey ? getKey(...args) : JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = func(...args);
    cache.set(key, result);
    
    // Prevent memory leaks by limiting cache size
    if (cache.size > 100) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }
    
    return result;
  }) as T;
}

// Lazy loading utility for React components
export function createLazyComponent<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) {
  const LazyComponent = React.lazy(importFunc);
  
  return (props: React.ComponentProps<T>) => (
    <React.Suspense fallback={fallback ? React.createElement(fallback) : <div>Loading...</div>}>
      <LazyComponent {...props} />
    </React.Suspense>
  );
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Utility functions
export const perf = {
  timer: (name: string) => new Timer(name),
  record: (name: string, value: number, metadata?: Record<string, any>) => {
    performanceMonitor.recordMetric({
      name,
      value,
      timestamp: Date.now(),
      metadata,
    });
  },
  getMetrics: () => performanceMonitor.getMetrics(),
  getAverage: (name: string) => performanceMonitor.getAverageMetric(name),
  clear: () => performanceMonitor.clearMetrics(),
};

// React hook for performance monitoring
export function usePerformanceMonitoring(componentName: string) {
  React.useEffect(() => {
    const timer = new Timer(`component.${componentName}.mount`);
    
    return () => {
      timer.end();
    };
  }, [componentName]);
}

// Add React import for lazy loading utility
import React from 'react';
