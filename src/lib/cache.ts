/**
 * Client-side caching utilities for API responses and data
 */

import { log } from './logger';

// Cache entry interface
interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
  key: string;
}

// Cache configuration
interface CacheConfig {
  defaultTTL: number; // Default time to live in milliseconds
  maxSize: number; // Maximum number of entries
  storageType: 'memory' | 'localStorage' | 'sessionStorage';
}

// Default cache configuration
const DEFAULT_CONFIG: CacheConfig = {
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  maxSize: 100,
  storageType: 'memory',
};

// Cache implementation
class Cache {
  private config: CacheConfig;
  private memoryCache: Map<string, CacheEntry> = new Map();

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // Generate cache key
  private generateKey(prefix: string, params?: Record<string, any>): string {
    if (!params) return prefix;
    
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((acc, key) => {
        acc[key] = params[key];
        return acc;
      }, {} as Record<string, any>);
    
    return `${prefix}:${JSON.stringify(sortedParams)}`;
  }

  // Check if entry is expired
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  // Get from storage
  private getFromStorage(key: string): CacheEntry | null {
    try {
      if (this.config.storageType === 'memory') {
        return this.memoryCache.get(key) || null;
      }

      const storage = this.config.storageType === 'localStorage' 
        ? localStorage 
        : sessionStorage;
      
      const item = storage.getItem(`cache:${key}`);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      log.warn(`Failed to get cache entry: ${key}`, 'CACHE', error);
      return null;
    }
  }

  // Set to storage
  private setToStorage(key: string, entry: CacheEntry): void {
    try {
      if (this.config.storageType === 'memory') {
        this.memoryCache.set(key, entry);
        
        // Enforce max size
        if (this.memoryCache.size > this.config.maxSize) {
          const firstKey = this.memoryCache.keys().next().value;
          this.memoryCache.delete(firstKey);
        }
        return;
      }

      const storage = this.config.storageType === 'localStorage' 
        ? localStorage 
        : sessionStorage;
      
      storage.setItem(`cache:${key}`, JSON.stringify(entry));
    } catch (error) {
      log.warn(`Failed to set cache entry: ${key}`, 'CACHE', error);
    }
  }

  // Remove from storage
  private removeFromStorage(key: string): void {
    try {
      if (this.config.storageType === 'memory') {
        this.memoryCache.delete(key);
        return;
      }

      const storage = this.config.storageType === 'localStorage' 
        ? localStorage 
        : sessionStorage;
      
      storage.removeItem(`cache:${key}`);
    } catch (error) {
      log.warn(`Failed to remove cache entry: ${key}`, 'CACHE', error);
    }
  }

  // Get cached data
  public get<T>(prefix: string, params?: Record<string, any>): T | null {
    const key = this.generateKey(prefix, params);
    const entry = this.getFromStorage(key);

    if (!entry) {
      return null;
    }

    if (this.isExpired(entry)) {
      this.removeFromStorage(key);
      return null;
    }

    log.debug(`Cache hit: ${key}`, 'CACHE');
    return entry.data;
  }

  // Set cached data
  public set<T>(
    prefix: string, 
    data: T, 
    params?: Record<string, any>, 
    ttl?: number
  ): void {
    const key = this.generateKey(prefix, params);
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.defaultTTL,
      key,
    };

    this.setToStorage(key, entry);
    log.debug(`Cache set: ${key}`, 'CACHE');
  }

  // Remove cached data
  public remove(prefix: string, params?: Record<string, any>): void {
    const key = this.generateKey(prefix, params);
    this.removeFromStorage(key);
    log.debug(`Cache removed: ${key}`, 'CACHE');
  }

  // Clear all cache entries
  public clear(): void {
    if (this.config.storageType === 'memory') {
      this.memoryCache.clear();
    } else {
      const storage = this.config.storageType === 'localStorage' 
        ? localStorage 
        : sessionStorage;
      
      const keys = Object.keys(storage).filter(key => key.startsWith('cache:'));
      keys.forEach(key => storage.removeItem(key));
    }
    
    log.debug('Cache cleared', 'CACHE');
  }

  // Get cache statistics
  public getStats(): { size: number; keys: string[] } {
    if (this.config.storageType === 'memory') {
      return {
        size: this.memoryCache.size,
        keys: Array.from(this.memoryCache.keys()),
      };
    }

    const storage = this.config.storageType === 'localStorage' 
      ? localStorage 
      : sessionStorage;
    
    const keys = Object.keys(storage)
      .filter(key => key.startsWith('cache:'))
      .map(key => key.replace('cache:', ''));
    
    return { size: keys.length, keys };
  }

  // Cleanup expired entries
  public cleanup(): void {
    if (this.config.storageType === 'memory') {
      for (const [key, entry] of this.memoryCache.entries()) {
        if (this.isExpired(entry)) {
          this.memoryCache.delete(key);
        }
      }
    } else {
      const storage = this.config.storageType === 'localStorage' 
        ? localStorage 
        : sessionStorage;
      
      const keys = Object.keys(storage).filter(key => key.startsWith('cache:'));
      
      keys.forEach(key => {
        try {
          const entry = JSON.parse(storage.getItem(key) || '{}');
          if (this.isExpired(entry)) {
            storage.removeItem(key);
          }
        } catch (error) {
          // Remove invalid entries
          storage.removeItem(key);
        }
      });
    }
    
    log.debug('Cache cleanup completed', 'CACHE');
  }
}

// Create cache instances
export const memoryCache = new Cache({ storageType: 'memory' });
export const sessionCache = new Cache({ storageType: 'sessionStorage' });
export const persistentCache = new Cache({ 
  storageType: 'localStorage',
  defaultTTL: 30 * 60 * 1000, // 30 minutes for persistent cache
});

// Cache decorator for functions
export function cached<T extends (...args: any[]) => any>(
  cache: Cache,
  prefix: string,
  ttl?: number
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: Parameters<T>) {
      const params = args.length > 0 ? { args } : undefined;
      
      // Try to get from cache first
      const cached = cache.get(prefix, params);
      if (cached !== null) {
        return cached;
      }

      // Execute original method
      const result = await originalMethod.apply(this, args);
      
      // Cache the result
      cache.set(prefix, result, params, ttl);
      
      return result;
    };

    return descriptor;
  };
}

// React hook for cached API calls
export function useCachedApi<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: {
    cache?: Cache;
    ttl?: number;
    enabled?: boolean;
  } = {}
) {
  const {
    cache = memoryCache,
    ttl,
    enabled = true,
  } = options;

  const [data, setData] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    if (!enabled) return;

    const fetchData = async () => {
      // Check cache first
      const cached = cache.get<T>(key);
      if (cached !== null) {
        setData(cached);
        return;
      }

      // Fetch from API
      setLoading(true);
      setError(null);
      
      try {
        const result = await fetcher();
        cache.set(key, result, undefined, ttl);
        setData(result);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [key, enabled, cache, ttl, fetcher]);

  const invalidate = React.useCallback(() => {
    cache.remove(key);
    setData(null);
  }, [cache, key]);

  return { data, loading, error, invalidate };
}

// Cleanup function to run periodically
export function setupCacheCleanup(): void {
  // Run cleanup every 10 minutes
  setInterval(() => {
    memoryCache.cleanup();
    sessionCache.cleanup();
    persistentCache.cleanup();
  }, 10 * 60 * 1000);
}

// Initialize cleanup on client side
if (typeof window !== 'undefined') {
  setupCacheCleanup();
}

// Add React import
import React from 'react';
