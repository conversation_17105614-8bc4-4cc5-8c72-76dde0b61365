import { logger, log, LogLevel } from '../logger'

// Mock console methods
const mockConsole = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}

// Replace console methods with mocks
beforeEach(() => {
  jest.clearAllMocks()
  global.console = mockConsole as any
})

describe('Logger', () => {
  describe('LogLevel', () => {
    it('should have correct log level values', () => {
      expect(LogLevel.DEBUG).toBe(0)
      expect(LogLevel.INFO).toBe(1)
      expect(LogLevel.WARN).toBe(2)
      expect(LogLevel.ERROR).toBe(3)
    })
  })

  describe('logger instance', () => {
    it('should be a singleton', () => {
      const logger1 = logger
      const logger2 = logger
      expect(logger1).toBe(logger2)
    })

    it('should log debug messages in development', () => {
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'development'
      
      logger.debug('Test debug message', 'TEST')
      
      expect(mockConsole.debug).toHaveBeenCalledWith(
        expect.stringContaining('DEBUG [TEST]: Test debug message'),
        ''
      )
      
      process.env.NODE_ENV = originalEnv
    })

    it('should log info messages', () => {
      logger.info('Test info message', 'TEST')
      
      expect(mockConsole.info).toHaveBeenCalledWith(
        expect.stringContaining('INFO [TEST]: Test info message'),
        ''
      )
    })

    it('should log warning messages', () => {
      logger.warn('Test warning message', 'TEST')
      
      expect(mockConsole.warn).toHaveBeenCalledWith(
        expect.stringContaining('WARN [TEST]: Test warning message'),
        ''
      )
    })

    it('should log error messages with error object', () => {
      const testError = new Error('Test error')
      logger.error('Test error message', 'TEST', testError)
      
      expect(mockConsole.error).toHaveBeenCalledWith(
        expect.stringContaining('ERROR [TEST]: Test error message'),
        '',
        testError
      )
    })

    it('should not log debug messages in production', () => {
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'production'
      
      logger.debug('Test debug message', 'TEST')
      
      expect(mockConsole.debug).not.toHaveBeenCalled()
      
      process.env.NODE_ENV = originalEnv
    })
  })

  describe('convenience log functions', () => {
    it('should call logger.debug', () => {
      const spy = jest.spyOn(logger, 'debug')
      log.debug('Test message', 'TEST')
      expect(spy).toHaveBeenCalledWith('Test message', 'TEST', undefined)
    })

    it('should call logger.info', () => {
      const spy = jest.spyOn(logger, 'info')
      log.info('Test message', 'TEST')
      expect(spy).toHaveBeenCalledWith('Test message', 'TEST', undefined)
    })

    it('should call logger.warn', () => {
      const spy = jest.spyOn(logger, 'warn')
      log.warn('Test message', 'TEST')
      expect(spy).toHaveBeenCalledWith('Test message', 'TEST', undefined)
    })

    it('should call logger.error', () => {
      const spy = jest.spyOn(logger, 'error')
      const testError = new Error('Test error')
      log.error('Test message', 'TEST', testError)
      expect(spy).toHaveBeenCalledWith('Test message', 'TEST', testError, undefined)
    })
  })

  describe('specialized logging methods', () => {
    it('should log API requests', () => {
      const spy = jest.spyOn(logger, 'debug')
      log.apiRequest('GET', '/api/test', { param: 'value' })
      expect(spy).toHaveBeenCalledWith('GET /api/test', 'API', { param: 'value' })
    })

    it('should log API responses', () => {
      const debugSpy = jest.spyOn(logger, 'debug')
      const errorSpy = jest.spyOn(logger, 'error')
      
      // Success response
      log.apiResponse('GET', '/api/test', 200, { data: 'success' })
      expect(debugSpy).toHaveBeenCalledWith('GET /api/test - 200', 'API', { data: 'success' })
      
      // Error response
      log.apiResponse('GET', '/api/test', 500, { error: 'server error' })
      expect(errorSpy).toHaveBeenCalledWith('GET /api/test - 500', 'API', undefined, { error: 'server error' })
    })

    it('should log API errors', () => {
      const spy = jest.spyOn(logger, 'error')
      const testError = new Error('API Error')
      log.apiError('GET', '/api/test', testError)
      expect(spy).toHaveBeenCalledWith('GET /api/test failed', 'API', testError)
    })

    it('should log database queries', () => {
      const spy = jest.spyOn(logger, 'debug')
      log.dbQuery('SELECT * FROM users', { limit: 10 })
      expect(spy).toHaveBeenCalledWith('Database query: SELECT * FROM users', 'DB', { limit: 10 })
    })

    it('should log database errors', () => {
      const spy = jest.spyOn(logger, 'error')
      const testError = new Error('DB Error')
      log.dbError('SELECT * FROM users', testError)
      expect(spy).toHaveBeenCalledWith('Database query failed: SELECT * FROM users', 'DB', testError)
    })

    it('should log authentication events', () => {
      const spy = jest.spyOn(logger, 'info')
      log.authEvent('login', 'user123', { method: 'email' })
      expect(spy).toHaveBeenCalledWith('Auth event: login', 'AUTH', { userId: 'user123', method: 'email' })
    })

    it('should log document events', () => {
      const spy = jest.spyOn(logger, 'info')
      log.documentEvent('created', 'doc123', 'user123', { type: 'essay' })
      expect(spy).toHaveBeenCalledWith('Document event: created', 'DOCS', { 
        documentId: 'doc123', 
        userId: 'user123', 
        type: 'essay' 
      })
    })

    it('should log Google API events', () => {
      const spy = jest.spyOn(logger, 'debug')
      log.googleApiEvent('document_created', { documentId: 'doc123' })
      expect(spy).toHaveBeenCalledWith('Google API event: document_created', 'GOOGLE', { documentId: 'doc123' })
    })

    it('should log Google API errors', () => {
      const spy = jest.spyOn(logger, 'error')
      const testError = new Error('Google API Error')
      log.googleApiError('document_creation_failed', testError)
      expect(spy).toHaveBeenCalledWith('Google API error: document_creation_failed', 'GOOGLE', testError)
    })
  })

  describe('message formatting', () => {
    it('should include timestamp in log messages', () => {
      logger.info('Test message', 'TEST')
      
      expect(mockConsole.info).toHaveBeenCalledWith(
        expect.stringMatching(/^\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z\] INFO \[TEST\]: Test message$/),
        ''
      )
    })

    it('should handle messages without context', () => {
      logger.info('Test message')
      
      expect(mockConsole.info).toHaveBeenCalledWith(
        expect.stringMatching(/^\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z\] INFO: Test message$/),
        ''
      )
    })
  })
})
