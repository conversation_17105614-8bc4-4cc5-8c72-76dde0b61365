import { renderHook, act } from '@testing-library/react';
import { ApiClient, useApiGet, useApiPost, useApiPut, useApiDelete } from '../api-client';

// Mock fetch globally
global.fetch = jest.fn();
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

describe('ApiClient', () => {
  let apiClient: ApiClient;

  beforeEach(() => {
    jest.clearAllMocks();
    apiClient = new ApiClient();
  });

  describe('GET requests', () => {
    it('should make successful GET request', async () => {
      const mockData = { id: 1, name: 'Test' };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          success: true,
          data: mockData,
        }),
      } as Response);

      const result = await apiClient.get('/test');

      expect(mockFetch).toHaveBeenCalledWith('/test', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: expect.any(AbortSignal),
      });
      expect(result.data).toEqual(mockData);
    });

    it('should handle GET request with query parameters', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          success: true,
          data: [],
        }),
      } as Response);

      await apiClient.get('/test', { page: '1', limit: '10' });

      expect(mockFetch).toHaveBeenCalledWith('/test?page=1&limit=10', expect.any(Object));
    });
  });

  describe('POST requests', () => {
    it('should make successful POST request', async () => {
      const postData = { name: 'New Item' };
      const responseData = { id: 1, ...postData };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: () => Promise.resolve({
          success: true,
          data: responseData,
        }),
      } as Response);

      const result = await apiClient.post('/test', postData);

      expect(mockFetch).toHaveBeenCalledWith('/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData),
        signal: expect.any(AbortSignal),
      });
      expect(result.data).toEqual(responseData);
    });
  });

  describe('Error handling', () => {
    it('should handle HTTP error responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        json: () => Promise.resolve({
          success: false,
          error: 'Resource not found',
        }),
      } as Response);

      await expect(apiClient.get('/nonexistent')).rejects.toThrow('Resource not found');
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(apiClient.get('/test')).rejects.toThrow('Network error');
    });

    it('should handle validation errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({
          success: false,
          error: 'Validation failed',
          validationErrors: [
            { field: 'email', message: 'Invalid email format' },
          ],
        }),
      } as Response);

      await expect(apiClient.post('/test', {})).rejects.toThrow('Validation failed');
    });
  });
});

describe('useApiGet', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useApiGet('/test'));

    expect(result.current.data).toBe(null);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.validationErrors).toBe(null);
  });

  it('should fetch data successfully', async () => {
    const mockData = { id: 1, name: 'Test' };
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: () => Promise.resolve({
        success: true,
        data: mockData,
      }),
    } as Response);

    const { result } = renderHook(() => useApiGet('/test'));

    await act(async () => {
      await result.current.fetch();
    });

    expect(result.current.data).toEqual(mockData);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should handle fetch errors', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
      json: () => Promise.resolve({
        success: false,
        error: 'Internal server error',
      }),
    } as Response);

    const { result } = renderHook(() => useApiGet('/test'));

    await act(async () => {
      await result.current.fetch();
    });

    expect(result.current.data).toBe(null);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe('Internal server error');
  });

  it('should reset state correctly', async () => {
    const { result } = renderHook(() => useApiGet('/test'));

    // Set some state first
    await act(async () => {
      result.current.reset();
    });

    expect(result.current.data).toBe(null);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.validationErrors).toBe(null);
  });
});

describe('useApiPost', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should post data successfully with preset endpoint', async () => {
    const mockData = { id: 1, name: 'Created' };
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 201,
      json: () => Promise.resolve({
        success: true,
        data: mockData,
      }),
    } as Response);

    const { result } = renderHook(() => useApiPost('/test'));

    await act(async () => {
      await result.current.post({ name: 'Test' });
    });

    expect(result.current.data).toEqual(mockData);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should post data successfully with dynamic endpoint', async () => {
    const mockData = { id: 1, name: 'Created' };
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 201,
      json: () => Promise.resolve({
        success: true,
        data: mockData,
      }),
    } as Response);

    const { result } = renderHook(() => useApiPost());

    await act(async () => {
      await result.current.post('/dynamic', { name: 'Test' });
    });

    expect(result.current.data).toEqual(mockData);
  });

  it('should handle validation errors', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 400,
      json: () => Promise.resolve({
        success: false,
        error: 'Validation failed',
        validationErrors: [
          { field: 'name', message: 'Name is required' },
        ],
      }),
    } as Response);

    const { result } = renderHook(() => useApiPost('/test'));

    await act(async () => {
      await result.current.post({});
    });

    expect(result.current.error).toBe('Validation failed');
    expect(result.current.validationErrors).toEqual([
      { field: 'name', message: 'Name is required' },
    ]);
  });
});

describe('useApiPut', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should update data successfully', async () => {
    const mockData = { id: 1, name: 'Updated' };
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: () => Promise.resolve({
        success: true,
        data: mockData,
      }),
    } as Response);

    const { result } = renderHook(() => useApiPut('/test/1'));

    await act(async () => {
      await result.current.put({ name: 'Updated' });
    });

    expect(result.current.data).toEqual(mockData);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });
});

describe('useApiDelete', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should delete data successfully', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: () => Promise.resolve({
        success: true,
        data: { deleted: true },
      }),
    } as Response);

    const { result } = renderHook(() => useApiDelete('/test/1'));

    await act(async () => {
      await result.current.delete();
    });

    expect(result.current.data).toEqual({ deleted: true });
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should delete with dynamic endpoint', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: () => Promise.resolve({
        success: true,
        data: { deleted: true },
      }),
    } as Response);

    const { result } = renderHook(() => useApiDelete());

    await act(async () => {
      await result.current.delete('/test/2');
    });

    expect(result.current.data).toEqual({ deleted: true });
  });
});
