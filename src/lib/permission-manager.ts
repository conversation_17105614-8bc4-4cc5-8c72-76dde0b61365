import { supabase as supabaseClient } from '@/lib/supabase';
import {
  DocumentPermissionService,
  GoogleDocsService
} from '@/lib/google-apis';

/**
 * Advanced permission management service for handling complex permission scenarios
 * Handles bulk operations, consultant reassignments, and permission auditing
 */
export class AdvancedPermissionManager {
  private static supabase = supabaseClient;

  /**
   * Handle consultant assignment change for a student
   * Updates permissions for all student's documents
   */
  static async handleConsultantAssignmentChange(
    studentId: string,
    oldConsultantId?: string,
    newConsultantId?: string
  ): Promise<void> {
    try {
      // Get all documents for the student
      const { data: documents, error: docsError } = await this.supabase
        .from('documents')
        .select('id, google_doc_id')
        .eq('student_id', studentId)
        .not('google_doc_id', 'is', null);

      if (docsError) {
        throw new Error('Failed to fetch student documents');
      }

      if (!documents || documents.length === 0) {
        console.log(
          'No documents found for student, skipping permission update'
        );
        return;
      }

      // Get consultant emails
      let oldConsultantEmail: string | undefined;
      let newConsultantEmail: string | undefined;

      if (oldConsultantId) {
        const { data: oldConsultant } = await this.supabase
          .from('consultants')
          .select(
            `
            users!inner(email)
          `
          )
          .eq('id', oldConsultantId)
          .single();

        oldConsultantEmail =
          (oldConsultant?.users as any)?.email ||
          (oldConsultant?.users as any)?.[0]?.email;
      }

      if (newConsultantId) {
        const { data: newConsultant } = await this.supabase
          .from('consultants')
          .select(
            `
            users!inner(email)
          `
          )
          .eq('id', newConsultantId)
          .single();

        newConsultantEmail =
          (newConsultant?.users as any)?.email ||
          (newConsultant?.users as any)?.[0]?.email;
      }

      // Update permissions for all documents
      const permissionUpdates = documents.map(async (doc) => {
        if (doc.google_doc_id) {
          try {
            await DocumentPermissionService.updateConsultantAccess(
              doc.google_doc_id,
              oldConsultantEmail,
              newConsultantEmail
            );
            console.log(`Updated permissions for document ${doc.id}`);
          } catch (error) {
            console.error(
              `Failed to update permissions for document ${doc.id}:`,
              error
            );
            // Continue with other documents even if one fails
          }
        }
      });

      await Promise.allSettled(permissionUpdates);

      // Also update folder permissions if needed
      if (newConsultantEmail || oldConsultantEmail) {
        await this.updateStudentFolderPermissions(
          studentId,
          oldConsultantEmail,
          newConsultantEmail
        );
      }
    } catch (error) {
      console.error('Error handling consultant assignment change:', error);
      throw new Error(
        `Failed to update consultant permissions: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Update folder permissions for a student when consultant changes
   */
  private static async updateStudentFolderPermissions(
    studentId: string,
    oldConsultantEmail?: string,
    newConsultantEmail?: string
  ): Promise<void> {
    try {
      // Get student information
      const { data: studentProfile } = await this.supabase
        .from('student_profiles')
        .select(
          `
          users!inner(email, profile_data)
        `
        )
        .eq('id', studentId)
        .single();

      if (!studentProfile) {
        throw new Error('Student not found');
      }

      const studentName =
        (studentProfile.users as any)?.profile_data?.name ||
        (studentProfile.users as any)[0]?.profile_data?.name ||
        ((studentProfile.users as any)?.profile_data?.firstName ||
          (studentProfile.users as any)[0]?.profile_data?.firstName) +
          ' ' +
          ((studentProfile.users as any)?.profile_data?.lastName ||
            (studentProfile.users as any)[0]?.profile_data?.lastName) ||
        'Student';

      // Get student's main folder (this will create it if it doesn't exist)
      const { GoogleDriveService } = await import('@/lib/google-apis');
      const studentFolderId =
        await GoogleDriveService.ensureStudentFolderStructure(studentName);

      // Update folder permissions
      if (oldConsultantEmail) {
        try {
          await GoogleDocsService.removeDocumentAccess(
            studentFolderId,
            oldConsultantEmail
          );
        } catch (error) {
          console.error(
            'Failed to remove old consultant folder access:',
            error
          );
        }
      }

      if (newConsultantEmail) {
        try {
          await GoogleDocsService.shareDocument(
            studentFolderId,
            newConsultantEmail,
            'reader'
          );
        } catch (error) {
          console.error('Failed to add new consultant folder access:', error);
        }
      }
    } catch (error) {
      console.error('Error updating student folder permissions:', error);
      // Don't throw here as this is a secondary operation
    }
  }

  /**
   * Bulk permission update for multiple students when a consultant is deactivated
   */
  static async handleConsultantDeactivation(
    consultantId: string
  ): Promise<void> {
    try {
      // Get all active student assignments for this consultant
      const { data: relations, error } = await this.supabase
        .from('student_consultant_relations')
        .select('student_id')
        .eq('consultant_id', consultantId)
        .eq('status', 'active');

      if (error) {
        throw new Error('Failed to fetch consultant assignments');
      }

      if (!relations || relations.length === 0) {
        console.log('No active assignments found for consultant');
        return;
      }

      // Remove consultant access from all assigned students' documents
      const deactivationPromises = relations.map((relation) =>
        this.handleConsultantAssignmentChange(
          relation.student_id,
          consultantId,
          undefined
        )
      );

      await Promise.allSettled(deactivationPromises);

      console.log(`Removed consultant access for ${relations.length} students`);
    } catch (error) {
      console.error('Error handling consultant deactivation:', error);
      throw new Error(
        `Failed to handle consultant deactivation: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Audit permissions for a student's documents
   * Returns a report of current permissions vs expected permissions
   */
  static async auditStudentPermissions(studentId: string): Promise<{
    studentId: string;
    documents: Array<{
      documentId: string;
      googleDocId: string | null;
      expectedPermissions: string[];
      actualPermissions: string[];
      issues: string[];
    }>;
    summary: {
      totalDocuments: number;
      documentsWithIssues: number;
      commonIssues: string[];
    };
  }> {
    try {
      // Get student and consultant information
      const { data: studentProfile } = await this.supabase
        .from('student_profiles')
        .select(
          `
          id,
          users!inner(email, profile_data)
        `
        )
        .eq('id', studentId)
        .single();

      if (!studentProfile) {
        throw new Error('Student not found');
      }

      const { data: consultantRelations } = await this.supabase
        .from('student_consultant_relations')
        .select(
          `
          consultants!inner(
            users!inner(email)
          )
        `
        )
        .eq('student_id', studentId)
        .eq('status', 'active');

      const studentEmail =
        (studentProfile.users as any)?.email ||
        (studentProfile.users as any)?.[0]?.email;
      const consultantEmails =
        consultantRelations?.map(
          (rel) =>
            (rel.consultants as any)?.users?.email ||
            (rel.consultants as any)?.users?.[0]?.email
        ) || [];

      // Get all documents for the student
      const { data: documents } = await this.supabase
        .from('documents')
        .select('id, google_doc_id')
        .eq('student_id', studentId)
        .not('google_doc_id', 'is', null);

      if (!documents || documents.length === 0) {
        return {
          studentId,
          documents: [],
          summary: {
            totalDocuments: 0,
            documentsWithIssues: 0,
            commonIssues: []
          }
        };
      }

      // Expected permissions for each document
      const expectedPermissions = [studentEmail, ...consultantEmails];

      // Audit each document (this would require implementing a method to get actual permissions)
      // For now, we'll return a placeholder structure
      const documentAudits = documents.map((doc) => ({
        documentId: doc.id,
        googleDocId: doc.google_doc_id,
        expectedPermissions,
        actualPermissions: [], // Would be populated by actual Google Drive API call
        issues: [] // Would be populated based on comparison
      }));

      const documentsWithIssues = documentAudits.filter(
        (audit) => audit.issues.length > 0
      );

      return {
        studentId,
        documents: documentAudits,
        summary: {
          totalDocuments: documents.length,
          documentsWithIssues: documentsWithIssues.length,
          commonIssues: []
        }
      };
    } catch (error) {
      console.error('Error auditing student permissions:', error);
      throw new Error(
        `Failed to audit permissions: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Repair permissions for a student's documents
   * Ensures all documents have the correct permissions based on current assignments
   */
  static async repairStudentPermissions(studentId: string): Promise<void> {
    try {
      // Get current consultant assignments
      const { data: consultantRelations } = await this.supabase
        .from('student_consultant_relations')
        .select(
          `
          consultants!inner(
            id,
            users!inner(email)
          )
        `
        )
        .eq('student_id', studentId)
        .eq('status', 'active');

      const consultantEmails =
        consultantRelations?.map(
          (rel) =>
            (rel.consultants as any)?.users?.email ||
            (rel.consultants as any)?.users?.[0]?.email
        ) || [];

      // Get student email
      const { data: studentProfile } = await this.supabase
        .from('student_profiles')
        .select(
          `
          users!inner(email)
        `
        )
        .eq('id', studentId)
        .single();

      if (!studentProfile) {
        throw new Error('Student not found');
      }

      const studentEmail =
        (studentProfile.users as any)?.email ||
        (studentProfile.users as any)?.[0]?.email;

      // Get all documents
      const { data: documents } = await this.supabase
        .from('documents')
        .select('id, google_doc_id')
        .eq('student_id', studentId)
        .not('google_doc_id', 'is', null);

      if (!documents || documents.length === 0) {
        return;
      }

      // Repair permissions for each document
      const repairPromises = documents.map(async (doc) => {
        if (doc.google_doc_id) {
          try {
            await DocumentPermissionService.setupDocumentPermissions(
              doc.google_doc_id,
              studentEmail,
              consultantEmails,
              [] // Admin emails can be added here if needed
            );
            console.log(`Repaired permissions for document ${doc.id}`);
          } catch (error) {
            console.error(
              `Failed to repair permissions for document ${doc.id}:`,
              error
            );
          }
        }
      });

      await Promise.allSettled(repairPromises);
    } catch (error) {
      console.error('Error repairing student permissions:', error);
      throw new Error(
        `Failed to repair permissions: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Get permission summary for admin dashboard
   */
  static async getPermissionSummary(): Promise<{
    totalStudents: number;
    totalDocuments: number;
    documentsWithGoogleDocs: number;
    activeConsultantAssignments: number;
    recentPermissionChanges: Array<{
      studentId: string;
      studentName: string;
      action: string;
      timestamp: string;
    }>;
  }> {
    try {
      // Get counts
      const [
        studentsResult,
        documentsResult,
        googleDocsResult,
        assignmentsResult
      ] = await Promise.all([
        this.supabase
          .from('student_profiles')
          .select('id', { count: 'exact', head: true }),
        this.supabase
          .from('documents')
          .select('id', { count: 'exact', head: true }),
        this.supabase
          .from('documents')
          .select('id', { count: 'exact', head: true })
          .not('google_doc_id', 'is', null),
        this.supabase
          .from('student_consultant_relations')
          .select('id', { count: 'exact', head: true })
          .eq('status', 'active')
      ]);

      return {
        totalStudents: studentsResult.count || 0,
        totalDocuments: documentsResult.count || 0,
        documentsWithGoogleDocs: googleDocsResult.count || 0,
        activeConsultantAssignments: assignmentsResult.count || 0,
        recentPermissionChanges: [] // Would be populated from an audit log table
      };
    } catch (error) {
      console.error('Error getting permission summary:', error);
      throw new Error('Failed to get permission summary');
    }
  }
}
