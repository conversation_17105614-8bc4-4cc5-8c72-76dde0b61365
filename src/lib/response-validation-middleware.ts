import { NextRequest, NextResponse } from 'next/server';
import type {
  ApiResponse,
  ApiSuccessResponse,
  ApiErrorResponse,
  ApiValidationErrorResponse,
  PaginatedApiResponse
} from '@/types/application';

// Response validation configuration
interface ValidationConfig {
  enabled: boolean;
  strictMode: boolean; // Throw errors for invalid responses
  logWarnings: boolean;
  validateSchema: boolean;
}

const defaultConfig: ValidationConfig = {
  enabled: process.env.NODE_ENV === 'development',
  strictMode: false,
  logWarnings: true,
  validateSchema: true
};

// Response validation errors
export class ResponseValidationError extends Error {
  constructor(
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ResponseValidationError';
  }
}

// Main validation function
export function validateApiResponse(
  response: NextResponse,
  config: Partial<ValidationConfig> = {}
): void {
  const validationConfig = { ...defaultConfig, ...config };

  if (!validationConfig.enabled) {
    return;
  }

  try {
    // Extract response body for validation
    const responseClone = response.clone();

    // Check content type
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      handleValidationIssue(
        'Response content-type is not application/json',
        { contentType },
        validationConfig
      );
      return;
    }

    // Validate response structure asynchronously
    responseClone
      .json()
      .then((body) => {
        validateResponseBody(body, validationConfig);
      })
      .catch((error) => {
        handleValidationIssue(
          'Failed to parse response JSON',
          { error: error.message },
          validationConfig
        );
      });
  } catch (error) {
    handleValidationIssue(
      'Response validation failed',
      { error: error instanceof Error ? error.message : 'Unknown error' },
      validationConfig
    );
  }
}

// Validate response body structure
function validateResponseBody(body: any, config: ValidationConfig): void {
  if (!body || typeof body !== 'object') {
    handleValidationIssue('Response body is not an object', { body }, config);
    return;
  }

  // Check for required fields
  if (!('success' in body)) {
    handleValidationIssue(
      'Response missing required "success" field',
      { body },
      config
    );
    return;
  }

  if (!('timestamp' in body)) {
    handleValidationIssue(
      'Response missing "timestamp" field (standard format)',
      { body },
      config
    );
  }

  // Validate based on success status
  if (body.success === true) {
    validateSuccessResponse(body, config);
  } else if (body.success === false) {
    validateErrorResponse(body, config);
  } else {
    handleValidationIssue(
      'Invalid "success" field value (must be boolean)',
      { success: body.success },
      config
    );
  }
}

// Validate success response structure
function validateSuccessResponse(body: any, config: ValidationConfig): void {
  if (!('data' in body)) {
    handleValidationIssue(
      'Success response missing required "data" field',
      { body },
      config
    );
  }

  // Check for pagination structure if it looks like a paginated response
  if (body.meta && body.meta.pagination) {
    validatePaginationMeta(body.meta.pagination, config);
  }

  // Validate timestamp format
  if (body.timestamp && !isValidTimestamp(body.timestamp)) {
    handleValidationIssue(
      'Invalid timestamp format',
      { timestamp: body.timestamp },
      config
    );
  }
}

// Validate error response structure
function validateErrorResponse(body: any, config: ValidationConfig): void {
  if (!('error' in body)) {
    handleValidationIssue(
      'Error response missing required "error" field',
      { body },
      config
    );
    return;
  }

  const error = body.error;

  // For standard format, error should be an object
  if (body.timestamp && typeof error === 'object') {
    if (!error.message) {
      handleValidationIssue(
        'Error object missing required "message" field',
        { error },
        config
      );
    }

    // Validate validation error structure
    if (error.code === 'VALIDATION_ERROR') {
      if (!error.details || !Array.isArray(error.details)) {
        handleValidationIssue(
          'Validation error missing or invalid "details" array',
          { error },
          config
        );
      } else {
        error.details.forEach((detail: any, index: number) => {
          if (!detail.field || !detail.message) {
            handleValidationIssue(
              `Validation error detail ${index} missing required fields`,
              { detail },
              config
            );
          }
        });
      }
    }
  }
}

// Validate pagination metadata
function validatePaginationMeta(
  pagination: any,
  config: ValidationConfig
): void {
  const requiredFields = [
    'page',
    'limit',
    'total',
    'totalPages',
    'hasNext',
    'hasPrev'
  ];

  for (const field of requiredFields) {
    if (!(field in pagination)) {
      handleValidationIssue(
        `Pagination metadata missing required field: ${field}`,
        { pagination },
        config
      );
    }
  }

  // Validate field types
  if (typeof pagination.page !== 'number' || pagination.page < 1) {
    handleValidationIssue(
      'Invalid pagination page value',
      { page: pagination.page },
      config
    );
  }

  if (typeof pagination.limit !== 'number' || pagination.limit < 1) {
    handleValidationIssue(
      'Invalid pagination limit value',
      { limit: pagination.limit },
      config
    );
  }

  if (typeof pagination.total !== 'number' || pagination.total < 0) {
    handleValidationIssue(
      'Invalid pagination total value',
      { total: pagination.total },
      config
    );
  }
}

// Utility functions
function isValidTimestamp(timestamp: string): boolean {
  try {
    const date = new Date(timestamp);
    return !isNaN(date.getTime()) && timestamp.includes('T');
  } catch {
    return false;
  }
}

function handleValidationIssue(
  message: string,
  details: any,
  config: ValidationConfig
): void {
  if (config.logWarnings) {
    console.warn(`[API Response Validation] ${message}`, details);
  }

  if (config.strictMode) {
    throw new ResponseValidationError(message, details);
  }
}

// Middleware wrapper for automatic validation
export function withResponseValidation(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>,
  config: Partial<ValidationConfig> = {}
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    const response = await handler(request, context);

    // Validate the response
    validateApiResponse(response, config);

    return response;
  };
}

// Development-only validation wrapper
export function withDevResponseValidation(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  if (process.env.NODE_ENV !== 'development') {
    return handler;
  }

  return withResponseValidation(handler, {
    enabled: true,
    strictMode: false,
    logWarnings: true,
    validateSchema: true
  });
}
