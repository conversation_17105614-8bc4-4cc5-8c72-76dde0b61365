import { google } from 'googleapis';
import { log } from '@/lib/logger';

// Google APIs configuration
const GOOGLE_SERVICE_ACCOUNT_EMAIL = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!;
const GOOGLE_PRIVATE_KEY = process.env.GOOGLE_PRIVATE_KEY!.replace(
  /\\n/g,
  '\n'
);
const GOOGLE_PROJECT_ID = process.env.GOOGLE_PROJECT_ID!;
const GOOGLE_ADMIN_EMAIL = process.env.GOOGLE_ADMIN_EMAIL; // Optional for domain-wide delegation

if (
  !GOOGLE_SERVICE_ACCOUNT_EMAIL ||
  !GOOGLE_PRIVATE_KEY ||
  !GOOGLE_PROJECT_ID
) {
  throw new Error('Missing Google APIs environment variables');
}

// Create JWT client factory for service account authentication
function createJWTClient(subject?: string) {
  const clientConfig: any = {
    email: GOOGLE_SERVICE_ACCOUNT_EMAIL,
    key: GOOGLE_PRIVATE_KEY,
    scopes: [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/documents'
    ]
  };

  // Add subject for domain-wide delegation if provided
  if (subject) {
    clientConfig.subject = subject;
  }

  return new google.auth.JWT(clientConfig);
}

// Try multiple authentication methods
async function getAuthenticatedClients() {
  log.googleApiEvent('Checking available authentication methods');
  log.debug('Service Account Only authentication available', 'GOOGLE');
  if (GOOGLE_ADMIN_EMAIL) {
    log.debug(`Domain-wide delegation available with ${GOOGLE_ADMIN_EMAIL}`, 'GOOGLE');
  } else {
    log.debug('Domain-wide delegation: DISABLED (no GOOGLE_ADMIN_EMAIL)', 'GOOGLE');
  }

  const authMethods = [
    // Method 1: Domain-wide delegation with admin email (try this first)
    ...(GOOGLE_ADMIN_EMAIL
      ? [
          {
            client: createJWTClient(GOOGLE_ADMIN_EMAIL),
            description: `Domain-wide delegation with ${GOOGLE_ADMIN_EMAIL}`
          }
        ]
      : []),
    // Method 2: Service account only (fallback)
    { client: createJWTClient(), description: 'Service Account Only' }
  ];

  for (const { client, description } of authMethods) {
    try {
      log.googleApiEvent(`Trying authentication method: ${description}`);
      await client.authorize();
      log.googleApiEvent(`Authentication successful: ${description}`);

      const drive = google.drive({ version: 'v3', auth: client });
      const docs = google.docs({ version: 'v1', auth: client });

      return { drive, docs, authMethod: description };
    } catch (error: any) {
      log.googleApiError(
        `Authentication failed: ${description}`,
        error
      );
      continue;
    }
  }

  throw new Error(
    'All authentication methods failed. Please check Google APIs configuration.'
  );
}

// Note: Google APIs clients are now created dynamically in each method

// Document creation and management
export class GoogleDocsService {
  // Create a new Google Doc with proper ownership and folder structure
  static async createDocument(
    title: string,
    studentName: string,
    docType:
      | 'essay'
      | 'personal_statement'
      | 'transcript'
      | 'activity_resume'
      | 'other',
    content?: string,
    templateId?: string
  ): Promise<{ documentId: string; documentUrl: string; folderId: string }> {
    try {
      log.googleApiEvent('Creating Google Document with authentication fallback');

      // Get authenticated clients with fallback methods
      const { drive, docs, authMethod } = await getAuthenticatedClients();
      log.googleApiEvent(`Using authentication method: ${authMethod}`);

      let documentId: string;

      if (templateId) {
        // Create from template
        log.googleApiEvent('Creating document from template', { templateId, title });
        const copyResponse = await drive.files.copy({
          fileId: templateId,
          requestBody: {
            name: title
          }
        });
        documentId = copyResponse.data.id!;
      } else {
        // Create new document
        log.googleApiEvent('Creating new document', { title });
        const docResponse = await docs.documents.create({
          requestBody: {
            title
          }
        });
        documentId = docResponse.data.documentId!;
      }

      log.googleApiEvent('Document created successfully', { documentId });

      // Add initial content if provided (and not using template)
      if (content && !templateId) {
        log.googleApiEvent('Adding initial content to document', { documentId });
        await docs.documents.batchUpdate({
          documentId,
          requestBody: {
            requests: [
              {
                insertText: {
                  location: {
                    index: 1
                  },
                  text: content
                }
              }
            ]
          }
        });
      }

      // Get or create student folder structure
      log.googleApiEvent('Setting up folder structure', { studentName });
      const folderId = await GoogleDriveService.ensureStudentFolderStructure(
        studentName,
        docType
      );

      // Move document to appropriate folder
      log.googleApiEvent('Moving document to folder', { documentId, folderId });
      await drive.files.update({
        fileId: documentId,
        addParents: folderId,
        fields: 'id, parents'
      });

      const documentUrl = `https://docs.google.com/document/d/${documentId}/edit`;
      log.googleApiEvent('Document creation completed successfully', { documentId, documentUrl });

      return { documentId, documentUrl, folderId };
    } catch (error: any) {
      log.googleApiError('Error creating Google Doc', error);

      // Provide more specific error information
      if (error.code === 403) {
        throw new Error(
          `Google Docs permission denied: ${error.message}. Please check domain-wide delegation configuration.`
        );
      } else if (error.code === 404) {
        throw new Error(
          `Google Docs API not found: ${error.message}. Please ensure Google Docs API is enabled.`
        );
      } else {
        throw new Error(`Failed to create Google Document: ${error.message}`);
      }
    }
  }

  // Get document content
  static async getDocumentContent(documentId: string): Promise<string> {
    try {
      // Get authenticated clients
      const { docs } = await getAuthenticatedClients();

      const response = await docs.documents.get({
        documentId
      });

      const content = response.data.body?.content || [];
      let text = '';

      for (const element of content) {
        if (element.paragraph) {
          for (const textElement of element.paragraph.elements || []) {
            if (textElement.textRun) {
              text += textElement.textRun.content || '';
            }
          }
        }
      }

      return text;
    } catch (error) {
      log.googleApiError('Error getting document content', error as Error);
      throw new Error('Failed to get document content');
    }
  }

  // Update document permissions
  static async shareDocument(
    documentId: string,
    email: string,
    role: 'reader' | 'writer' | 'commenter' = 'writer'
  ): Promise<void> {
    try {
      // Get authenticated clients
      const { drive } = await getAuthenticatedClients();

      await drive.permissions.create({
        fileId: documentId,
        requestBody: {
          role,
          type: 'user',
          emailAddress: email
        },
        sendNotificationEmail: true
      });
    } catch (error) {
      console.error('Error sharing document:', error);
      throw new Error('Failed to share document');
    }
  }

  // Remove document permissions
  static async removeDocumentAccess(
    documentId: string,
    email: string
  ): Promise<void> {
    try {
      // Get authenticated clients
      const { drive } = await getAuthenticatedClients();

      // Get all permissions
      const permissions = await drive.permissions.list({
        fileId: documentId
      });

      // Find permission for the email
      const permission = permissions.data.permissions?.find(
        (p) => p.emailAddress === email
      );

      if (permission && permission.id) {
        await drive.permissions.delete({
          fileId: documentId,
          permissionId: permission.id
        });
      }
    } catch (error) {
      console.error('Error removing document access:', error);
      throw new Error('Failed to remove document access');
    }
  }
}

// Folder management
export class GoogleDriveService {
  // Main Lighten folder ID (should be set in environment or created once)
  private static readonly MAIN_FOLDER_NAME = 'Lighten College Consulting';

  // Get or create the main Lighten folder
  static async getOrCreateMainFolder(): Promise<string> {
    try {
      // Get authenticated clients
      const { drive } = await getAuthenticatedClients();

      // Search for existing main folder
      const searchResponse = await drive.files.list({
        q: `name='${this.MAIN_FOLDER_NAME}' and mimeType='application/vnd.google-apps.folder' and trashed=false`,
        fields: 'files(id, name)'
      });

      if (searchResponse.data.files && searchResponse.data.files.length > 0) {
        return searchResponse.data.files[0].id!;
      }

      // Create main folder if it doesn't exist
      const folderResponse = await drive.files.create({
        requestBody: {
          name: this.MAIN_FOLDER_NAME,
          mimeType: 'application/vnd.google-apps.folder'
        }
      });

      return folderResponse.data.id!;
    } catch (error) {
      console.error('Error getting/creating main folder:', error);
      throw new Error('Failed to get/create main folder');
    }
  }

  // Create complete folder structure for a student
  static async ensureStudentFolderStructure(
    studentName: string,
    docType?:
      | 'essay'
      | 'personal_statement'
      | 'transcript'
      | 'activity_resume'
      | 'other'
  ): Promise<string> {
    try {
      const mainFolderId = await this.getOrCreateMainFolder();

      // Get or create Students folder
      const studentsFolderId = await this.getOrCreateSubfolder(
        mainFolderId,
        'Students'
      );

      // Get or create individual student folder
      const studentFolderId = await this.getOrCreateSubfolder(
        studentsFolderId,
        studentName
      );

      // Create document type subfolders
      const subfolders = [
        'Personal Statement',
        'Supplemental Essays',
        'Activity Resume',
        'Transcripts',
        'Other Documents'
      ];

      for (const subfolder of subfolders) {
        await this.getOrCreateSubfolder(studentFolderId, subfolder);
      }

      // Return the appropriate subfolder based on docType
      if (docType) {
        const folderMap = {
          personal_statement: 'Personal Statement',
          essay: 'Supplemental Essays',
          activity_resume: 'Activity Resume',
          transcript: 'Transcripts',
          other: 'Other Documents'
        };

        const targetFolder = folderMap[docType] || 'Other Documents';
        return await this.getOrCreateSubfolder(studentFolderId, targetFolder);
      }

      return studentFolderId;
    } catch (error) {
      console.error('Error ensuring student folder structure:', error);
      throw new Error('Failed to create student folder structure');
    }
  }

  // Helper to get or create a subfolder
  private static async getOrCreateSubfolder(
    parentId: string,
    folderName: string
  ): Promise<string> {
    try {
      // Get authenticated clients
      const { drive } = await getAuthenticatedClients();

      // Search for existing subfolder
      const searchResponse = await drive.files.list({
        q: `name='${folderName}' and '${parentId}' in parents and mimeType='application/vnd.google-apps.folder' and trashed=false`,
        fields: 'files(id, name)'
      });

      if (searchResponse.data.files && searchResponse.data.files.length > 0) {
        return searchResponse.data.files[0].id!;
      }

      // Create subfolder if it doesn't exist
      const folderResponse = await drive.files.create({
        requestBody: {
          name: folderName,
          parents: [parentId],
          mimeType: 'application/vnd.google-apps.folder'
        }
      });

      return folderResponse.data.id!;
    } catch (error) {
      console.error(`Error getting/creating subfolder ${folderName}:`, error);
      throw new Error(`Failed to get/create subfolder: ${folderName}`);
    }
  }

  // Create a folder for a student (legacy method - kept for compatibility)
  static async createStudentFolder(
    studentName: string,
    studentEmail: string
  ): Promise<{ folderId: string; folderUrl: string }> {
    try {
      // Get authenticated clients
      const { drive } = await getAuthenticatedClients();

      const folderId = await this.ensureStudentFolderStructure(studentName);

      // Share folder with student
      await drive.permissions.create({
        fileId: folderId,
        requestBody: {
          role: 'writer',
          type: 'user',
          emailAddress: studentEmail
        },
        sendNotificationEmail: true
      });

      const folderUrl = `https://drive.google.com/drive/folders/${folderId}`;

      return { folderId, folderUrl };
    } catch (error) {
      console.error('Error creating student folder:', error);
      throw new Error('Failed to create student folder');
    }
  }

  // Get files in a folder
  static async getFolderContents(folderId: string): Promise<any[]> {
    try {
      // Get authenticated clients
      const { drive } = await getAuthenticatedClients();

      const response = await drive.files.list({
        q: `'${folderId}' in parents and trashed=false`,
        fields:
          'files(id, name, mimeType, createdTime, modifiedTime, webViewLink)'
      });

      return response.data.files || [];
    } catch (error) {
      console.error('Error getting folder contents:', error);
      throw new Error('Failed to get folder contents');
    }
  }

  // Delete a file or folder
  static async deleteFile(fileId: string): Promise<void> {
    try {
      // Get authenticated clients
      const { drive } = await getAuthenticatedClients();

      await drive.files.delete({
        fileId
      });
    } catch (error) {
      console.error('Error deleting file:', error);
      throw new Error('Failed to delete file');
    }
  }

  // Template management
  static async getOrCreateTemplatesFolder(): Promise<string> {
    try {
      const mainFolderId = await this.getOrCreateMainFolder();
      return await this.getOrCreateSubfolder(mainFolderId, 'Templates');
    } catch (error) {
      console.error('Error getting/creating templates folder:', error);
      throw new Error('Failed to get/create templates folder');
    }
  }

  // Get available templates
  static async getTemplates(): Promise<
    Array<{ id: string; name: string; type: string }>
  > {
    try {
      // Get authenticated clients
      const { drive } = await getAuthenticatedClients();

      const templatesFolderId = await this.getOrCreateTemplatesFolder();

      const response = await drive.files.list({
        q: `'${templatesFolderId}' in parents and trashed=false`,
        fields: 'files(id, name, mimeType, description)'
      });

      return (response.data.files || []).map((file) => ({
        id: file.id!,
        name: file.name!,
        type: file.description || 'general'
      }));
    } catch (error) {
      console.error('Error getting templates:', error);
      throw new Error('Failed to get templates');
    }
  }

  // Move a file to a specific folder
  static async moveToFolder(fileId: string, folderId: string): Promise<void> {
    try {
      // Get authenticated clients
      const { drive } = await getAuthenticatedClients();

      await drive.files.update({
        fileId: fileId,
        addParents: folderId,
        fields: 'id, parents'
      });
    } catch (error) {
      console.error('Error moving file to folder:', error);
      throw new Error('Failed to move file to folder');
    }
  }
}

// Permission management based on user roles
export class DocumentPermissionService {
  // Set up permissions for a document based on user roles
  // Note: Lighten admin account owns all documents, this method shares with users
  static async setupDocumentPermissions(
    documentId: string,
    studentEmail: string,
    consultantEmails: string[] = [],
    adminEmails: string[] = []
  ): Promise<void> {
    try {
      // Give student edit access to their documents
      await GoogleDocsService.shareDocument(documentId, studentEmail, 'writer');

      // Give consultants comment/suggestion access (they can suggest but not directly edit)
      for (const consultantEmail of consultantEmails) {
        await GoogleDocsService.shareDocument(
          documentId,
          consultantEmail,
          'commenter'
        );
      }

      // Give additional admins edit access if needed
      for (const adminEmail of adminEmails) {
        await GoogleDocsService.shareDocument(documentId, adminEmail, 'writer');
      }
    } catch (error) {
      console.error('Error setting up document permissions:', error);
      throw new Error('Failed to set up document permissions');
    }
  }

  // Share entire student folder with appropriate permissions
  static async setupStudentFolderPermissions(
    studentFolderId: string,
    studentEmail: string,
    consultantEmails: string[] = []
  ): Promise<void> {
    try {
      // Get authenticated clients
      const { drive } = await getAuthenticatedClients();

      // Give student edit access to their folder
      await drive.permissions.create({
        fileId: studentFolderId,
        requestBody: {
          role: 'writer',
          type: 'user',
          emailAddress: studentEmail
        },
        sendNotificationEmail: false // Don't spam with notifications
      });

      // Give consultants view access to the student's folder
      for (const consultantEmail of consultantEmails) {
        await drive.permissions.create({
          fileId: studentFolderId,
          requestBody: {
            role: 'reader',
            type: 'user',
            emailAddress: consultantEmail
          },
          sendNotificationEmail: false
        });
      }
    } catch (error) {
      console.error('Error setting up folder permissions:', error);
      throw new Error('Failed to set up folder permissions');
    }
  }

  // Update permissions when consultant assignment changes
  static async updateConsultantAccess(
    documentId: string,
    oldConsultantEmail?: string,
    newConsultantEmail?: string
  ): Promise<void> {
    try {
      // Remove old consultant access
      if (oldConsultantEmail) {
        await GoogleDocsService.removeDocumentAccess(
          documentId,
          oldConsultantEmail
        );
      }

      // Add new consultant access
      if (newConsultantEmail) {
        await GoogleDocsService.shareDocument(
          documentId,
          newConsultantEmail,
          'commenter'
        );
      }
    } catch (error) {
      console.error('Error updating consultant access:', error);
      throw new Error('Failed to update consultant access');
    }
  }
}
