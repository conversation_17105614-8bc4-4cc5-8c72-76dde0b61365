import { supabase } from '@/lib/supabase';
import { Database } from '@/types/database';

type User = {
  id: string;
  role: 'student' | 'consultant' | 'admin';
};

/**
 * Check if user can access student profile
 */
export async function canAccessStudent(
  currentUser: User,
  studentId: string
): Promise<boolean> {
  if (currentUser.role === 'admin') {
    return true;
  }

  if (currentUser.role === 'student') {
    // Check if this is their own profile
    const { data: studentProfile } = await supabase
      .from('student_profiles')
      .select('user_id')
      .eq('id', studentId)
      .single();

    return studentProfile?.user_id === currentUser.id;
  }

  if (currentUser.role === 'consultant') {
    // Check if student is assigned to this consultant
    const { data: consultant } = await supabase
      .from('consultants')
      .select('id')
      .eq('user_id', currentUser.id)
      .single();

    if (!consultant) return false;

    const { data: relation } = await supabase
      .from('student_consultant_relations')
      .select('id')
      .eq('student_id', studentId)
      .eq('consultant_id', consultant.id)
      .eq('status', 'active')
      .single();

    return !!relation;
  }

  return false;
}

/**
 * Check if user can access consultant profile
 */
export async function canAccessConsultant(
  currentUser: User,
  consultantId: string
): Promise<boolean> {
  if (currentUser.role === 'admin') {
    return true;
  }

  if (currentUser.role === 'consultant') {
    // Check if this is their own profile
    const { data: consultant } = await supabase
      .from('consultants')
      .select('user_id')
      .eq('id', consultantId)
      .single();

    return consultant?.user_id === currentUser.id;
  }

  return false;
}

/**
 * Check if user can access a specific document
 */
export async function canAccessDocument(
  currentUser: User,
  documentId: string
): Promise<boolean> {
  const { data: document } = await supabase
    .from('documents')
    .select('student_id')
    .eq('id', documentId)
    .single();

  if (!document) return false;

  if (currentUser.role === 'admin') return true;

  if (currentUser.role === 'student') {
    const { data: studentProfile } = await supabase
      .from('student_profiles')
      .select('user_id')
      .eq('id', document.student_id)
      .single();
    return studentProfile?.user_id === currentUser.id;
  }

  if (currentUser.role === 'consultant') {
    // Check if student is assigned to this consultant
    const { data: consultant } = await supabase
      .from('consultants')
      .select('id')
      .eq('user_id', currentUser.id)
      .single();

    if (!consultant) return false;

    const { data: relation } = await supabase
      .from('student_consultant_relations')
      .select('id')
      .eq('student_id', document.student_id)
      .eq('consultant_id', consultant.id)
      .eq('status', 'active')
      .single();

    return !!relation;
  }

  return false;
}

/**
 * Check if user can access documents for a student
 */
export async function canAccessStudentDocuments(
  currentUser: User,
  studentId: string
): Promise<boolean> {
  return canAccessStudent(currentUser, studentId);
}
