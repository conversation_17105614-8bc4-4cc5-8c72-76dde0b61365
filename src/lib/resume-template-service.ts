import { supabaseAdmin } from '@/lib/supabase-admin';

export interface ActivityData {
  id: string;
  activity_type: string;
  organization_name: string;
  position_title?: string;
  description?: string;
  hours_per_week: number;
  weeks_per_year: number;
  leadership_role: boolean;
  awards_recognition?: string;
  participation_grade_levels?: string[];

  // Competition/Awards details (Goal #10)
  competition_details?: {
    competition_name: string;
    award_level: 'school' | 'regional' | 'state' | 'national' | 'international';
    placement?: string;
    date_received: string;
  };
}

export interface ResumeTemplate {
  id: string;
  name: string;
  description: string;
  focus_area: string;
  template_style: string;
  formatting_options: {
    font_family: string;
    font_size: number;
    line_spacing: number;
    section_spacing: number;
    header_style: 'bold' | 'underline' | 'caps' | 'bold_underline';
    bullet_style: 'bullet' | 'dash' | 'arrow' | 'number';
    color_scheme: 'black_white' | 'blue_accent' | 'green_accent' | 'professional';
  };
  sections: {
    order: string[];
    include_contact: boolean;
    include_summary: boolean;
    include_education: boolean;
    include_activities: boolean;
    include_awards: boolean;
    include_skills: boolean;
  };
  activity_grouping: 'chronological' | 'categorical' | 'impact' | 'leadership';
  max_activities: number;
  word_limit_per_activity: number;
}

export interface ResumeAnalytics {
  activity_distribution: Record<string, number>;
  leadership_percentage: number;
  total_hours_per_week: number;
  activity_diversity_score: number;
  impact_score: number;
  recommendations: string[];
}

export class ResumeTemplateService {
  /**
   * Get available resume templates based on focus area
   */
  static getTemplatesByFocusArea(focusArea: string): ResumeTemplate[] {
    const templates: Record<string, ResumeTemplate[]> = {
      engineering: [
        {
          id: 'engineering_standard',
          name: 'Engineering Standard',
          description: 'Clean, technical format emphasizing STEM activities and projects',
          focus_area: 'engineering',
          template_style: 'standard',
          formatting_options: {
            font_family: 'Arial',
            font_size: 11,
            line_spacing: 1.15,
            section_spacing: 12,
            header_style: 'bold',
            bullet_style: 'bullet',
            color_scheme: 'black_white'
          },
          sections: {
            order: ['contact', 'education', 'activities', 'skills', 'awards'],
            include_contact: true,
            include_summary: false,
            include_education: true,
            include_activities: true,
            include_awards: true,
            include_skills: true
          },
          activity_grouping: 'categorical',
          max_activities: 10,
          word_limit_per_activity: 150
        },
        {
          id: 'engineering_detailed',
          name: 'Engineering Detailed',
          description: 'Comprehensive format with detailed project descriptions and technical skills',
          focus_area: 'engineering',
          template_style: 'detailed',
          formatting_options: {
            font_family: 'Times New Roman',
            font_size: 10,
            line_spacing: 1.0,
            section_spacing: 10,
            header_style: 'bold_underline',
            bullet_style: 'dash',
            color_scheme: 'blue_accent'
          },
          sections: {
            order: ['contact', 'summary', 'education', 'activities', 'skills', 'awards'],
            include_contact: true,
            include_summary: true,
            include_education: true,
            include_activities: true,
            include_awards: true,
            include_skills: true
          },
          activity_grouping: 'impact',
          max_activities: 12,
          word_limit_per_activity: 200
        }
      ],
      liberal_arts: [
        {
          id: 'liberal_arts_narrative',
          name: 'Liberal Arts Narrative',
          description: 'Story-driven format highlighting intellectual curiosity and diverse interests',
          focus_area: 'liberal_arts',
          template_style: 'narrative',
          formatting_options: {
            font_family: 'Georgia',
            font_size: 11,
            line_spacing: 1.2,
            section_spacing: 14,
            header_style: 'caps',
            bullet_style: 'dash',
            color_scheme: 'professional'
          },
          sections: {
            order: ['contact', 'summary', 'activities', 'education', 'awards'],
            include_contact: true,
            include_summary: true,
            include_education: true,
            include_activities: true,
            include_awards: true,
            include_skills: false
          },
          activity_grouping: 'chronological',
          max_activities: 8,
          word_limit_per_activity: 180
        }
      ],
      business: [
        {
          id: 'business_leadership',
          name: 'Business Leadership',
          description: 'Executive-style format emphasizing leadership roles and business impact',
          focus_area: 'business',
          template_style: 'leadership',
          formatting_options: {
            font_family: 'Calibri',
            font_size: 11,
            line_spacing: 1.1,
            section_spacing: 12,
            header_style: 'bold',
            bullet_style: 'arrow',
            color_scheme: 'green_accent'
          },
          sections: {
            order: ['contact', 'summary', 'activities', 'education', 'skills', 'awards'],
            include_contact: true,
            include_summary: true,
            include_education: true,
            include_activities: true,
            include_awards: true,
            include_skills: true
          },
          activity_grouping: 'leadership',
          max_activities: 10,
          word_limit_per_activity: 160
        }
      ]
    };

    return templates[focusArea] || templates['engineering'];
  }

  /**
   * Generate smart activity recommendations based on focus area and existing activities
   */
  static generateActivityRecommendations(
    focusArea: string,
    activities: ActivityData[]
  ): {
    recommended: ActivityData[];
    suggestions: string[];
    gaps: string[];
  } {
    const focusAreaPriorities: Record<string, string[]> = {
      engineering: [
        'Academic Teams',
        'Research',
        'Robotics',
        'Math/Science Competitions',
        'Engineering Clubs',
        'Technology Projects'
      ],
      liberal_arts: [
        'Debate Team',
        'Model UN',
        'Writing/Journalism',
        'Language Clubs',
        'Cultural Organizations',
        'Community Service'
      ],
      business: [
        'Student Government',
        'Business Clubs',
        'Entrepreneurship',
        'Leadership Positions',
        'Internships',
        'Finance/Economics Clubs'
      ],
      pre_med: [
        'Medical Volunteering',
        'Research',
        'Science Olympiad',
        'Health-related Clubs',
        'Community Health',
        'Academic Tutoring'
      ]
    };

    const priorities = focusAreaPriorities[focusArea] || focusAreaPriorities['engineering'];
    
    // Score activities based on relevance to focus area
    const scoredActivities = activities.map(activity => ({
      ...activity,
      relevanceScore: this.calculateRelevanceScore(activity, priorities),
      impactScore: this.calculateImpactScore(activity)
    }));

    // Sort by combined score
    const recommended = scoredActivities
      .sort((a, b) => (b.relevanceScore + b.impactScore) - (a.relevanceScore + a.impactScore))
      .slice(0, 10);

    // Generate suggestions for improvement
    const suggestions = this.generateImprovementSuggestions(activities, focusArea);
    
    // Identify gaps in activity portfolio
    const gaps = this.identifyActivityGaps(activities, priorities);

    return {
      recommended: recommended.map(({ relevanceScore, impactScore, ...activity }) => activity),
      suggestions,
      gaps
    };
  }

  /**
   * Calculate relevance score for an activity based on focus area priorities
   */
  private static calculateRelevanceScore(activity: ActivityData, priorities: string[]): number {
    let score = 0;
    
    // Check activity type against priorities
    priorities.forEach((priority, index) => {
      if (activity.activity_type.toLowerCase().includes(priority.toLowerCase()) ||
          activity.organization_name.toLowerCase().includes(priority.toLowerCase())) {
        score += (priorities.length - index) * 2;
      }
    });

    // Bonus for leadership roles
    if (activity.leadership_role) {
      score += 5;
    }

    // Bonus for high time commitment
    const totalHours = activity.hours_per_week * activity.weeks_per_year;
    if (totalHours > 100) score += 3;
    else if (totalHours > 50) score += 2;
    else if (totalHours > 20) score += 1;

    return score;
  }

  /**
   * Calculate impact score based on activity characteristics
   */
  private static calculateImpactScore(activity: ActivityData): number {
    let score = 0;

    // Leadership bonus
    if (activity.leadership_role) score += 10;

    // Awards/recognition bonus
    if (activity.awards_recognition && activity.awards_recognition.length > 0) score += 5;

    // Time commitment score
    const totalHours = activity.hours_per_week * activity.weeks_per_year;
    score += Math.min(totalHours / 10, 15); // Max 15 points for time commitment

    // Description quality (proxy for impact)
    if (activity.description && activity.description.length > 100) score += 3;

    return score;
  }

  /**
   * Generate improvement suggestions for activities
   */
  private static generateImprovementSuggestions(
    activities: ActivityData[],
    focusArea: string
  ): string[] {
    const suggestions: string[] = [];

    // Check for leadership roles
    const leadershipCount = activities.filter(a => a.leadership_role).length;
    if (leadershipCount < 2) {
      suggestions.push('Consider taking on leadership roles in your existing activities');
    }

    // Check for activity diversity
    const activityTypes = new Set(activities.map(a => a.activity_type));
    if (activityTypes.size < 4) {
      suggestions.push('Diversify your activities across different categories');
    }

    // Check for long-term commitment
    const longTermActivities = activities.filter(a =>
      (a.participation_grade_levels?.length || 0) > 2
    ).length;
    if (longTermActivities < 3) {
      suggestions.push('Show long-term commitment by continuing activities across multiple years');
    }

    // Focus area specific suggestions
    if (focusArea === 'engineering' && !activities.some(a =>
      a.activity_type.toLowerCase().includes('research') ||
      a.activity_type.toLowerCase().includes('project')
    )) {
      suggestions.push('Consider adding research or project-based activities');
    }

    return suggestions;
  }

  /**
   * Identify gaps in activity portfolio
   */
  private static identifyActivityGaps(
    activities: ActivityData[],
    priorities: string[]
  ): string[] {
    const gaps: string[] = [];
    const activityTypes = activities.map(a => a.activity_type.toLowerCase());

    priorities.forEach(priority => {
      const hasActivity = activityTypes.some(type =>
        type.includes(priority.toLowerCase())
      );
      if (!hasActivity) {
        gaps.push(priority);
      }
    });

    return gaps.slice(0, 3); // Return top 3 gaps
  }

  /**
   * Generate resume analytics for a set of activities
   */
  static generateResumeAnalytics(activities: ActivityData[]): ResumeAnalytics {
    // Activity distribution by type
    const activityDistribution: Record<string, number> = {};
    activities.forEach(activity => {
      activityDistribution[activity.activity_type] =
        (activityDistribution[activity.activity_type] || 0) + 1;
    });

    // Leadership percentage
    const leadershipCount = activities.filter(a => a.leadership_role).length;
    const leadershipPercentage = activities.length > 0 ?
      (leadershipCount / activities.length) * 100 : 0;

    // Total hours per week
    const totalHoursPerWeek = activities.reduce((sum, activity) =>
      sum + activity.hours_per_week, 0);

    // Activity diversity score (0-100)
    const uniqueTypes = new Set(activities.map(a => a.activity_type)).size;
    const diversityScore = Math.min((uniqueTypes / 8) * 100, 100);

    // Impact score (0-100)
    const totalImpactScore = activities.reduce((sum, activity) =>
      sum + this.calculateImpactScore(activity), 0);
    const averageImpactScore = activities.length > 0 ?
      totalImpactScore / activities.length : 0;
    const impactScore = Math.min((averageImpactScore / 30) * 100, 100);

    // Generate recommendations
    const recommendations = this.generateAnalyticsRecommendations({
      leadershipPercentage,
      diversityScore,
      impactScore,
      totalHoursPerWeek,
      activityCount: activities.length
    });

    return {
      activity_distribution: activityDistribution,
      leadership_percentage: leadershipPercentage,
      total_hours_per_week: totalHoursPerWeek,
      activity_diversity_score: diversityScore,
      impact_score: impactScore,
      recommendations
    };
  }

  /**
   * Generate analytics-based recommendations
   */
  private static generateAnalyticsRecommendations(metrics: {
    leadershipPercentage: number;
    diversityScore: number;
    impactScore: number;
    totalHoursPerWeek: number;
    activityCount: number;
  }): string[] {
    const recommendations: string[] = [];

    if (metrics.leadershipPercentage < 20) {
      recommendations.push('Seek leadership opportunities in your current activities');
    }

    if (metrics.diversityScore < 50) {
      recommendations.push('Consider adding activities from different categories');
    }

    if (metrics.impactScore < 60) {
      recommendations.push('Focus on activities where you can make a measurable impact');
    }

    if (metrics.totalHoursPerWeek < 10) {
      recommendations.push('Consider increasing your activity involvement');
    } else if (metrics.totalHoursPerWeek > 40) {
      recommendations.push('Consider focusing on fewer, high-impact activities');
    }

    if (metrics.activityCount < 5) {
      recommendations.push('Add more activities to demonstrate diverse interests');
    } else if (metrics.activityCount > 15) {
      recommendations.push('Focus on your most impactful activities for your resume');
    }

    return recommendations;
  }

  /**
   * Format activities for Google Docs based on template
   */
  static formatActivitiesForGoogleDocs(
    activities: ActivityData[],
    template: ResumeTemplate
  ): string {
    const { recommended } = this.generateActivityRecommendations(
      template.focus_area,
      activities
    );

    // Limit activities based on template
    const selectedActivities = recommended.slice(0, template.max_activities);

    // Group activities based on template preference
    const groupedActivities = this.groupActivities(
      selectedActivities,
      template.activity_grouping
    );

    let formattedContent = '';

    // Add header
    formattedContent += `EXTRACURRICULAR ACTIVITIES\n\n`;

    // Format each group
    Object.entries(groupedActivities).forEach(([groupName, groupActivities]) => {
      if (template.activity_grouping !== 'chronological') {
        formattedContent += `${groupName.toUpperCase()}\n`;
      }

      groupActivities.forEach((activity, index) => {
        const bullet = this.getBulletStyle(template.formatting_options.bullet_style);

        formattedContent += `${bullet} ${activity.organization_name}`;

        if (activity.position_title) {
          formattedContent += ` - ${activity.position_title}`;
        }

        if (activity.leadership_role) {
          formattedContent += ` (Leadership Role)`;
        }

        formattedContent += `\n`;

        // Add time commitment
        formattedContent += `   ${activity.hours_per_week} hours/week, ${activity.weeks_per_year} weeks/year\n`;

        // Add description (limited by template)
        if (activity.description) {
          const limitedDescription = activity.description.length > template.word_limit_per_activity
            ? activity.description.substring(0, template.word_limit_per_activity) + '...'
            : activity.description;
          formattedContent += `   ${limitedDescription}\n`;
        }

        // Add awards if present
        if (activity.awards_recognition) {
          formattedContent += `   Awards: ${activity.awards_recognition}\n`;
        }

        // Add competition details if present (Goal #10)
        if (activity.competition_details) {
          const { competition_name, award_level, placement, date_received } = activity.competition_details;
          formattedContent += `   Competition: ${competition_name} (${award_level.charAt(0).toUpperCase() + award_level.slice(1)} Level)`;
          if (placement) {
            formattedContent += ` - ${placement}`;
          }
          formattedContent += ` - ${new Date(date_received).getFullYear()}\n`;
        }

        formattedContent += `\n`;
      });

      formattedContent += `\n`;
    });

    return formattedContent;
  }

  /**
   * Group activities based on grouping strategy
   */
  private static groupActivities(
    activities: ActivityData[],
    grouping: 'chronological' | 'categorical' | 'impact' | 'leadership'
  ): Record<string, ActivityData[]> {
    switch (grouping) {
      case 'categorical':
        return activities.reduce((groups, activity) => {
          const category = activity.activity_type;
          if (!groups[category]) groups[category] = [];
          groups[category].push(activity);
          return groups;
        }, {} as Record<string, ActivityData[]>);

      case 'leadership':
        return {
          'Leadership Roles': activities.filter(a => a.leadership_role),
          'Member Roles': activities.filter(a => !a.leadership_role)
        };

      case 'impact':
        const highImpact = activities.filter(a => this.calculateImpactScore(a) > 20);
        const mediumImpact = activities.filter(a => {
          const score = this.calculateImpactScore(a);
          return score >= 10 && score <= 20;
        });
        const standardImpact = activities.filter(a => this.calculateImpactScore(a) < 10);

        return {
          'High Impact Activities': highImpact,
          'Significant Activities': mediumImpact,
          'Additional Activities': standardImpact
        };

      case 'chronological':
      default:
        return { 'Activities': activities };
    }
  }

  /**
   * Get bullet style character
   */
  private static getBulletStyle(style: string): string {
    switch (style) {
      case 'dash': return '–';
      case 'arrow': return '→';
      case 'number': return '1.'; // This would need to be dynamic
      case 'bullet':
      default: return '•';
    }
  }
}
