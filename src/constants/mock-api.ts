import { Product } from './data';

export const fakeProducts: Product[] = [
  {
    id: 1,
    name: 'Wireless Headphones',
    category: 'Electronics',
    price: 99.99,
    photo_url: '/placeholder.svg',
    description: 'High-quality wireless headphones with noise cancellation',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: 'Smart Watch',
    category: 'Electronics',
    price: 199.99,
    photo_url: '/placeholder.svg',
    description: 'Feature-rich smartwatch with health monitoring',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 3,
    name: 'Coffee Maker',
    category: 'Appliances',
    price: 79.99,
    photo_url: '/placeholder.svg',
    description: 'Programmable coffee maker with thermal carafe',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 4,
    name: '<PERSON><PERSON>',
    category: 'Furniture',
    price: 49.99,
    photo_url: '/placeholder.svg',
    description: 'Adjustable LED desk lamp with USB charging port',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 5,
    name: 'Bluetooth Speaker',
    category: 'Electronics',
    price: 59.99,
    photo_url: '/placeholder.svg',
    description: 'Portable Bluetooth speaker with excellent sound quality',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

// Mock API functions
export const getProducts = async (filters?: {
  page?: number;
  limit?: number;
  search?: string;
  categories?: string[];
}): Promise<{ products: Product[]; totalCount: number }> => {
  let filteredProducts = [...fakeProducts];

  // Apply search filter
  if (filters?.search) {
    filteredProducts = filteredProducts.filter(product =>
      product.name.toLowerCase().includes(filters.search!.toLowerCase()) ||
      product.description.toLowerCase().includes(filters.search!.toLowerCase())
    );
  }

  // Apply category filter
  if (filters?.categories && filters.categories.length > 0) {
    filteredProducts = filteredProducts.filter(product =>
      filters.categories!.includes(product.category)
    );
  }

  // Apply pagination
  const page = filters?.page || 1;
  const limit = filters?.limit || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

  return {
    products: paginatedProducts,
    totalCount: filteredProducts.length
  };
};

export const getProduct = async (id: string): Promise<Product | null> => {
  return fakeProducts.find(product => product.id === parseInt(id)) || null;
};
