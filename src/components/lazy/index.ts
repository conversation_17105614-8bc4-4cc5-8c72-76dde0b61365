/**
 * Lazy-loaded components for better performance
 */

import { lazy, Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Loading fallback components
const DashboardSkeleton = () => (
  <div className="space-y-4 p-4">
    <Skeleton className="h-8 w-64" />
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Skeleton className="h-32" />
      <Skeleton className="h-32" />
      <Skeleton className="h-32" />
      <Skeleton className="h-32" />
    </div>
    <Skeleton className="h-64" />
  </div>
);

const FormSkeleton = () => (
  <div className="space-y-4 p-4">
    <Skeleton className="h-8 w-48" />
    <div className="space-y-2">
      <Skeleton className="h-4 w-24" />
      <Skeleton className="h-10 w-full" />
    </div>
    <div className="space-y-2">
      <Skeleton className="h-4 w-24" />
      <Skeleton className="h-10 w-full" />
    </div>
    <div className="space-y-2">
      <Skeleton className="h-4 w-24" />
      <Skeleton className="h-32 w-full" />
    </div>
    <Skeleton className="h-10 w-24" />
  </div>
);

const TableSkeleton = () => (
  <div className="space-y-4 p-4">
    <Skeleton className="h-8 w-48" />
    <div className="space-y-2">
      <Skeleton className="h-12 w-full" />
      <Skeleton className="h-12 w-full" />
      <Skeleton className="h-12 w-full" />
      <Skeleton className="h-12 w-full" />
      <Skeleton className="h-12 w-full" />
    </div>
  </div>
);

// Lazy-loaded dashboard components
export const LazyStudentDashboard = lazy(() => 
  import('@/components/dashboard/student-dashboard').then(module => ({
    default: module.default
  }))
);

export const LazyConsultantDashboard = lazy(() => 
  import('@/components/dashboard/consultant-dashboard').then(module => ({
    default: module.default
  }))
);

export const LazyAdminDashboard = lazy(() => 
  import('@/components/dashboard/admin-dashboard').then(module => ({
    default: module.default
  }))
);

// Lazy-loaded form components
export const LazyCreateDocumentForm = lazy(() => 
  import('@/components/documents/create-document-form').then(module => ({
    default: module.CreateDocumentForm
  }))
);

export const LazyAssignConsultantDialog = lazy(() => 
  import('@/components/admin/assign-consultant-dialog').then(module => ({
    default: module.AssignConsultantDialog
  }))
);

// Lazy-loaded analytics components
export const LazyAnalyticsCharts = lazy(() => 
  import('@/components/analytics/charts').catch(() => ({
    default: () => <div>Analytics charts not available</div>
  }))
);

// Lazy-loaded document components
export const LazyGoogleDocsViewer = lazy(() => 
  import('@/components/documents/google-docs-viewer').then(module => ({
    default: module.GoogleDocsViewer
  }))
);

// Lazy-loaded kanban board (heavy component)
export const LazyKanbanBoard = lazy(() => 
  import('@/app/dashboard/kanban/page').then(module => ({
    default: module.default
  }))
);

// Higher-order component for lazy loading with custom fallback
export function withLazyLoading<T extends Record<string, any>>(
  LazyComponent: React.LazyExoticComponent<React.ComponentType<T>>,
  fallback?: React.ComponentType
) {
  return function LazyWrapper(props: T) {
    const FallbackComponent = fallback || DashboardSkeleton;
    
    return (
      <Suspense fallback={<FallbackComponent />}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
}

// Wrapped components with appropriate skeletons
export const StudentDashboard = withLazyLoading(LazyStudentDashboard, DashboardSkeleton);
export const ConsultantDashboard = withLazyLoading(LazyConsultantDashboard, DashboardSkeleton);
export const AdminDashboard = withLazyLoading(LazyAdminDashboard, DashboardSkeleton);
export const CreateDocumentForm = withLazyLoading(LazyCreateDocumentForm, FormSkeleton);
export const AssignConsultantDialog = withLazyLoading(LazyAssignConsultantDialog, FormSkeleton);
export const GoogleDocsViewer = withLazyLoading(LazyGoogleDocsViewer, () => (
  <div className="flex items-center justify-center h-64">
    <Skeleton className="h-full w-full" />
  </div>
));
export const KanbanBoard = withLazyLoading(LazyKanbanBoard, () => (
  <div className="space-y-4 p-4">
    <Skeleton className="h-8 w-48" />
    <div className="grid gap-4 md:grid-cols-3">
      <div className="space-y-2">
        <Skeleton className="h-8 w-24" />
        <Skeleton className="h-32" />
        <Skeleton className="h-32" />
      </div>
      <div className="space-y-2">
        <Skeleton className="h-8 w-24" />
        <Skeleton className="h-32" />
        <Skeleton className="h-32" />
      </div>
      <div className="space-y-2">
        <Skeleton className="h-8 w-24" />
        <Skeleton className="h-32" />
        <Skeleton className="h-32" />
      </div>
    </div>
  </div>
));

// Preload function for critical components
export function preloadCriticalComponents() {
  // Preload dashboard components based on user role
  if (typeof window !== 'undefined') {
    // This will start loading the components in the background
    import('@/components/dashboard/student-dashboard');
    import('@/components/dashboard/consultant-dashboard');
    import('@/components/dashboard/admin-dashboard');
  }
}

// Component preloader hook
export function useComponentPreloader(components: string[]) {
  React.useEffect(() => {
    const preloadPromises = components.map(component => {
      switch (component) {
        case 'student-dashboard':
          return import('@/components/dashboard/student-dashboard');
        case 'consultant-dashboard':
          return import('@/components/dashboard/consultant-dashboard');
        case 'admin-dashboard':
          return import('@/components/dashboard/admin-dashboard');
        case 'create-document-form':
          return import('@/components/documents/create-document-form');
        case 'google-docs-viewer':
          return import('@/components/documents/google-docs-viewer');
        case 'kanban-board':
          return import('@/app/dashboard/kanban/page');
        default:
          return Promise.resolve();
      }
    });

    Promise.all(preloadPromises).catch(error => {
      console.warn('Failed to preload components:', error);
    });
  }, [components]);
}

import React from 'react';
