'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Icons } from '@/components/icons';
import { useApiGet } from '@/lib/api-client';
import {
  ResumeTemplateService,
  ActivityData,
  ResumeAnalytics
} from '@/lib/resume-template-service';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';

interface ActivityResume {
  id: string;
  title: string;
  focus_area: string;
  selected_activities: string[];
  is_default: boolean;
  created_at: string;
}

interface ResumeAnalyticsDashboardProps {
  resumeId?: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export function ResumeAnalyticsDashboard({ resumeId }: ResumeAnalyticsDashboardProps) {
  const [selectedResumeId, setSelectedResumeId] = useState<string | null>(resumeId || null);
  const [analytics, setAnalytics] = useState<ResumeAnalytics | null>(null);
  const [comparisonData, setComparisonData] = useState<any[]>([]);

  const {
    data: activities,
    loading: activitiesLoading
  } = useApiGet<ActivityData[]>('/api/activities');

  const {
    data: resumes,
    loading: resumesLoading
  } = useApiGet<ActivityResume[]>('/api/activity-resumes');

  const {
    data: selectedResume,
    loading: resumeLoading
  } = useApiGet<ActivityResume>(
    selectedResumeId ? `/api/activity-resumes/${selectedResumeId}` : null
  );

  const activityList = activities || [];
  const resumeList = resumes || [];

  // Generate analytics when resume is selected
  useEffect(() => {
    if (selectedResume && activityList.length > 0) {
      const selectedActivities = activityList.filter(activity =>
        selectedResume.selected_activities.includes(activity.id)
      );
      const resumeAnalytics = ResumeTemplateService.generateResumeAnalytics(selectedActivities);
      setAnalytics(resumeAnalytics);
    }
  }, [selectedResume, activityList]);

  // Generate comparison data for all resumes
  useEffect(() => {
    if (resumeList.length > 0 && activityList.length > 0) {
      const comparison = resumeList.map(resume => {
        const selectedActivities = activityList.filter(activity =>
          resume.selected_activities.includes(activity.id)
        );
        const analytics = ResumeTemplateService.generateResumeAnalytics(selectedActivities);
        
        return {
          name: resume.title,
          id: resume.id,
          leadership: Math.round(analytics.leadership_percentage),
          diversity: Math.round(analytics.activity_diversity_score),
          impact: Math.round(analytics.impact_score),
          activities: selectedActivities.length,
          hours: analytics.total_hours_per_week,
          focus_area: resume.focus_area
        };
      });
      setComparisonData(comparison);
    }
  }, [resumeList, activityList]);

  if (activitiesLoading || resumesLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Icons.spinner className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading analytics...</span>
      </div>
    );
  }

  if (resumeList.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Icons.barChart className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Resume Analytics Available</h3>
          <p className="text-muted-foreground text-center mb-6">
            Create activity resumes to see detailed analytics and insights.
          </p>
          <Button asChild>
            <a href="/dashboard/activities/resumes/new">
              <Icons.plus className="mr-2 h-4 w-4" />
              Create Your First Resume
            </a>
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Prepare chart data
  const activityDistributionData = analytics ? 
    Object.entries(analytics.activity_distribution).map(([type, count]) => ({
      name: type,
      value: count
    })) : [];

  const radarData = analytics ? [
    {
      subject: 'Leadership',
      value: analytics.leadership_percentage,
      fullMark: 100
    },
    {
      subject: 'Diversity',
      value: analytics.activity_diversity_score,
      fullMark: 100
    },
    {
      subject: 'Impact',
      value: analytics.impact_score,
      fullMark: 100
    },
    {
      subject: 'Time Commitment',
      value: Math.min((analytics.total_hours_per_week / 40) * 100, 100),
      fullMark: 100
    }
  ] : [];

  return (
    <div className="space-y-6">
      {/* Resume Selector */}
      <Card>
        <CardHeader>
          <CardTitle>Resume Analytics Dashboard</CardTitle>
          <CardDescription>
            Analyze and compare your activity resume versions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {resumeList.map((resume) => (
              <Button
                key={resume.id}
                variant={selectedResumeId === resume.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedResumeId(resume.id)}
              >
                {resume.title}
                {resume.is_default && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    Default
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {selectedResumeId && analytics && (
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="distribution">Activity Distribution</TabsTrigger>
            <TabsTrigger value="comparison">Resume Comparison</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Leadership Rate</CardTitle>
                  <Icons.crown className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Math.round(analytics.leadership_percentage)}%
                  </div>
                  <Progress value={analytics.leadership_percentage} className="mt-2" />
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Activity Diversity</CardTitle>
                  <Icons.activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Math.round(analytics.activity_diversity_score)}%
                  </div>
                  <Progress value={analytics.activity_diversity_score} className="mt-2" />
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Impact Score</CardTitle>
                  <Icons.target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Math.round(analytics.impact_score)}%
                  </div>
                  <Progress value={analytics.impact_score} className="mt-2" />
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Weekly Hours</CardTitle>
                  <Icons.clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {analytics.total_hours_per_week}h
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Total commitment
                  </p>
                </CardContent>
              </Card>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Radar</CardTitle>
                  <CardDescription>
                    Overall resume strength across key metrics
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <RadarChart data={radarData}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="subject" />
                      <PolarRadiusAxis angle={90} domain={[0, 100]} />
                      <Radar
                        name="Score"
                        dataKey="value"
                        stroke="#8884d8"
                        fill="#8884d8"
                        fillOpacity={0.3}
                      />
                    </RadarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Activity Types</CardTitle>
                  <CardDescription>
                    Distribution of activities by category
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={activityDistributionData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {activityDistributionData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Distribution Tab */}
          <TabsContent value="distribution" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Activity Distribution Analysis</CardTitle>
                <CardDescription>
                  Detailed breakdown of your activity portfolio
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(analytics.activity_distribution).map(([type, count]) => (
                    <div key={type} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 rounded bg-primary/20" />
                        <span className="font-medium">{type}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground">{count} activities</span>
                        <Badge variant="secondary">
                          {Math.round((count / Object.values(analytics.activity_distribution).reduce((a, b) => a + b, 0)) * 100)}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Comparison Tab */}
          <TabsContent value="comparison" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Resume Comparison</CardTitle>
                <CardDescription>
                  Compare metrics across all your resume versions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={comparisonData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="leadership" fill="#8884d8" name="Leadership %" />
                    <Bar dataKey="diversity" fill="#82ca9d" name="Diversity %" />
                    <Bar dataKey="impact" fill="#ffc658" name="Impact %" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Recommendations Tab */}
          <TabsContent value="recommendations" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Improvement Recommendations</CardTitle>
                <CardDescription>
                  Personalized suggestions to strengthen your resume
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.recommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg">
                      <Icons.lightbulb className="h-5 w-5 text-yellow-500 mt-0.5" />
                      <div>
                        <p className="text-sm">{recommendation}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
