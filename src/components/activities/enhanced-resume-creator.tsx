'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Icons } from '@/components/icons';
import { useApiGet } from '@/lib/api-client';
import {
  ResumeTemplateService,
  ActivityData,
  ResumeTemplate,
  ResumeAnalytics
} from '@/lib/resume-template-service';

interface EnhancedResumeCreatorProps {
  onResumeCreated?: (resumeId: string) => void;
}

interface CreateResumeForm {
  title: string;
  description: string;
  focus_area: string;
  selected_activities: string[];
  template_style: string;
  is_default: boolean;
  custom_template?: ResumeTemplate;
}

const focusAreaOptions = [
  { value: 'engineering', label: 'Engineering & Technology' },
  { value: 'liberal_arts', label: 'Liberal Arts & Humanities' },
  { value: 'business', label: 'Business & Economics' },
  { value: 'sciences', label: 'Natural Sciences' },
  { value: 'social_sciences', label: 'Social Sciences' },
  { value: 'arts', label: 'Arts & Creative' },
  { value: 'pre_med', label: 'Pre-Medical' },
  { value: 'pre_law', label: 'Pre-Law' },
  { value: 'education', label: 'Education' },
  { value: 'general', label: 'General Application' }
];

export function EnhancedResumeCreator({ onResumeCreated }: EnhancedResumeCreatorProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<CreateResumeForm>({
    title: '',
    description: '',
    focus_area: '',
    selected_activities: [],
    template_style: 'standard',
    is_default: false
  });

  const [availableTemplates, setAvailableTemplates] = useState<ResumeTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ResumeTemplate | null>(null);
  const [activityRecommendations, setActivityRecommendations] = useState<{
    recommended: ActivityData[];
    suggestions: string[];
    gaps: string[];
  } | null>(null);
  const [resumeAnalytics, setResumeAnalytics] = useState<ResumeAnalytics | null>(null);

  const {
    data: activities,
    loading: activitiesLoading,
    error: activitiesError
  } = useApiGet<ActivityData[]>('/api/activities');

  const activityList = activities || [];

  // Update templates when focus area changes
  useEffect(() => {
    if (formData.focus_area) {
      const templates = ResumeTemplateService.getTemplatesByFocusArea(formData.focus_area);
      setAvailableTemplates(templates);
      
      // Auto-select first template
      if (templates.length > 0) {
        setSelectedTemplate(templates[0]);
        setFormData(prev => ({ ...prev, template_style: templates[0].template_style }));
      }

      // Generate activity recommendations
      if (activityList.length > 0) {
        const recommendations = ResumeTemplateService.generateActivityRecommendations(
          formData.focus_area,
          activityList
        );
        setActivityRecommendations(recommendations);

        // Auto-select recommended activities
        const recommendedIds = recommendations.recommended.slice(0, 8).map(a => a.id);
        setFormData(prev => ({ ...prev, selected_activities: recommendedIds }));
      }
    }
  }, [formData.focus_area, activityList]);

  // Update analytics when activities are selected
  useEffect(() => {
    if (formData.selected_activities.length > 0) {
      const selectedActivitiesData = activityList.filter(activity =>
        formData.selected_activities.includes(activity.id)
      );
      const analytics = ResumeTemplateService.generateResumeAnalytics(selectedActivitiesData);
      setResumeAnalytics(analytics);
    }
  }, [formData.selected_activities, activityList]);

  const handleInputChange = (field: keyof CreateResumeForm, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleActivityToggle = (activityId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      selected_activities: checked
        ? [...prev.selected_activities, activityId]
        : prev.selected_activities.filter(id => id !== activityId)
    }));
  };

  const handleTemplateSelect = (template: ResumeTemplate) => {
    setSelectedTemplate(template);
    setFormData(prev => ({
      ...prev,
      template_style: template.template_style,
      custom_template: template
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      alert('Please enter a title for your resume');
      return;
    }

    if (!formData.focus_area) {
      alert('Please select a focus area');
      return;
    }

    if (formData.selected_activities.length === 0) {
      alert('Please select at least one activity');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/activity-resumes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          template_metadata: selectedTemplate
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (onResumeCreated) {
          onResumeCreated(result.data.id);
        } else {
          router.push(`/dashboard/activities/resumes/${result.data.id}`);
        }
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || 'Failed to create resume'}`);
      }
    } catch (error) {
      console.error('Error creating resume:', error);
      alert('Failed to create resume');
    } finally {
      setLoading(false);
    }
  };

  const selectedActivitiesData = activityList.filter(activity =>
    formData.selected_activities.includes(activity.id)
  );

  const getStepStatus = (step: number) => {
    if (step < currentStep) return 'complete';
    if (step === currentStep) return 'current';
    return 'upcoming';
  };

  if (activitiesLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Icons.spinner className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading activities...</span>
      </div>
    );
  }

  if (activityList.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Icons.activity className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Activities Found</h3>
          <p className="text-muted-foreground text-center mb-6">
            You need to add activities before creating a resume.
          </p>
          <Button asChild>
            <a href="/dashboard/activities/add">
              <Icons.plus className="mr-2 h-4 w-4" />
              Add Your First Activity
            </a>
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Progress Steps */}
      <div className="flex items-center justify-between">
        {[1, 2, 3, 4].map((step) => (
          <div key={step} className="flex items-center">
            <div
              className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
                getStepStatus(step) === 'complete'
                  ? 'bg-primary text-primary-foreground'
                  : getStepStatus(step) === 'current'
                  ? 'bg-primary/20 text-primary'
                  : 'bg-muted text-muted-foreground'
              }`}
            >
              {getStepStatus(step) === 'complete' ? (
                <Icons.check className="h-4 w-4" />
              ) : (
                step
              )}
            </div>
            {step < 4 && (
              <div
                className={`ml-2 h-0.5 w-16 ${
                  getStepStatus(step) === 'complete' ? 'bg-primary' : 'bg-muted'
                }`}
              />
            )}
          </div>
        ))}
      </div>

      <form onSubmit={handleSubmit}>
        <Tabs value={currentStep.toString()} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="1">Basic Info</TabsTrigger>
            <TabsTrigger value="2">Template</TabsTrigger>
            <TabsTrigger value="3">Activities</TabsTrigger>
            <TabsTrigger value="4">Review</TabsTrigger>
          </TabsList>

          {/* Step 1: Basic Information */}
          <TabsContent value="1" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Resume Details</CardTitle>
                <CardDescription>
                  Basic information about this resume version
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Resume Title *</Label>
                  <Input
                    id="title"
                    placeholder="e.g., Engineering Focus Resume"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Brief description of this resume version..."
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="focus_area">Focus Area *</Label>
                  <Select
                    value={formData.focus_area}
                    onValueChange={(value) => handleInputChange('focus_area', value)}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select focus area" />
                    </SelectTrigger>
                    <SelectContent>
                      {focusAreaOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_default"
                    checked={formData.is_default}
                    onCheckedChange={(checked) => 
                      handleInputChange('is_default', checked === true)
                    }
                  />
                  <Label htmlFor="is_default">Set as default resume</Label>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button
                type="button"
                onClick={() => setCurrentStep(2)}
                disabled={!formData.title || !formData.focus_area}
              >
                Next: Choose Template
                <Icons.chevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </TabsContent>

          {/* Step 2: Template Selection */}
          <TabsContent value="2" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Choose Template Style</CardTitle>
                <CardDescription>
                  Select a template that best fits your focus area and preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  {availableTemplates.map((template) => (
                    <div
                      key={template.id}
                      className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                        selectedTemplate?.id === template.id
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => handleTemplateSelect(template)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-semibold">{template.name}</h3>
                          <p className="text-sm text-muted-foreground mt-1">
                            {template.description}
                          </p>
                          <div className="flex flex-wrap gap-2 mt-3">
                            <Badge variant="secondary" className="text-xs">
                              {template.formatting_options.font_family}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {template.activity_grouping}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              Max {template.max_activities} activities
                            </Badge>
                          </div>
                        </div>
                        {selectedTemplate?.id === template.id && (
                          <Icons.check className="h-5 w-5 text-primary" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {selectedTemplate && (
                  <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                    <h4 className="font-medium mb-2">Template Details</h4>
                    <div className="grid gap-2 text-sm">
                      <div className="flex justify-between">
                        <span>Font:</span>
                        <span>{selectedTemplate.formatting_options.font_family}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Activity Grouping:</span>
                        <span className="capitalize">{selectedTemplate.activity_grouping}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Max Activities:</span>
                        <span>{selectedTemplate.max_activities}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Word Limit per Activity:</span>
                        <span>{selectedTemplate.word_limit_per_activity} words</span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep(1)}
              >
                <Icons.chevronLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button
                type="button"
                onClick={() => setCurrentStep(3)}
                disabled={!selectedTemplate}
              >
                Next: Select Activities
                <Icons.chevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </TabsContent>

          {/* Step 3: Activity Selection */}
          <TabsContent value="3" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-3">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Select Activities</CardTitle>
                    <CardDescription>
                      Choose activities that best represent your {formData.focus_area} focus
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {activityRecommendations && (
                      <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                        <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                          Smart Recommendations
                        </h4>
                        <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                          Based on your {formData.focus_area} focus, we've pre-selected your most relevant activities.
                        </p>
                        {activityRecommendations.suggestions.length > 0 && (
                          <div className="space-y-1">
                            <p className="text-xs font-medium text-blue-900 dark:text-blue-100">
                              Suggestions:
                            </p>
                            {activityRecommendations.suggestions.map((suggestion, index) => (
                              <p key={index} className="text-xs text-blue-700 dark:text-blue-300">
                                • {suggestion}
                              </p>
                            ))}
                          </div>
                        )}
                      </div>
                    )}

                    <div className="space-y-3">
                      {activityList.map((activity) => {
                        const isRecommended = activityRecommendations?.recommended
                          .some(rec => rec.id === activity.id);

                        return (
                          <div
                            key={activity.id}
                            className={`flex items-start space-x-3 rounded-lg border p-3 ${
                              isRecommended ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20' : ''
                            }`}
                          >
                            <Checkbox
                              id={`activity-${activity.id}`}
                              checked={formData.selected_activities.includes(activity.id)}
                              onCheckedChange={(checked) =>
                                handleActivityToggle(activity.id, checked === true)
                              }
                            />
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <Label
                                  htmlFor={`activity-${activity.id}`}
                                  className="font-medium cursor-pointer"
                                >
                                  {activity.organization_name}
                                  {isRecommended && (
                                    <Badge variant="secondary" className="ml-2 text-xs">
                                      Recommended
                                    </Badge>
                                  )}
                                </Label>
                                {activity.leadership_role && (
                                  <Icons.crown className="h-4 w-4 text-yellow-500" />
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {activity.position_title && `${activity.position_title} • `}
                                {activity.activity_type} • {activity.hours_per_week}h/week
                              </p>
                              {activity.description && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  {activity.description.length > 100
                                    ? `${activity.description.substring(0, 100)}...`
                                    : activity.description}
                                </p>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="lg:col-span-1">
                {resumeAnalytics && (
                  <Card className="sticky top-4">
                    <CardHeader>
                      <CardTitle>Resume Analytics</CardTitle>
                      <CardDescription>
                        Analysis of your selected activities
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Leadership Activities</span>
                          <span>{Math.round(resumeAnalytics.leadership_percentage)}%</span>
                        </div>
                        <Progress value={resumeAnalytics.leadership_percentage} />
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Activity Diversity</span>
                          <span>{Math.round(resumeAnalytics.activity_diversity_score)}%</span>
                        </div>
                        <Progress value={resumeAnalytics.activity_diversity_score} />
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Impact Score</span>
                          <span>{Math.round(resumeAnalytics.impact_score)}%</span>
                        </div>
                        <Progress value={resumeAnalytics.impact_score} />
                      </div>

                      <div className="pt-2 border-t">
                        <p className="text-sm font-medium mb-2">Quick Stats</p>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span>Total Activities:</span>
                            <span>{formData.selected_activities.length}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Hours/Week:</span>
                            <span>{resumeAnalytics.total_hours_per_week}</span>
                          </div>
                        </div>
                      </div>

                      {resumeAnalytics.recommendations.length > 0 && (
                        <div className="pt-2 border-t">
                          <p className="text-sm font-medium mb-2">Recommendations</p>
                          <div className="space-y-1">
                            {resumeAnalytics.recommendations.slice(0, 3).map((rec, index) => (
                              <p key={index} className="text-xs text-muted-foreground">
                                • {rec}
                              </p>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep(2)}
              >
                <Icons.chevronLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button
                type="button"
                onClick={() => setCurrentStep(4)}
                disabled={formData.selected_activities.length === 0}
              >
                Next: Review
                <Icons.chevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </TabsContent>

          {/* Step 4: Review and Submit */}
          <TabsContent value="4" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Review Your Resume</CardTitle>
                <CardDescription>
                  Review all details before creating your resume
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="font-medium mb-2">Resume Details</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Title:</span>
                        <span>{formData.title}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Focus Area:</span>
                        <span>{focusAreaOptions.find(opt => opt.value === formData.focus_area)?.label}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Template:</span>
                        <span>{selectedTemplate?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Default Resume:</span>
                        <span>{formData.is_default ? 'Yes' : 'No'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Selected Activities</h4>
                    <div className="space-y-1 text-sm">
                      <p>{formData.selected_activities.length} activities selected</p>
                      {selectedActivitiesData.slice(0, 3).map((activity) => (
                        <p key={activity.id} className="text-muted-foreground">
                          • {activity.organization_name}
                        </p>
                      ))}
                      {selectedActivitiesData.length > 3 && (
                        <p className="text-muted-foreground">
                          ... and {selectedActivitiesData.length - 3} more
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {formData.description && (
                  <div>
                    <h4 className="font-medium mb-2">Description</h4>
                    <p className="text-sm text-muted-foreground">{formData.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep(3)}
              >
                <Icons.chevronLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    Creating Resume...
                  </>
                ) : (
                  <>
                    <Icons.plus className="mr-2 h-4 w-4" />
                    Create Resume
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </form>
    </div>
  );
}
