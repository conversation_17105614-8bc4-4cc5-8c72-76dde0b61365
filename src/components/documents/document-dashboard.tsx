'use client';

import { useState, useEffect, useMemo } from 'react';
import { useCurrentUser } from '@/hooks/use-current-user';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import {
  FileText,
  Plus,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  BarChart3,
  Users,
  Target
} from 'lucide-react';
import { DocumentList } from './document-list';
import { CreateDocumentForm } from './create-document-form';
import { useApiGet } from '@/lib/api-client';

interface DocumentStats {
  total_documents: number;
  documents_with_google_docs: number;
  google_docs_integration_percentage: number;
  documents_by_type: Record<string, number>;
  completion_stats: {
    draft: number;
    in_review: number;
    completed: number;
    no_status: number;
  };
  word_count_stats: {
    total_words: number;
    average_words: number;
    documents_with_word_count: number;
  };
  recent_activity: Array<{
    id: string;
    title: string;
    doc_type: string;
    created_at: string;
    student_name?: string;
    has_google_doc: boolean;
  }>;
  summary: {
    most_common_type: string;
    completion_rate: number;
    recent_activity_count: number;
  };
}

interface DocumentDashboardProps {
  studentId?: string;
}

export function DocumentDashboard({ studentId }: DocumentDashboardProps) {
  const { user, isStudent, isConsultant, isAdmin } = useCurrentUser();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  // Use the new API client hook
  const params = useMemo(() => {
    const p: Record<string, string> = {};
    if (studentId) p.student_id = studentId;
    return p;
  }, [studentId]);

  const {
    data: stats,
    loading,
    error,
    fetch: fetchStats
  } = useApiGet<DocumentStats>('/api/documents/stats', params);

  useEffect(() => {
    fetchStats();
  }, [studentId, refreshKey, fetchStats]);

  // Default stats for error cases
  const defaultStats: DocumentStats = {
    total_documents: 0,
    documents_with_google_docs: 0,
    google_docs_integration_percentage: 0,
    documents_by_type: {},
    completion_stats: {
      draft: 0,
      in_review: 0,
      completed: 0,
      no_status: 0
    },
    word_count_stats: {
      total_words: 0,
      average_words: 0,
      documents_with_word_count: 0
    },
    recent_activity: [],
    summary: {
      most_common_type: 'none',
      completion_rate: 0,
      recent_activity_count: 0
    }
  };

  const handleDocumentCreated = () => {
    setShowCreateForm(false);
    setRefreshKey((prev) => prev + 1);
  };

  const getDocTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      personal_statement: 'Personal Statement',
      essay: 'Essay',
      activity_resume: 'Activity Resume',
      transcript: 'Transcript',
      other: 'Other'
    };
    return labels[type] || type;
  };

  // Use stats data or default stats if there's an error
  const displayStats = stats && stats.summary ? stats : defaultStats;

  if (loading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className='pb-2'>
                <Skeleton className='h-4 w-20' />
              </CardHeader>
              <CardContent>
                <Skeleton className='h-8 w-16' />
              </CardContent>
            </Card>
          ))}
        </div>
        <Skeleton className='h-[400px] w-full' />
      </div>
    );
  }

  if (showCreateForm) {
    return (
      <CreateDocumentForm
        studentId={studentId || (isStudent ? user?.profile?.id : undefined)}
        onSuccess={handleDocumentCreated}
        onCancel={() => setShowCreateForm(false)}
      />
    );
  }

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      {/* Header */}
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Documents</h2>
          <p className='text-muted-foreground'>
            {isStudent && 'Manage your college application documents'}
            {isConsultant && 'Review and collaborate on student documents'}
            {isAdmin && 'Oversee all document management and collaboration'}
          </p>
        </div>
        {(isStudent || isAdmin) && (
          <Button onClick={() => setShowCreateForm(true)}>
            <Plus className='mr-2 h-4 w-4' />
            New Document
          </Button>
        )}
      </div>

      {/* Overview Stats */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Documents
            </CardTitle>
            <FileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {displayStats.total_documents}
            </div>
            <p className='text-muted-foreground text-xs'>
              {displayStats.documents_with_google_docs} with Google Docs
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Completion Rate
            </CardTitle>
            <CheckCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {displayStats.summary.completion_rate}%
            </div>
            <Progress
              value={displayStats.summary.completion_rate}
              className='mt-2'
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Google Docs Integration
            </CardTitle>
            <TrendingUp className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {displayStats.google_docs_integration_percentage}%
            </div>
            <p className='text-muted-foreground text-xs'>
              {displayStats.documents_with_google_docs} of{' '}
              {displayStats.total_documents} documents
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Recent Activity
            </CardTitle>
            <Clock className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {displayStats.summary.recent_activity_count}
            </div>
            <p className='text-muted-foreground text-xs'>
              Documents created this month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue='documents' className='space-y-4'>
        <TabsList>
          <TabsTrigger value='documents'>All Documents</TabsTrigger>
          <TabsTrigger value='analytics'>Analytics</TabsTrigger>
          {displayStats.recent_activity.length > 0 && (
            <TabsTrigger value='activity'>Recent Activity</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value='documents' className='space-y-4'>
          <DocumentList
            studentId={studentId}
            showCreateButton={false}
            onCreateDocument={() => setShowCreateForm(true)}
          />
        </TabsContent>

        <TabsContent value='analytics' className='space-y-4'>
          {stats && (
            <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
              {/* Document Types */}
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <BarChart3 className='h-5 w-5' />
                    Documents by Type
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='space-y-3'>
                    {Object.entries(displayStats.documents_by_type).map(
                      ([type, count]) => (
                        <div
                          key={type}
                          className='flex items-center justify-between'
                        >
                          <span className='text-sm'>
                            {getDocTypeLabel(type)}
                          </span>
                          <div className='flex items-center gap-2'>
                            <div className='bg-muted h-2 w-20 rounded-full'>
                              <div
                                className='bg-primary h-2 rounded-full'
                                style={{
                                  width: `${displayStats.total_documents > 0 ? (count / displayStats.total_documents) * 100 : 0}%`
                                }}
                              />
                            </div>
                            <span className='w-8 text-right text-sm font-medium'>
                              {count}
                            </span>
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Completion Status */}
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Target className='h-5 w-5' />
                    Completion Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='space-y-3'>
                    <div className='flex items-center justify-between'>
                      <span className='flex items-center gap-2 text-sm'>
                        <CheckCircle className='h-4 w-4 text-green-500' />
                        Completed
                      </span>
                      <Badge variant='outline'>
                        {displayStats.completion_stats.completed}
                      </Badge>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='flex items-center gap-2 text-sm'>
                        <Clock className='h-4 w-4 text-yellow-500' />
                        In Review
                      </span>
                      <Badge variant='outline'>
                        {displayStats.completion_stats.in_review}
                      </Badge>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='flex items-center gap-2 text-sm'>
                        <FileText className='h-4 w-4 text-blue-500' />
                        Draft
                      </span>
                      <Badge variant='outline'>
                        {displayStats.completion_stats.draft}
                      </Badge>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='flex items-center gap-2 text-sm'>
                        <AlertTriangle className='h-4 w-4 text-gray-500' />
                        No Status
                      </span>
                      <Badge variant='outline'>
                        {displayStats.completion_stats.no_status}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Word Count Stats */}
              {stats?.word_count_stats?.documents_with_word_count > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Writing Statistics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-3'>
                      <div className='flex justify-between'>
                        <span className='text-sm'>Total Words</span>
                        <span className='font-medium'>
                          {stats?.word_count_stats?.total_words?.toLocaleString() ||
                            0}
                        </span>
                      </div>
                      <div className='flex justify-between'>
                        <span className='text-sm'>Average per Document</span>
                        <span className='font-medium'>
                          {stats?.word_count_stats?.average_words || 0}
                        </span>
                      </div>
                      <div className='flex justify-between'>
                        <span className='text-sm'>Documents Tracked</span>
                        <span className='font-medium'>
                          {stats?.word_count_stats?.documents_with_word_count ||
                            0}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value='activity' className='space-y-4'>
          {stats && stats?.recent_activity?.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Clock className='h-5 w-5' />
                  Recent Activity
                </CardTitle>
                <CardDescription>
                  Latest document creation and updates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  {stats?.recent_activity?.map((activity) => (
                    <div
                      key={activity.id}
                      className='flex items-center justify-between rounded-lg border p-3'
                    >
                      <div className='flex items-center gap-3'>
                        <div className='bg-primary/10 rounded-full p-2'>
                          <FileText className='text-primary h-4 w-4' />
                        </div>
                        <div>
                          <p className='font-medium'>{activity.title}</p>
                          <p className='text-muted-foreground text-sm'>
                            {getDocTypeLabel(activity.doc_type)}
                            {activity.student_name &&
                              !isStudent &&
                              ` • ${activity.student_name}`}
                          </p>
                        </div>
                      </div>
                      <div className='text-right'>
                        <p className='text-muted-foreground text-sm'>
                          {new Date(activity.created_at).toLocaleDateString()}
                        </p>
                        {activity.has_google_doc && (
                          <Badge variant='outline' className='mt-1'>
                            Google Docs
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
