'use client';

import { useEffect } from 'react';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useApiGet } from '@/lib/api-client';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  FileText,
  Calendar,
  User,
  Clock,
  Edit,
  Trash2,
  ExternalLink,
  Settings,
  AlertCircle
} from 'lucide-react';
import { GoogleDocsViewer } from './google-docs-viewer';
import Link from 'next/link';

interface DocumentDetailProps {
  documentId: string;
  onBack?: () => void;
}

interface DocumentData {
  id: string;
  student_id: string;
  google_doc_id: string | null;
  doc_type: string;
  metadata: {
    title: string;
    description?: string;
    school_id?: string;
    word_limit?: number;
    deadline?: string;
    status?: 'draft' | 'in_review' | 'completed';
    google_doc_url?: string;
    folder_id?: string;
    word_count?: number;
    character_count?: number;
    last_synced?: string;
  };
  created_at: string;
  updated_at: string;
  student_name?: string;
  student_email?: string;
  consultant_emails?: string[];
}

export function DocumentDetail({ documentId, onBack }: DocumentDetailProps) {
  const { user, isStudent, isConsultant, isAdmin } = useCurrentUser();

  // Use the new API client
  const {
    data: document,
    loading,
    error,
    fetch: fetchDocument
  } = useApiGet<DocumentData>(`/api/documents/${documentId}`);

  useEffect(() => {
    if (documentId) {
      fetchDocument();
    }
  }, [documentId, fetchDocument]);

  const getDocTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      personal_statement: 'Personal Statement',
      essay: 'Essay',
      activity_resume: 'Activity Resume',
      transcript: 'Transcript',
      other: 'Other'
    };
    return labels[type] || type;
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const canEdit =
    isAdmin || (isStudent && document?.student_id === user?.profile?.id);
  const canDelete = canEdit;

  if (loading) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center gap-4'>
          <Skeleton className='h-10 w-10' />
          <Skeleton className='h-8 w-64' />
        </div>
        <div className='grid grid-cols-1 gap-6 lg:grid-cols-3'>
          <div className='lg:col-span-2'>
            <Skeleton className='h-[600px] w-full' />
          </div>
          <div className='space-y-4'>
            <Skeleton className='h-[200px] w-full' />
            <Skeleton className='h-[150px] w-full' />
          </div>
        </div>
      </div>
    );
  }

  if (error || !document) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center gap-4'>
          <Button variant='ghost' size='sm' onClick={onBack}>
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <h1 className='text-2xl font-bold'>Document Not Found</h1>
        </div>
        <Alert>
          <AlertCircle className='h-4 w-4' />
          <AlertDescription>
            {error || 'The requested document could not be found.'}
          </AlertDescription>
        </Alert>
        <Button onClick={fetchDocument} variant='outline'>
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <Button variant='ghost' size='sm' onClick={onBack}>
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <div>
            <h1 className='text-2xl font-bold'>{document.metadata.title}</h1>
            <p className='text-muted-foreground'>
              {getDocTypeLabel(document.doc_type)}
              {document.student_name &&
                !isStudent &&
                ` • ${document.student_name}`}
            </p>
          </div>
        </div>
        <div className='flex items-center gap-2'>
          {document.metadata.status && (
            <Badge className={getStatusColor(document.metadata.status)}>
              {document.metadata.status}
            </Badge>
          )}
          {canEdit && (
            <Button variant='outline' size='sm'>
              <Edit className='mr-2 h-4 w-4' />
              Edit
            </Button>
          )}
          {canDelete && (
            <Button variant='outline' size='sm'>
              <Trash2 className='mr-2 h-4 w-4' />
              Delete
            </Button>
          )}
        </div>
      </div>

      <div className='grid grid-cols-1 gap-6 lg:grid-cols-3'>
        {/* Main Content */}
        <div className='lg:col-span-2'>
          <GoogleDocsViewer
            documentId={document.id}
            googleDocId={document.google_doc_id}
            title={document.metadata.title}
            canEdit={canEdit}
            showCollaborators={true}
            onCreateGoogleDoc={fetchDocument}
          />
        </div>

        {/* Sidebar */}
        <div className='space-y-4'>
          {/* Document Info */}
          <Card>
            <CardHeader>
              <CardTitle className='text-base'>Document Information</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-3'>
                <div className='flex items-center justify-between'>
                  <span className='text-muted-foreground text-sm'>Type</span>
                  <Badge variant='outline'>
                    {getDocTypeLabel(document.doc_type)}
                  </Badge>
                </div>

                {document.metadata.word_limit && (
                  <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground text-sm'>
                      Word Limit
                    </span>
                    <span className='text-sm font-medium'>
                      {document.metadata.word_limit}
                    </span>
                  </div>
                )}

                {document.metadata.deadline && (
                  <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground flex items-center gap-1 text-sm'>
                      <Calendar className='h-3 w-3' />
                      Deadline
                    </span>
                    <span className='text-sm font-medium'>
                      {new Date(
                        document.metadata.deadline
                      ).toLocaleDateString()}
                    </span>
                  </div>
                )}

                <div className='flex items-center justify-between'>
                  <span className='text-muted-foreground text-sm'>Created</span>
                  <span className='text-sm font-medium'>
                    {new Date(document.created_at).toLocaleDateString()}
                  </span>
                </div>

                <div className='flex items-center justify-between'>
                  <span className='text-muted-foreground text-sm'>
                    Last Updated
                  </span>
                  <span className='text-sm font-medium'>
                    {new Date(document.updated_at).toLocaleDateString()}
                  </span>
                </div>
              </div>

              {document.metadata.description && (
                <>
                  <Separator />
                  <div>
                    <h4 className='mb-2 text-sm font-medium'>Description</h4>
                    <p className='text-muted-foreground text-sm'>
                      {document.metadata.description}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Writing Progress */}
          {(document.metadata.word_count || document.metadata.word_limit) && (
            <Card>
              <CardHeader>
                <CardTitle className='text-base'>Writing Progress</CardTitle>
              </CardHeader>
              <CardContent className='space-y-3'>
                {document.metadata.word_count && (
                  <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground text-sm'>
                      Current Words
                    </span>
                    <span className='text-sm font-medium'>
                      {document.metadata.word_count}
                    </span>
                  </div>
                )}

                {document.metadata.character_count && (
                  <div className='flex items-center justify-between'>
                    <span className='text-muted-foreground text-sm'>
                      Characters
                    </span>
                    <span className='text-sm font-medium'>
                      {document.metadata.character_count}
                    </span>
                  </div>
                )}

                {document.metadata.word_limit &&
                  document.metadata.word_count && (
                    <div className='space-y-2'>
                      <div className='flex items-center justify-between'>
                        <span className='text-muted-foreground text-sm'>
                          Progress
                        </span>
                        <span className='text-sm font-medium'>
                          {Math.round(
                            (document.metadata.word_count /
                              document.metadata.word_limit) *
                              100
                          )}
                          %
                        </span>
                      </div>
                      <div className='bg-muted h-2 w-full rounded-full'>
                        <div
                          className='bg-primary h-2 rounded-full transition-all'
                          style={{
                            width: `${Math.min((document.metadata.word_count / document.metadata.word_limit) * 100, 100)}%`
                          }}
                        />
                      </div>
                    </div>
                  )}

                {document.metadata.last_synced && (
                  <div className='text-muted-foreground flex items-center gap-1 text-xs'>
                    <Clock className='h-3 w-3' />
                    Last synced:{' '}
                    {new Date(document.metadata.last_synced).toLocaleString()}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Collaboration */}
          {(document.consultant_emails?.length || 0) > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2 text-base'>
                  <User className='h-4 w-4' />
                  Collaboration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  <div>
                    <h4 className='mb-2 text-sm font-medium'>Student</h4>
                    <div className='flex items-center gap-2'>
                      <div className='flex h-6 w-6 items-center justify-center rounded-full bg-blue-100'>
                        <User className='h-3 w-3 text-blue-600' />
                      </div>
                      <span className='text-sm'>
                        {document.student_name || 'Student'}
                      </span>
                    </div>
                  </div>

                  {document.consultant_emails &&
                    document.consultant_emails.length > 0 && (
                      <div>
                        <h4 className='mb-2 text-sm font-medium'>
                          Consultants
                        </h4>
                        <div className='space-y-2'>
                          {document.consultant_emails.map((email, index) => (
                            <div
                              key={index}
                              className='flex items-center gap-2'
                            >
                              <div className='flex h-6 w-6 items-center justify-center rounded-full bg-green-100'>
                                <User className='h-3 w-3 text-green-600' />
                              </div>
                              <span className='text-sm'>{email}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className='text-base'>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className='space-y-2'>
              {document.google_doc_id && document.metadata.google_doc_url && (
                <Button
                  variant='outline'
                  size='sm'
                  className='w-full justify-start'
                  asChild
                >
                  <a
                    href={document.metadata.google_doc_url}
                    target='_blank'
                    rel='noopener noreferrer'
                  >
                    <ExternalLink className='mr-2 h-4 w-4' />
                    Open in Google Docs
                  </a>
                </Button>
              )}

              <Button
                variant='outline'
                size='sm'
                className='w-full justify-start'
                asChild
              >
                <Link href={`/dashboard/documents`}>
                  <FileText className='mr-2 h-4 w-4' />
                  Back to Documents
                </Link>
              </Button>

              {canEdit && (
                <Button
                  variant='outline'
                  size='sm'
                  className='w-full justify-start'
                >
                  <Settings className='mr-2 h-4 w-4' />
                  Document Settings
                </Button>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
