'use client';

import { useState, useEffect } from 'react';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useApiGet } from '@/lib/api-client';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  FileText,
  ExternalLink,
  Calendar,
  User,
  School,
  Plus,
  Filter,
  Search
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import Link from 'next/link';

interface Document {
  id: string;
  student_id: string;
  google_doc_id: string | null;
  doc_type: string;
  metadata: {
    title: string;
    school_id?: string;
    word_limit?: number;
    deadline?: string;
    status?: 'draft' | 'in_review' | 'completed';
    google_doc_url?: string;
  };
  created_at: string;
  updated_at: string;
  student_name?: string;
  student_email?: string;
}

interface DocumentListProps {
  studentId?: string;
  docType?: string;
  showCreateButton?: boolean;
  onCreateDocument?: () => void;
}

export function DocumentList({
  studentId,
  docType,
  showCreateButton = true,
  onCreateDocument
}: DocumentListProps) {
  const { user, loading: userLoading } = useCurrentUser();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // Build API parameters
  const apiParams: Record<string, string> = {};
  if (studentId) apiParams.student_id = studentId;
  if (docType) apiParams.doc_type = docType;

  // Use the new API client
  const {
    data: documents = [],
    loading,
    error,
    fetch: fetchDocuments
  } = useApiGet<Document[]>('/api/documents', apiParams);

  useEffect(() => {
    if (!userLoading && user) {
      fetchDocuments();
    }
  }, [user, userLoading, studentId, docType, fetchDocuments]);

  const getDocTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      personal_statement: 'Personal Statement',
      essay: 'Essay',
      activity_resume: 'Activity Resume',
      transcript: 'Transcript',
      other: 'Other'
    };
    return labels[type] || type;
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredDocuments = Array.isArray(documents)
    ? documents.filter((doc) => {
        const matchesSearch =
          doc?.metadata?.title
            ?.toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          doc?.student_name?.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesType =
          filterType === 'all' || doc?.doc_type === filterType;
        const matchesStatus =
          filterStatus === 'all' || doc?.metadata?.status === filterStatus;

        return matchesSearch && matchesType && matchesStatus;
      })
    : [];

  if (userLoading || loading) {
    return (
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <Skeleton className='h-8 w-48' />
          <Skeleton className='h-10 w-32' />
        </div>
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className='h-6 w-3/4' />
              <Skeleton className='h-4 w-1/2' />
            </CardHeader>
            <CardContent>
              <Skeleton className='h-4 w-full' />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className='pt-6'>
          <div className='text-center text-red-600'>
            <p>Error loading documents: {error}</p>
            <Button onClick={fetchDocuments} variant='outline' className='mt-2'>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold'>Documents</h2>
          <p className='text-muted-foreground'>
            {filteredDocuments.length} of {documents?.length || 0} documents
          </p>
        </div>
        {showCreateButton && (
          <Button onClick={onCreateDocument}>
            <Plus className='mr-2 h-4 w-4' />
            New Document
          </Button>
        )}
      </div>

      {/* Filters */}
      <div className='flex flex-wrap gap-4'>
        <div className='min-w-[200px] flex-1'>
          <div className='relative'>
            <Search className='text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform' />
            <Input
              placeholder='Search documents...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>
        </div>
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className='w-[180px]'>
            <SelectValue placeholder='Document Type' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Types</SelectItem>
            <SelectItem value='personal_statement'>
              Personal Statement
            </SelectItem>
            <SelectItem value='essay'>Essay</SelectItem>
            <SelectItem value='activity_resume'>Activity Resume</SelectItem>
            <SelectItem value='transcript'>Transcript</SelectItem>
            <SelectItem value='other'>Other</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className='w-[150px]'>
            <SelectValue placeholder='Status' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Status</SelectItem>
            <SelectItem value='draft'>Draft</SelectItem>
            <SelectItem value='in_review'>In Review</SelectItem>
            <SelectItem value='completed'>Completed</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Document List */}
      {filteredDocuments.length === 0 ? (
        <Card>
          <CardContent className='pt-6'>
            <div className='text-muted-foreground text-center'>
              <FileText className='mx-auto mb-4 h-12 w-12 opacity-50' />
              <p className='text-lg font-medium'>No documents found</p>
              <p>
                {(documents?.length || 0) === 0
                  ? 'Get started by creating your first document'
                  : 'Try adjusting your search or filters'}
              </p>
              {showCreateButton && (documents?.length || 0) === 0 && (
                <Button onClick={onCreateDocument} className='mt-4'>
                  <Plus className='mr-2 h-4 w-4' />
                  Create Document
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className='grid gap-4'>
          {filteredDocuments.map((document) => (
            <Card
              key={document.id}
              className='transition-shadow hover:shadow-md'
            >
              <CardHeader>
                <div className='flex items-start justify-between'>
                  <div className='space-y-1'>
                    <CardTitle className='text-lg'>
                      {document.metadata.title}
                    </CardTitle>
                    <CardDescription className='flex items-center gap-4'>
                      <span className='flex items-center gap-1'>
                        <FileText className='h-4 w-4' />
                        {getDocTypeLabel(document.doc_type)}
                      </span>
                      {document.student_name && user?.role !== 'student' && (
                        <span className='flex items-center gap-1'>
                          <User className='h-4 w-4' />
                          {document.student_name}
                        </span>
                      )}
                      <span className='flex items-center gap-1'>
                        <Calendar className='h-4 w-4' />
                        {new Date(document.created_at).toLocaleDateString()}
                      </span>
                    </CardDescription>
                  </div>
                  <div className='flex items-center gap-2'>
                    {document.metadata.status && (
                      <Badge
                        className={getStatusColor(document.metadata.status)}
                      >
                        {document.metadata.status}
                      </Badge>
                    )}
                    {document.google_doc_id && (
                      <Badge variant='outline'>Google Docs</Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className='flex items-center justify-between'>
                  <div className='text-muted-foreground space-y-1 text-sm'>
                    {document.metadata.word_limit && (
                      <p>Word limit: {document.metadata.word_limit}</p>
                    )}
                    {document.metadata.deadline && (
                      <p>
                        Deadline:{' '}
                        {new Date(
                          document.metadata.deadline
                        ).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                  <div className='flex gap-2'>
                    <Button variant='outline' size='sm' asChild>
                      <Link href={`/dashboard/documents/${document.id}`}>
                        View Details
                      </Link>
                    </Button>
                    {document.google_doc_id &&
                      document.metadata.google_doc_url && (
                        <Button variant='outline' size='sm' asChild>
                          <a
                            href={document.metadata.google_doc_url}
                            target='_blank'
                            rel='noopener noreferrer'
                          >
                            <ExternalLink className='mr-1 h-4 w-4' />
                            Open in Google Docs
                          </a>
                        </Button>
                      )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
