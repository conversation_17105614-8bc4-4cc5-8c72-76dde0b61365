'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ExternalLink,
  RefreshCw,
  FileText,
  Users,
  Clock,
  AlertCircle,
  CheckCircle,
  Eye
} from 'lucide-react';

interface GoogleDocsViewerProps {
  documentId: string;
  googleDocId?: string | null;
  title: string;
  canEdit?: boolean;
  showCollaborators?: boolean;
  onCreateGoogleDoc?: () => void;
}

interface DocumentContent {
  google_doc_id: string;
  content: string;
  metadata: {
    title?: string;
    google_doc_url?: string;
    word_count?: number;
    character_count?: number;
    last_synced?: string;
  };
}

export function GoogleDocsViewer({
  documentId,
  googleDocId,
  title,
  canEdit = false,
  showCollaborators = true,
  onCreateGoogleDoc
}: GoogleDocsViewerProps) {
  const [content, setContent] = useState<DocumentContent | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [syncing, setSyncing] = useState(false);

  useEffect(() => {
    if (googleDocId) {
      fetchContent();
    }
  }, [googleDocId]);

  const fetchContent = async () => {
    if (!googleDocId) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/documents/${documentId}/google-doc`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch document content');
      }

      setContent(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch content');
    } finally {
      setLoading(false);
    }
  };

  const syncContent = async () => {
    if (!googleDocId) return;

    try {
      setSyncing(true);

      const response = await fetch('/api/documents/operations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          operation: 'sync_content',
          document_ids: [documentId]
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to sync content');
      }

      // Refresh content after sync
      await fetchContent();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sync content');
    } finally {
      setSyncing(false);
    }
  };

  const createGoogleDoc = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/documents/${documentId}/google-doc`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: title,
          content: ''
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create Google Doc');
      }

      onCreateGoogleDoc?.();
      await fetchContent();
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to create Google Doc'
      );
    } finally {
      setLoading(false);
    }
  };

  if (!googleDocId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            Google Docs Integration
          </CardTitle>
          <CardDescription>
            Create a Google Doc for collaborative editing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='py-8 text-center'>
            <div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100'>
              <FileText className='h-6 w-6 text-blue-600' />
            </div>
            <h3 className='mb-2 text-lg font-medium'>
              No Google Doc Connected
            </h3>
            <p className='text-muted-foreground mb-4'>
              Create a Google Doc to enable real-time collaboration with your
              consultant
            </p>
            <Button onClick={createGoogleDoc} disabled={loading}>
              {loading ? 'Creating...' : 'Create Google Doc'}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (loading && !content) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className='h-6 w-3/4' />
          <Skeleton className='h-4 w-1/2' />
        </CardHeader>
        <CardContent>
          <Skeleton className='h-[400px] w-full' />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <AlertCircle className='h-5 w-5 text-red-500' />
            Error Loading Document
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className='h-4 w-4' />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <div className='mt-4 flex gap-2'>
            <Button onClick={fetchContent} variant='outline'>
              <RefreshCw className='mr-2 h-4 w-4' />
              Try Again
            </Button>
            {!googleDocId && (
              <Button onClick={createGoogleDoc}>Create Google Doc</Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  const googleDocUrl =
    content?.metadata.google_doc_url ||
    `https://docs.google.com/document/d/${googleDocId}/edit`;

  return (
    <div className='space-y-4'>
      {/* Document Header */}
      <Card>
        <CardHeader>
          <div className='flex items-start justify-between'>
            <div>
              <CardTitle className='flex items-center gap-2'>
                <FileText className='h-5 w-5' />
                {title}
              </CardTitle>
              <CardDescription className='mt-2 flex items-center gap-4'>
                <Badge variant='outline' className='flex items-center gap-1'>
                  <CheckCircle className='h-3 w-3' />
                  Google Docs Connected
                </Badge>
                {content?.metadata.word_count && (
                  <span className='text-sm'>
                    {content.metadata.word_count} words
                  </span>
                )}
                {content?.metadata.last_synced && (
                  <span className='flex items-center gap-1 text-sm'>
                    <Clock className='h-3 w-3' />
                    Synced{' '}
                    {new Date(content.metadata.last_synced).toLocaleString()}
                  </span>
                )}
              </CardDescription>
            </div>
            <div className='flex gap-2'>
              <Button
                variant='outline'
                size='sm'
                onClick={syncContent}
                disabled={syncing}
              >
                <RefreshCw
                  className={`mr-2 h-4 w-4 ${syncing ? 'animate-spin' : ''}`}
                />
                {syncing ? 'Syncing...' : 'Sync'}
              </Button>
              <Button size='sm' asChild>
                <a
                  href={googleDocUrl}
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  <ExternalLink className='mr-2 h-4 w-4' />
                  Open in Google Docs
                </a>
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Embedded Google Docs */}
      <Card>
        <CardContent className='p-0'>
          <div className='relative'>
            <iframe
              src={`${googleDocUrl}?embedded=true`}
              width='100%'
              height='600'
              className='rounded-lg border-0'
              title={`Google Doc: ${title}`}
            />
            {!canEdit && (
              <div className='absolute top-2 right-2'>
                <Badge variant='secondary' className='flex items-center gap-1'>
                  <Eye className='h-3 w-3' />
                  View Only
                </Badge>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Collaboration Info */}
      {showCollaborators && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2 text-base'>
              <Users className='h-4 w-4' />
              Collaboration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              <div className='flex items-center justify-between'>
                <span className='text-sm'>Document Access</span>
                <Badge variant={canEdit ? 'default' : 'secondary'}>
                  {canEdit ? 'Can Edit' : 'View Only'}
                </Badge>
              </div>
              <div className='text-muted-foreground text-sm'>
                <p>
                  This document is shared with your consultant for review and
                  feedback.
                  {canEdit
                    ? ' You can edit the document directly in Google Docs.'
                    : ' You have view-only access to this document.'}
                </p>
              </div>
              <div className='pt-2'>
                <Button variant='outline' size='sm' asChild>
                  <a
                    href={`${googleDocUrl}?usp=sharing`}
                    target='_blank'
                    rel='noopener noreferrer'
                  >
                    Manage Sharing
                  </a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Content Preview (if available) */}
      {content?.content && (
        <Card>
          <CardHeader>
            <CardTitle className='text-base'>Content Preview</CardTitle>
            <CardDescription>
              Latest content from Google Docs (may not reflect real-time
              changes)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='prose prose-sm max-w-none'>
              <div className='bg-muted/30 max-h-60 overflow-y-auto rounded-lg p-4 text-sm whitespace-pre-wrap'>
                {content.content.slice(0, 1000)}
                {content.content.length > 1000 && '...'}
              </div>
            </div>
            {content.content.length > 1000 && (
              <p className='text-muted-foreground mt-2 text-xs'>
                Showing first 1000 characters. Open in Google Docs to view full
                content.
              </p>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
