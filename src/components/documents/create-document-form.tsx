'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useCurrentUser } from '@/hooks/use-current-user';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { FileText, Sparkles, Edit, File, Lightbulb, AlertCircle } from 'lucide-react';
import { DocumentRecommendationService, type CreationMethod, type DocumentType } from '@/lib/document-recommendations';

const createDocumentSchema = z.object({
  title: z
    .string()
    .min(1, 'Title is required')
    .max(200, 'Title must be less than 200 characters'),
  doc_type: z.enum([
    'essay',
    'personal_statement',
    'transcript',
    'activity_resume',
    'other'
  ]),
  creation_method: z.enum(['free_form', 'template_based', 'blank_document']),
  description: z.string().optional(),
  word_limit: z.number().min(1).max(10000).optional(),
  deadline: z.string().optional(),
  school_id: z.string().optional(),
  template_id: z.string().optional(),
  initial_content: z.string().optional()
});

type CreateDocumentForm = z.infer<typeof createDocumentSchema>;

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  doc_type: string;
  usage_count: number;
  difficulty_level: string;
  match_score: number;
  reasons: string[];
}

interface CreateDocumentFormProps {
  studentId?: string;
  onSuccess?: (document: any) => void;
  onCancel?: () => void;
  defaultDocType?: string;
}

export function CreateDocumentForm({
  studentId,
  onSuccess,
  onCancel,
  defaultDocType
}: CreateDocumentFormProps) {
  const { user, loading: userLoading } = useCurrentUser();
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loadingTemplates, setLoadingTemplates] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(
    null
  );
  const [currentRecommendations, setCurrentRecommendations] = useState(
    DocumentRecommendationService.getRecommendations('essay')
  );

  // Helper function to get display label for document type
  const getDocTypeLabel = (docType: string) => {
    const labels: Record<string, string> = {
      personal_statement: 'Personal Statement',
      essay: 'Essay',
      activity_resume: 'Activity Resume',
      transcript: 'Transcript',
      other: 'Other Document'
    };
    return labels[docType] || docType;
  };

  const form = useForm<CreateDocumentForm>({
    resolver: zodResolver(createDocumentSchema),
    defaultValues: {
      title: '',
      doc_type: (defaultDocType as any) || 'essay',
      creation_method: 'free_form',
      description: '',
      word_limit: undefined,
      deadline: '',
      school_id: '',
      template_id: '',
      initial_content: ''
    }
  });

  const watchedDocType = form.watch('doc_type');
  const watchedCreationMethod = form.watch('creation_method');

  // Load template recommendations when doc type changes
  useEffect(() => {
    if (watchedDocType && studentId) {
      loadTemplateRecommendations();
    }
  }, [watchedDocType, studentId]);

  // Update recommendations when doc_type changes
  useEffect(() => {
    if (watchedDocType) {
      const recommendations = DocumentRecommendationService.getRecommendations(watchedDocType as DocumentType);
      setCurrentRecommendations(recommendations);

      // Auto-suggest word limit if available and not already set
      if (recommendations.word_limit && !form.getValues('word_limit')) {
        form.setValue('word_limit', recommendations.word_limit);
      }
    }
  }, [watchedDocType, form]);

  const loadTemplateRecommendations = async () => {
    if (!studentId || !watchedDocType) return;

    try {
      setLoadingTemplates(true);
      const response = await fetch(
        `/api/templates/recommendations?student_id=${studentId}&doc_type=${watchedDocType}&limit=5`
      );
      const result = await response.json();

      if (response.ok) {
        setTemplates(result.data.recommendations || []);
      }
    } catch (error) {
      console.error('Error loading template recommendations:', error);
    } finally {
      setLoadingTemplates(false);
    }
  };

  const onSubmit = async (data: CreateDocumentForm) => {
    try {
      setLoading(true);

      // Get the student_id - prioritize passed studentId, then user's profile id
      const effectiveStudentId = studentId || user?.profile?.id;

      if (!effectiveStudentId) {
        throw new Error(
          'Student profile not found. Please ensure your profile is set up correctly.'
        );
      }

      const requestData = {
        student_id: effectiveStudentId,
        doc_type: data.doc_type,
        metadata: {
          title: data.title,
          description: data.description,
          word_limit: data.word_limit,
          deadline: data.deadline,
          school_id: data.school_id,
          status: 'draft' as const,
          creation_method: data.creation_method,
          recommendations: currentRecommendations
        },
        template_id: data.template_id || undefined,
        initial_content: data.initial_content || undefined
      };

      console.log('Creating document with data:', requestData); // Debug log

      const response = await fetch('/api/documents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create document');
      }

      onSuccess?.(result.data);
    } catch (error) {
      console.error('Error creating document:', error);
      // You might want to show a toast notification here
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
    form.setValue('template_id', template.id);

    // Auto-fill some fields based on template
    if (template.name.includes('Personal Statement')) {
      form.setValue('word_limit', 650); // Common personal statement limit
    }
  };

  // Show loading state while user data is being fetched
  if (userLoading) {
    return (
      <div className='space-y-6'>
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <FileText className='h-5 w-5' />
              Create New Document
            </CardTitle>
            <CardDescription>Loading user profile...</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='flex items-center justify-center py-8'>
              <div className='border-primary h-8 w-8 animate-spin rounded-full border-b-2'></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error if user profile is not available
  if (!user || (!studentId && !user.profile?.id)) {
    return (
      <div className='space-y-6'>
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <FileText className='h-5 w-5' />
              Create New Document
            </CardTitle>
            <CardDescription>Profile setup required</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='py-8 text-center'>
              <p className='text-muted-foreground mb-4'>
                Your student profile needs to be set up before you can create
                documents.
              </p>
              <Button onClick={() => window.location.reload()}>
                Refresh Page
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            Create New Document
          </CardTitle>
          <CardDescription>
            Create your document your way - with complete flexibility and helpful guidance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
                <FormField
                  control={form.control}
                  name='title'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Document Title</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='e.g., Harvard Diversity Essay'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='doc_type'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Document Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select document type' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value='personal_statement'>
                            Personal Statement
                          </SelectItem>
                          <SelectItem value='essay'>Essay</SelectItem>
                          <SelectItem value='activity_resume'>
                            Activity Resume
                          </SelectItem>
                          <SelectItem value='transcript'>Transcript</SelectItem>
                          <SelectItem value='other'>Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Creation Method Selection */}
              <div className='space-y-4'>
                <div>
                  <h3 className='text-lg font-medium'>How would you like to create this document?</h3>
                  <p className='text-sm text-muted-foreground'>Choose the approach that works best for you</p>
                </div>

                <FormField
                  control={form.control}
                  name='creation_method'
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className='grid grid-cols-1 gap-3 md:grid-cols-3'>
                          {(['free_form', 'template_based', 'blank_document'] as CreationMethod[]).map((method) => {
                            const methodInfo = DocumentRecommendationService.getCreationMethodInfo(method);
                            const IconComponent = method === 'free_form' ? Edit : method === 'template_based' ? FileText : File;

                            return (
                              <div
                                key={method}
                                className={`cursor-pointer rounded-lg border p-4 transition-all hover:border-primary/50 ${
                                  field.value === method
                                    ? 'border-primary bg-primary/5 ring-1 ring-primary/20'
                                    : 'border-border'
                                }`}
                                onClick={() => field.onChange(method)}
                              >
                                <div className='flex items-start space-x-3'>
                                  <IconComponent className='h-5 w-5 mt-0.5 text-primary' />
                                  <div className='flex-1'>
                                    <h4 className='font-medium text-sm'>{methodInfo.title}</h4>
                                    <p className='text-xs text-muted-foreground mt-1'>{methodInfo.description}</p>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
                <FormField
                  control={form.control}
                  name='word_limit'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Word Limit (Recommendation)</FormLabel>
                      <FormControl>
                        <Input
                          type='number'
                          placeholder={currentRecommendations.word_limit ? `Recommended: ${currentRecommendations.word_limit}` : 'e.g., 650'}
                          {...field}
                          onChange={(e) =>
                            field.onChange(
                              e.target.value
                                ? parseInt(e.target.value)
                                : undefined
                            )
                          }
                        />
                      </FormControl>
                      {currentRecommendations.word_limit_note && (
                        <FormDescription className='flex items-start gap-2'>
                          <Lightbulb className='h-4 w-4 mt-0.5 text-amber-500 flex-shrink-0' />
                          <span className='text-xs'>{currentRecommendations.word_limit_note}</span>
                        </FormDescription>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='deadline'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Deadline (Optional)</FormLabel>
                      <FormControl>
                        <Input type='date' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Brief description of the document purpose...'
                        className='min-h-[80px]'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='initial_content'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Initial Content (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Start writing your document here, or select a template below...'
                        className='min-h-[120px]'
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      You can add content here or use a template to get started
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Document Recommendations */}
              <Card className='bg-blue-50/50 border-blue-200'>
                <CardHeader className='pb-3'>
                  <CardTitle className='flex items-center gap-2 text-base'>
                    <Lightbulb className='h-4 w-4 text-blue-600' />
                    Writing Guidance for {getDocTypeLabel(watchedDocType)}
                  </CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  {currentRecommendations.guidelines.length > 0 && (
                    <div>
                      <h4 className='font-medium text-sm mb-2'>Key Guidelines:</h4>
                      <ul className='space-y-1'>
                        {currentRecommendations.guidelines.slice(0, 3).map((guideline, index) => (
                          <li key={index} className='text-sm text-muted-foreground flex items-start gap-2'>
                            <span className='text-blue-600 mt-1'>•</span>
                            <span>{guideline}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {currentRecommendations.tips.length > 0 && (
                    <div>
                      <h4 className='font-medium text-sm mb-2'>Pro Tips:</h4>
                      <ul className='space-y-1'>
                        {currentRecommendations.tips.slice(0, 2).map((tip, index) => (
                          <li key={index} className='text-sm text-muted-foreground flex items-start gap-2'>
                            <span className='text-green-600 mt-1'>✓</span>
                            <span>{tip}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {currentRecommendations.common_mistakes.length > 0 && (
                    <div>
                      <h4 className='font-medium text-sm mb-2'>Avoid These Mistakes:</h4>
                      <ul className='space-y-1'>
                        {currentRecommendations.common_mistakes.slice(0, 2).map((mistake, index) => (
                          <li key={index} className='text-sm text-muted-foreground flex items-start gap-2'>
                            <AlertCircle className='h-3 w-3 text-amber-500 mt-1 flex-shrink-0' />
                            <span>{mistake}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className='flex gap-3'>
                <Button type='submit' disabled={loading}>
                  {loading ? 'Creating...' : 'Create Document'}
                </Button>
                {onCancel && (
                  <Button type='button' variant='outline' onClick={onCancel}>
                    Cancel
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Template Recommendations - Only show for template-based creation */}
      {studentId && watchedCreationMethod === 'template_based' && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Sparkles className='h-5 w-5' />
              Optional Templates
            </CardTitle>
            <CardDescription>
              Choose a template to get started, or skip to create from scratch. Templates suggested for {getDocTypeLabel(watchedDocType)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loadingTemplates ? (
              <div className='space-y-3'>
                {[...Array(3)].map((_, i) => (
                  <div key={i} className='flex items-center space-x-4'>
                    <Skeleton className='h-12 w-12 rounded' />
                    <div className='flex-1 space-y-2'>
                      <Skeleton className='h-4 w-3/4' />
                      <Skeleton className='h-3 w-1/2' />
                    </div>
                  </div>
                ))}
              </div>
            ) : templates.length > 0 ? (
              <div className='space-y-3'>
                {templates.map((template) => (
                  <div
                    key={template.id}
                    className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                      selectedTemplate?.id === template.id
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => handleTemplateSelect(template)}
                  >
                    <div className='flex items-start justify-between'>
                      <div className='space-y-1'>
                        <h4 className='font-medium'>{template.name}</h4>
                        <p className='text-muted-foreground text-sm'>
                          {template.description}
                        </p>
                        <div className='flex flex-wrap gap-2'>
                          <Badge variant='secondary'>{template.category}</Badge>
                          <Badge variant='outline'>
                            {template.difficulty_level}
                          </Badge>
                          <Badge variant='outline'>
                            {template.usage_count} uses
                          </Badge>
                        </div>
                      </div>
                      <div className='text-right'>
                        <div className='text-primary text-sm font-medium'>
                          {Math.round(template.match_score * 10)}% match
                        </div>
                      </div>
                    </div>
                    {template.reasons.length > 0 && (
                      <div className='text-muted-foreground mt-2 text-xs'>
                        {template.reasons.join(' • ')}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className='text-muted-foreground py-8 text-center'>
                <Sparkles className='mx-auto mb-2 h-8 w-8 opacity-50' />
                <p>No templates available for this document type</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
