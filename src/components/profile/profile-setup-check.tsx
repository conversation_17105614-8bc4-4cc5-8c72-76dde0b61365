'use client';

import { useEffect, useState } from 'react';
import { useCurrentUser } from '@/hooks/use-current-user';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Icons } from '@/components/icons';

interface ProfileSetupCheckProps {
  children: React.ReactNode;
  requireStudentProfile?: boolean;
}

export function ProfileSetupCheck({
  children,
  requireStudentProfile = false
}: ProfileSetupCheckProps) {
  const { user, loading, error } = useCurrentUser();
  const [isInitializing, setIsInitializing] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);

  const needsProfileSetup =
    user &&
    requireStudentProfile &&
    user.role === 'student' &&
    !user.profile?.id;

  const handleInitializeProfile = async () => {
    if (!user) return;

    setIsInitializing(true);
    setInitError(null);

    try {
      // Call the profile initialization API
      const response = await fetch('/api/profile/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to initialize profile');
      }

      // Refresh the user data
      window.location.reload();
    } catch (error) {
      console.error('Error initializing profile:', error);
      setInitError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsInitializing(false);
    }
  };

  if (loading) {
    return (
      <div className='flex min-h-[200px] items-center justify-center'>
        <div className='border-primary h-8 w-8 animate-spin rounded-full border-b-2'></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant='destructive'>
        <Icons.alertTriangle className='h-4 w-4' />
        <AlertDescription>Error loading user profile: {error}</AlertDescription>
      </Alert>
    );
  }

  if (needsProfileSetup) {
    return (
      <Card className='mx-auto max-w-md'>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Icons.user className='h-5 w-5' />
            Profile Setup Required
          </CardTitle>
          <CardDescription>
            Your student profile needs to be initialized before you can
            continue.
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          {initError && (
            <Alert variant='destructive'>
              <Icons.alertTriangle className='h-4 w-4' />
              <AlertDescription>{initError}</AlertDescription>
            </Alert>
          )}

          <div className='text-muted-foreground text-sm'>
            <p>
              We need to set up your student profile to enable document creation
              and other features.
            </p>
          </div>

          <Button
            onClick={handleInitializeProfile}
            disabled={isInitializing}
            className='w-full'
          >
            {isInitializing ? (
              <>
                <div className='mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white'></div>
                Setting up profile...
              </>
            ) : (
              <>
                <Icons.user className='mr-2 h-4 w-4' />
                Initialize Profile
              </>
            )}
          </Button>

          <div className='text-muted-foreground text-center text-xs'>
            This will create your student profile and enable all features.
          </div>
        </CardContent>
      </Card>
    );
  }

  return <>{children}</>;
}
