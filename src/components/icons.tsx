import {
  IconAlertTriangle,
  IconAlertCircle,
  IconArrowRight,
  IconCheck,
  IconChevronLeft,
  IconChevronRight,
  IconCommand,
  IconCreditCard,
  IconFile,
  IconFileText,
  IconHelpCircle,
  IconPhoto,
  IconDeviceLaptop,
  IconLayoutDashboard,
  IconLoader2,
  IconLogin,
  IconProps,
  IconShoppingBag,
  IconMoon,
  IconDotsVertical,
  IconPizza,
  IconPlus,
  IconSettings,
  IconSun,
  IconTrash,
  IconBrandTwitter,
  IconUser,
  IconUserCircle,
  IconUserEdit,
  IconUserX,
  IconX,
  IconLayoutKanban,
  IconBrandGithub,
  IconFiles,
  IconFolder,
  IconSchool,
  IconCalculator,
  IconAward,
  IconTrophy,
  IconBuilding,
  IconTarget,
  IconCircleCheck,
  IconUsers,
  IconEye,
  IconCalendar,
  IconUserCheck,
  IconChartBar,
  IconSearch,
  IconClock,
  IconEdit,
  IconExternalLink,
  IconBulb,
  IconShield,
  IconStar,
  IconBook,
  IconVideo,
  IconMapPin,
  IconPhone,
  IconList,
  IconDeviceFloppy,
  IconCamera,
  IconGrid3x3,
  IconUserPlus,
  IconDots,
  IconDownload,
  IconMessage,
  IconTrendingUp,
  IconClipboard,
  IconSend,
  IconFilter,
  IconArrowsSort,
  IconDatabase,
  IconKey,
  IconTool,
  IconLock,
  IconRefresh,
  IconInfoCircle,
  IconCopy,
  IconCrown,
  IconBook2,
  IconMail,
  IconBriefcase,
  IconUserCog
} from '@tabler/icons-react';

export type Icon = React.ComponentType<IconProps>;

export const Icons = {
  dashboard: IconLayoutDashboard,
  logo: IconCommand,
  login: IconLogin,
  close: IconX,
  product: IconShoppingBag,
  spinner: IconLoader2,
  kanban: IconLayoutKanban,
  chevronLeft: IconChevronLeft,
  chevronRight: IconChevronRight,
  trash: IconTrash,
  employee: IconUserX,
  post: IconFileText,
  page: IconFile,
  userPen: IconUserEdit,
  user2: IconUserCircle,
  media: IconPhoto,
  settings: IconSettings,
  billing: IconCreditCard,
  ellipsis: IconDotsVertical,
  add: IconPlus,
  warning: IconAlertTriangle,
  user: IconUser,
  arrowRight: IconArrowRight,
  help: IconHelpCircle,
  pizza: IconPizza,
  sun: IconSun,
  moon: IconMoon,
  laptop: IconDeviceLaptop,
  github: IconBrandGithub,
  twitter: IconBrandTwitter,
  check: IconCheck,
  // College Application System Icons
  fileText: IconFileText,
  file: IconFile,
  files: IconFiles,
  folder: IconFolder,
  graduationCap: IconSchool,
  calculator: IconCalculator,
  award: IconAward,
  trophy: IconTrophy,
  building: IconBuilding,
  target: IconTarget,
  checkCircle: IconCircleCheck,
  users: IconUsers,
  eye: IconEye,
  calendar: IconCalendar,
  userCheck: IconUserCheck,
  barChart: IconChartBar,
  search: IconSearch,
  // Additional missing icons
  plus: IconPlus,
  clock: IconClock,
  edit: IconEdit,
  externalLink: IconExternalLink,
  lightbulb: IconBulb,
  shield: IconShield,
  star: IconStar,
  book: IconBook,
  school: IconSchool,
  alertTriangle: IconAlertTriangle,
  alertCircle: IconAlertCircle,
  video: IconVideo,
  mapPin: IconMapPin,
  phone: IconPhone,
  list: IconList,
  save: IconDeviceFloppy,
  camera: IconCamera,
  grid: IconGrid3x3,
  userPlus: IconUserPlus,
  moreHorizontal: IconDots,
  download: IconDownload,
  messageSquare: IconMessage,
  trendingUp: IconTrendingUp,
  userX: IconUserX,
  clipboard: IconClipboard,
  send: IconSend,
  filter: IconFilter,
  sort: IconArrowsSort,
  database: IconDatabase,
  key: IconKey,
  wrench: IconTool,
  lock: IconLock,
  refresh: IconRefresh,
  info: IconInfoCircle,
  copy: IconCopy,
  crown: IconCrown,
  bookOpen: IconBook2,
  mail: IconMail,
  briefcase: IconBriefcase,
  userCog: IconUserCog,
  x: IconX
};
