'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Icons } from '@/components/icons';
import { useApiPost } from '@/lib/api-client';
import { toast } from 'sonner';

interface RequestAdvisorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

const SPECIALTY_OPTIONS = [
  'College Admissions',
  'Essay Writing',
  'Test Preparation',
  'Financial Aid',
  'Career Counseling',
  'Academic Planning',
  'Extracurricular Activities',
  'Interview Preparation'
];

export function RequestAdvisorDialog({ 
  open, 
  onOpenChange, 
  onSuccess 
}: RequestAdvisorDialogProps) {
  const [message, setMessage] = useState('');
  const [selectedSpecialties, setSelectedSpecialties] = useState<string[]>([]);
  
  const { post: submitRequest, loading } = useApiPost();

  const handleSpecialtyChange = (specialty: string, checked: boolean) => {
    if (checked) {
      setSelectedSpecialties(prev => [...prev, specialty]);
    } else {
      setSelectedSpecialties(prev => prev.filter(s => s !== specialty));
    }
  };

  const handleSubmit = async () => {
    try {
      const response = await submitRequest('/api/advisor-requests', {
        message: message.trim() || undefined,
        preferred_specialties: selectedSpecialties.length > 0 ? selectedSpecialties : undefined
      });

      // If we reach here, the request was successful
      toast.success('Advisor request submitted successfully! We will review your request and get back to you soon.');
      setMessage('');
      setSelectedSpecialties([]);
      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      console.error('Error submitting advisor request:', error);
      toast.error('Failed to submit advisor request');
    }
  };

  const handleCancel = () => {
    setMessage('');
    setSelectedSpecialties([]);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Icons.users className="h-5 w-5" />
            Request an Advisor
          </DialogTitle>
          <DialogDescription>
            Tell us about your needs and preferences for an advisor. Our team will review your request and assign you a suitable college counselor.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Message */}
          <div className="space-y-2">
            <Label htmlFor="message">
              Message (Optional)
            </Label>
            <Textarea
              id="message"
              placeholder="Tell us about your goals, concerns, or any specific help you're looking for..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <p className="text-xs text-muted-foreground">
              Share any specific goals, concerns, or areas where you need help.
            </p>
          </div>

          {/* Preferred Specialties */}
          <div className="space-y-3">
            <Label>Preferred Specialties (Optional)</Label>
            <p className="text-sm text-muted-foreground">
              Select areas where you&apos;d like your advisor to have expertise:
            </p>
            <div className="grid grid-cols-2 gap-3">
              {SPECIALTY_OPTIONS.map((specialty) => (
                <div key={specialty} className="flex items-center space-x-2">
                  <Checkbox
                    id={specialty}
                    checked={selectedSpecialties.includes(specialty)}
                    onCheckedChange={(checked) => 
                      handleSpecialtyChange(specialty, checked as boolean)
                    }
                  />
                  <Label 
                    htmlFor={specialty}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {specialty}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Info Box */}
          <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
            <div className="flex items-start gap-3">
              <Icons.info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-blue-900 mb-1">What happens next?</p>
                <ul className="text-blue-800 space-y-1">
                  <li>• Our team will review your request within 24-48 hours</li>
                  <li>• We&apos;ll match you with an advisor based on your needs</li>
                  <li>• You&apos;ll receive an email notification when an advisor is assigned</li>
                  <li>• Your advisor will reach out to schedule your first meeting</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={handleCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <>
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                Submitting...
              </>
            ) : (
              <>
                <Icons.send className="mr-2 h-4 w-4" />
                Submit Request
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
