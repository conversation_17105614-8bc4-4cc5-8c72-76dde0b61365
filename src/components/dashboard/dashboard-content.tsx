'use client';

import { useCurrentUser } from '@/hooks/use-current-user';
import StudentDashboard from './student-dashboard';
import ConsultantDashboard from './consultant-dashboard';
import AdminDashboard from './admin-dashboard';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw, WifiOff } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function DashboardContent() {
  const { user, loading, error, retryCount, refetch } = useCurrentUser();

  if (loading) {
    return <DashboardSkeleton retryCount={retryCount} />;
  }

  if (error) {
    return (
      <div className='flex h-64 items-center justify-center'>
        <div className='max-w-md text-center'>
          <AlertCircle className='mx-auto mb-4 h-12 w-12 text-red-500' />
          <h3 className='mb-2 text-lg font-semibold text-red-600'>
            Error Loading Dashboard
          </h3>
          <Alert className='mb-4'>
            <AlertCircle className='h-4 w-4' />
            <AlertDescription className='text-sm'>{error}</AlertDescription>
          </Alert>
          {retryCount > 0 && (
            <p className='text-muted-foreground mb-4 text-xs'>
              Retry attempt: {retryCount}/{3}
            </p>
          )}
          <div className='flex justify-center gap-2'>
            <Button onClick={refetch} variant='outline' size='sm'>
              <RefreshCw className='mr-2 h-4 w-4' />
              Try Again
            </Button>
            <Button
              onClick={() => window.location.reload()}
              variant='ghost'
              size='sm'
            >
              Refresh Page
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className='flex h-64 items-center justify-center'>
        <div className='max-w-md text-center'>
          <WifiOff className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
          <h3 className='mb-2 text-lg font-semibold'>No User Data</h3>
          <p className='text-muted-foreground mb-4 text-sm'>
            Unable to load user information. This might be a temporary
            connection issue.
          </p>
          <div className='flex justify-center gap-2'>
            <Button onClick={refetch} variant='outline' size='sm'>
              <RefreshCw className='mr-2 h-4 w-4' />
              Retry
            </Button>
            <Button
              onClick={() => (window.location.href = '/auth/sign-in')}
              variant='ghost'
              size='sm'
            >
              Sign In Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  switch (user.role) {
    case 'student':
      return <StudentDashboard user={user} />;
    case 'consultant':
      return <ConsultantDashboard user={user} />;
    case 'admin':
      return <AdminDashboard user={user} />;
    default:
      return (
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <h3 className='text-lg font-semibold'>Unknown User Role</h3>
            <p className='text-muted-foreground mt-2 text-sm'>
              Role: {user.role}
            </p>
          </div>
        </div>
      );
  }
}

function DashboardSkeleton({ retryCount }: { retryCount?: number }) {
  return (
    <div className='space-y-4'>
      {retryCount && retryCount > 0 && (
        <div className='flex items-center justify-center py-2'>
          <div className='text-muted-foreground flex items-center gap-2 text-sm'>
            <RefreshCw className='h-4 w-4 animate-spin' />
            <span>Loading user data... (attempt {retryCount})</span>
          </div>
        </div>
      )}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className='h-32' />
        ))}
      </div>
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-7'>
        <Skeleton className='h-80 md:col-span-4' />
        <Skeleton className='h-80 md:col-span-3' />
      </div>
    </div>
  );
}
