'use client';

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useApiGet } from '@/lib/api-client';
import type { User } from '@/types/application';
import { RequestAdvisorDialog } from '@/components/dialogs/request-advisor-dialog';

interface StudentDashboardProps {
  user: User;
}

interface StudentDashboardStats {
  progress: {
    applicationProgress: number;
    essaysCompleted: number;
    totalEssays: number;
    schoolsApplied: number;
    totalSchools: number;
  };
  documents: {
    totalDocuments: number;
    completedDocuments: number;
    inReviewDocuments: number;
    draftDocuments: number;
  };
  activities: {
    totalActivities: number;
    totalHours: number;
    leadershipRoles: number;
  };
  testScores: {
    satScore?: number;
    actScore?: number;
    apScores: number;
  };
  advisor?: {
    id: string;
    name: string;
    email: string;
    title: string;
  };
}

interface TargetSchool {
  id: string;
  student_id: string;
  school_id: string;
  application_type: 'early_decision' | 'early_action' | 'regular_decision' | 'rolling_admission';
  deadline: string;
  priority: 'safety' | 'target' | 'reach';
  notes?: string;
  status: 'not_started' | 'in_progress' | 'submitted' | 'decision_received';
  schools: {
    id: string;
    name: string;
    details: any;
  };
  created_at: string;
  updated_at: string;
}

export default function StudentDashboard({ user }: StudentDashboardProps) {
  const [showRequestAdvisorDialog, setShowRequestAdvisorDialog] = useState(false);

  const {
    data: stats,
    loading,
    error,
    fetch: fetchStats
  } = useApiGet<StudentDashboardStats>('/api/stats/dashboard', {
    role: 'student'
  });

  // Fetch target schools for upcoming deadlines
  const {
    data: targetSchools,
    loading: targetSchoolsLoading,
    fetch: fetchTargetSchools
  } = useApiGet<TargetSchool[]>('/api/students/target-schools');

  useEffect(() => {
    fetchStats();
    fetchTargetSchools();
  }, [fetchStats, fetchTargetSchools]);

  // Helper function to calculate days until deadline
  const getDaysUntilDeadline = (deadline: string): number => {
    const deadlineDate = new Date(deadline);
    const today = new Date();
    const diffTime = deadlineDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Helper function to get deadline urgency color
  const getDeadlineColor = (daysLeft: number): string => {
    if (daysLeft <= 7) return 'text-red-500';
    if (daysLeft <= 30) return 'text-orange-500';
    return 'text-blue-500';
  };

  // Get upcoming deadlines (next 90 days)
  const upcomingDeadlines = targetSchools
    ?.filter(school => {
      const daysLeft = getDaysUntilDeadline(school.deadline);
      return daysLeft > 0 && daysLeft <= 90 && school.status !== 'submitted';
    })
    .sort((a, b) => new Date(a.deadline).getTime() - new Date(b.deadline).getTime())
    .slice(0, 3) || [];

  // Use real data if available, fallback to defaults
  const applicationProgress = stats?.progress.applicationProgress || 0;
  const essaysCompleted = stats?.progress.essaysCompleted || 0;
  const totalEssays = stats?.progress.totalEssays || 0;
  const schoolsApplied = stats?.progress.schoolsApplied || 0;
  const totalSchools = stats?.progress.totalSchools || 0;

  return (
    <div className='space-y-6'>
      {/* Welcome Section */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold'>Welcome back!</h1>
          <p className='text-muted-foreground'>
            Here&apos;s your college application progress
          </p>
        </div>
        <Button asChild>
          <Link href='/dashboard/essays'>
            <Icons.fileText className='mr-2 h-4 w-4' />
            Continue Writing
          </Link>
        </Button>
      </div>

      {/* Progress Overview */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Overall Progress
            </CardTitle>
            <Icons.target className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{applicationProgress}%</div>
            <Progress value={applicationProgress} className='mt-2' />
            <p className='text-muted-foreground mt-2 text-xs'>
              Keep going! You&apos;re making great progress.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Essays</CardTitle>
            <Icons.fileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {essaysCompleted}/{totalEssays}
            </div>
            <p className='text-muted-foreground text-xs'>
              {totalEssays - essaysCompleted} essays remaining
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Applications</CardTitle>
            <Icons.building className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {schoolsApplied}/{totalSchools}
            </div>
            <p className='text-muted-foreground text-xs'>
              {totalSchools - schoolsApplied} schools to apply
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Test Scores</CardTitle>
            <Icons.calculator className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className='flex items-center justify-center py-2'>
                <Icons.spinner className='h-4 w-4 animate-spin' />
              </div>
            ) : (
              <>
                <div className='text-2xl font-bold'>
                  {stats?.testScores.satScore ||
                    stats?.testScores.actScore ||
                    'N/A'}
                </div>
                <p className='text-muted-foreground text-xs'>
                  {stats?.testScores.satScore
                    ? 'SAT Score'
                    : stats?.testScores.actScore
                      ? 'ACT Score'
                      : 'No scores yet'}
                  {stats?.testScores.apScores
                    ? ` • ${stats.testScores.apScores} AP scores`
                    : ''}
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* My Advisor & My Documents */}
      <div className='grid gap-4 md:grid-cols-2'>
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.users className='h-5 w-5' />
              My Advisor
            </CardTitle>
            <CardDescription>Your assigned college counselor</CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            {loading ? (
              <div className='flex items-center justify-center py-4'>
                <Icons.spinner className='h-6 w-6 animate-spin' />
              </div>
            ) : stats?.advisor ? (
              <>
                <div className='flex items-center space-x-3'>
                  <div className='flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-600 font-semibold text-white'>
                    {stats.advisor.name
                      .split(' ')
                      .map((n) => n[0])
                      .join('')
                      .toUpperCase()}
                  </div>
                  <div>
                    <p className='font-medium'>{stats.advisor.name}</p>
                    <p className='text-muted-foreground text-sm'>
                      {stats.advisor.title}
                    </p>
                  </div>
                </div>
                <div className='space-y-2'>
                  <div className='flex items-center gap-2 text-sm'>
                    <Icons.messageSquare className='text-muted-foreground h-4 w-4' />
                    <span>{stats.advisor.email}</span>
                  </div>
                  <div className='flex items-center gap-2 text-sm'>
                    <Icons.calendar className='text-muted-foreground h-4 w-4' />
                    <span>Schedule your next meeting</span>
                  </div>
                </div>
                <div className='flex gap-2'>
                  <Button size='sm' className='flex-1'>
                    <Icons.messageSquare className='mr-2 h-4 w-4' />
                    Message
                  </Button>
                  <Button size='sm' variant='outline' className='flex-1'>
                    <Icons.calendar className='mr-2 h-4 w-4' />
                    Schedule
                  </Button>
                </div>
              </>
            ) : (
              <div className='py-4 text-center'>
                <p className='text-muted-foreground text-sm'>
                  No advisor assigned yet
                </p>
                <Button
                  size='sm'
                  variant='outline'
                  className='mt-2'
                  onClick={() => setShowRequestAdvisorDialog(true)}
                >
                  Request Advisor
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.fileText className='h-5 w-5' />
              My Documents
            </CardTitle>
            <CardDescription>
              Quick access to your essays and documents
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            {loading ? (
              <div className='flex items-center justify-center py-4'>
                <Icons.spinner className='h-6 w-6 animate-spin' />
              </div>
            ) : (
              <>
                <div className='grid grid-cols-3 gap-4 text-center'>
                  <div>
                    <p className='text-2xl font-bold text-green-600'>
                      {stats?.documents.completedDocuments || 0}
                    </p>
                    <p className='text-muted-foreground text-xs'>Completed</p>
                  </div>
                  <div>
                    <p className='text-2xl font-bold text-yellow-600'>
                      {stats?.documents.inReviewDocuments || 0}
                    </p>
                    <p className='text-muted-foreground text-xs'>In Review</p>
                  </div>
                  <div>
                    <p className='text-2xl font-bold text-gray-600'>
                      {stats?.documents.draftDocuments || 0}
                    </p>
                    <p className='text-muted-foreground text-xs'>Draft</p>
                  </div>
                </div>
                <div className='space-y-2'>
                  <div className='flex items-center justify-between text-sm'>
                    <span>Total Documents</span>
                    <span className='font-medium'>
                      {stats?.documents.totalDocuments || 0}
                    </span>
                  </div>
                  <Progress
                    value={
                      stats?.documents.totalDocuments
                        ? (stats.documents.completedDocuments /
                            stats.documents.totalDocuments) *
                          100
                        : 0
                    }
                    className='h-2'
                  />
                  <p className='text-muted-foreground text-center text-xs'>
                    {stats?.documents.completedDocuments || 0} of{' '}
                    {stats?.documents.totalDocuments || 0} documents completed
                  </p>
                </div>
                <Button asChild size='sm' variant='outline' className='w-full'>
                  <Link href='/dashboard/documents'>View All Documents</Link>
                </Button>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Assignment Status & Recent Activity */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-7'>
        <Card className='md:col-span-4'>
          <CardHeader>
            <CardTitle>Assignment Status & Recent Activity</CardTitle>
            <CardDescription>
              Track your assignments and latest activities
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='flex items-center space-x-4'>
              <div className='flex h-8 w-8 items-center justify-center rounded-full bg-red-100'>
                <Icons.clock className='h-4 w-4 text-red-600' />
              </div>
              <div className='flex-1 space-y-1'>
                <p className='text-sm font-medium'>
                  Harvard Supplemental Essay - Due Tomorrow
                </p>
                <p className='text-muted-foreground text-xs'>
                  Assigned by Dr. Jane Smith
                </p>
              </div>
              <Badge variant='destructive'>Urgent</Badge>
            </div>

            <div className='flex items-center space-x-4'>
              <div className='flex h-8 w-8 items-center justify-center rounded-full bg-blue-100'>
                <Icons.fileText className='h-4 w-4 text-blue-600' />
              </div>
              <div className='flex-1 space-y-1'>
                <p className='text-sm font-medium'>
                  Personal Statement Updated
                </p>
                <p className='text-muted-foreground text-xs'>2 hours ago</p>
              </div>
              <Badge variant='secondary'>Essay</Badge>
            </div>

            <div className='flex items-center space-x-4'>
              <div className='flex h-8 w-8 items-center justify-center rounded-full bg-green-100'>
                <Icons.checkCircle className='h-4 w-4 text-green-600' />
              </div>
              <div className='flex-1 space-y-1'>
                <p className='text-sm font-medium'>
                  Stanford Application Submitted
                </p>
                <p className='text-muted-foreground text-xs'>1 day ago</p>
              </div>
              <Badge variant='default'>Application</Badge>
            </div>

            <div className='flex items-center space-x-4'>
              <div className='flex h-8 w-8 items-center justify-center rounded-full bg-purple-100'>
                <Icons.calculator className='h-4 w-4 text-purple-600' />
              </div>
              <div className='flex-1 space-y-1'>
                <p className='text-sm font-medium'>SAT Score Added</p>
                <p className='text-muted-foreground text-xs'>3 days ago</p>
              </div>
              <Badge variant='outline'>Test Score</Badge>
            </div>
          </CardContent>
        </Card>

        <Card className='md:col-span-3'>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.lightbulb className='h-5 w-5' />
              Personalized Recommendations
            </CardTitle>
            <CardDescription>
              AI-powered suggestions for your success
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='rounded-lg border border-blue-200 bg-blue-50 p-3'>
              <div className='flex items-start gap-2'>
                <Icons.target className='mt-0.5 h-4 w-4 text-blue-600' />
                <div>
                  <p className='text-sm font-medium text-blue-900'>
                    Focus on Harvard Essay
                  </p>
                  <p className='text-xs text-blue-700'>
                    Due tomorrow - prioritize completion
                  </p>
                </div>
              </div>
            </div>

            <div className='rounded-lg border border-green-200 bg-green-50 p-3'>
              <div className='flex items-start gap-2'>
                <Icons.trendingUp className='mt-0.5 h-4 w-4 text-green-600' />
                <div>
                  <p className='text-sm font-medium text-green-900'>
                    Strong SAT Score
                  </p>
                  <p className='text-xs text-green-700'>
                    Consider applying to more reach schools
                  </p>
                </div>
              </div>
            </div>

            <div className='rounded-lg border border-orange-200 bg-orange-50 p-3'>
              <div className='flex items-start gap-2'>
                <Icons.clock className='mt-0.5 h-4 w-4 text-orange-600' />
                <div>
                  <p className='text-sm font-medium text-orange-900'>
                    Update Activities
                  </p>
                  <p className='text-xs text-orange-700'>
                    Add recent leadership experiences
                  </p>
                </div>
              </div>
            </div>

            <Button asChild size='sm' variant='outline' className='w-full'>
              <Link href='/dashboard/recommendations'>
                View All Recommendations
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common tasks to help you progress</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid gap-3 md:grid-cols-2 lg:grid-cols-4'>
            <Button asChild className='h-auto justify-start p-4'>
              <Link href='/dashboard/essays/personal-statement'>
                <div className='flex flex-col items-start gap-1'>
                  <div className='flex items-center gap-2'>
                    <Icons.fileText className='h-4 w-4' />
                    <span className='font-medium'>Edit Personal Statement</span>
                  </div>
                  <span className='text-xs opacity-90'>
                    Continue writing your main essay
                  </span>
                </div>
              </Link>
            </Button>

            <Button
              asChild
              variant='outline'
              className='h-auto justify-start p-4'
            >
              <Link href='/dashboard/academics/test-scores'>
                <div className='flex flex-col items-start gap-1'>
                  <div className='flex items-center gap-2'>
                    <Icons.calculator className='h-4 w-4' />
                    <span className='font-medium'>Add Test Scores</span>
                  </div>
                  <span className='text-muted-foreground text-xs'>
                    Update your SAT/ACT scores
                  </span>
                </div>
              </Link>
            </Button>

            <Button
              asChild
              variant='outline'
              className='h-auto justify-start p-4'
            >
              <Link href='/dashboard/schools/target'>
                <div className='flex flex-col items-start gap-1'>
                  <div className='flex items-center gap-2'>
                    <Icons.building className='h-4 w-4' />
                    <span className='font-medium'>Manage Schools</span>
                  </div>
                  <span className='text-muted-foreground text-xs'>
                    Add or edit target schools
                  </span>
                </div>
              </Link>
            </Button>

            <Button
              asChild
              variant='outline'
              className='h-auto justify-start p-4'
            >
              <Link href='/dashboard/activities'>
                <div className='flex flex-col items-start gap-1'>
                  <div className='flex items-center gap-2'>
                    <Icons.trophy className='h-4 w-4' />
                    <span className='font-medium'>Update Activities</span>
                  </div>
                  <span className='text-muted-foreground text-xs'>
                    Add extracurricular activities
                  </span>
                </div>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Activities Summary */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Icons.trophy className='h-5 w-5' />
            Activities Summary
          </CardTitle>
          <CardDescription>
            Your extracurricular involvement overview
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className='flex items-center justify-center py-4'>
              <Icons.spinner className='h-6 w-6 animate-spin' />
            </div>
          ) : (
            <div className='grid grid-cols-3 gap-4 text-center'>
              <div>
                <p className='text-2xl font-bold text-blue-600'>
                  {stats?.activities.totalActivities || 0}
                </p>
                <p className='text-muted-foreground text-xs'>
                  Total Activities
                </p>
              </div>
              <div>
                <p className='text-2xl font-bold text-green-600'>
                  {stats?.activities.totalHours || 0}
                </p>
                <p className='text-muted-foreground text-xs'>Total Hours</p>
              </div>
              <div>
                <p className='text-2xl font-bold text-purple-600'>
                  {stats?.activities.leadershipRoles || 0}
                </p>
                <p className='text-muted-foreground text-xs'>
                  Leadership Roles
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upcoming Deadlines */}
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Deadlines</CardTitle>
          <CardDescription>
            Don&apos;t miss these important dates
          </CardDescription>
        </CardHeader>
        <CardContent>
          {targetSchoolsLoading ? (
            <div className='space-y-4'>
              <div className='flex items-center justify-center py-4'>
                <Icons.spinner className='h-6 w-6 animate-spin' />
              </div>
            </div>
          ) : upcomingDeadlines.length > 0 ? (
            <div className='space-y-4'>
              {upcomingDeadlines.map((school) => {
                const daysLeft = getDaysUntilDeadline(school.deadline);
                const colorClass = getDeadlineColor(daysLeft);

                return (
                  <div key={school.id} className='flex items-center justify-between rounded-lg border p-3'>
                    <div className='flex items-center space-x-3'>
                      <Icons.calendar className={`h-5 w-5 ${colorClass}`} />
                      <div>
                        <p className='font-medium'>
                          {school.schools.name} {school.application_type.replace('_', ' ').toUpperCase()}
                        </p>
                        <p className='text-muted-foreground text-sm'>
                          Application deadline
                        </p>
                      </div>
                    </div>
                    <div className='text-right'>
                      <p className={`font-medium ${colorClass}`}>
                        {new Date(school.deadline).toLocaleDateString()}
                      </p>
                      <p className='text-muted-foreground text-xs'>
                        {daysLeft === 1 ? '1 day left' : `${daysLeft} days left`}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className='flex flex-col items-center justify-center py-8'>
              <Icons.calendar className='text-muted-foreground mb-4 h-12 w-12' />
              <h3 className='mb-2 text-lg font-semibold'>No upcoming deadlines</h3>
              <p className='text-muted-foreground mb-4 text-center'>
                Add target schools to see your application deadlines
              </p>
              <Button asChild>
                <Link href='/dashboard/schools/add'>
                  <Icons.plus className='mr-2 h-4 w-4' />
                  Add Schools
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Request Advisor Dialog */}
      <RequestAdvisorDialog
        open={showRequestAdvisorDialog}
        onOpenChange={setShowRequestAdvisorDialog}
        onSuccess={() => {
          // Refresh stats to update advisor info
          fetchStats();
        }}
      />
    </div>
  );
}
