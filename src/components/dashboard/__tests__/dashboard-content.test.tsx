import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import DashboardContent from '../dashboard-content';
import { useCurrentUser } from '@/hooks/use-current-user';

// Mock the useCurrentUser hook
jest.mock('@/hooks/use-current-user');
const mockUseCurrentUser = useCurrentUser as jest.MockedFunction<typeof useCurrentUser>;

// Mock the dashboard components
jest.mock('../student-dashboard', () => {
  return function MockStudentDashboard() {
    return <div data-testid="student-dashboard">Student Dashboard</div>;
  };
});

jest.mock('../consultant-dashboard', () => {
  return function MockConsultantDashboard() {
    return <div data-testid="consultant-dashboard">Consultant Dashboard</div>;
  };
});

jest.mock('../admin-dashboard', () => {
  return function MockAdminDashboard() {
    return <div data-testid="admin-dashboard">Admin Dashboard</div>;
  };
});

describe('DashboardContent', () => {
  const mockRefetch = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Loading State', () => {
    it('shows loading skeleton when loading', () => {
      mockUseCurrentUser.mockReturnValue({
        user: null,
        loading: true,
        error: null,
        retryCount: 0,
        lastKnownRole: null,
        isStudent: false,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      render(<DashboardContent />);

      // Should show skeleton loading state
      expect(screen.getByText('Loading your dashboard...')).toBeInTheDocument();
    });

    it('shows retry count when retrying', () => {
      mockUseCurrentUser.mockReturnValue({
        user: null,
        loading: true,
        error: null,
        retryCount: 2,
        lastKnownRole: null,
        isStudent: false,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      render(<DashboardContent />);

      expect(screen.getByText('Retrying... (Attempt 2/3)')).toBeInTheDocument();
    });
  });

  describe('Error State', () => {
    it('shows error message when there is an error', () => {
      mockUseCurrentUser.mockReturnValue({
        user: null,
        loading: false,
        error: 'Failed to load user data',
        retryCount: 0,
        lastKnownRole: null,
        isStudent: false,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      render(<DashboardContent />);

      expect(screen.getByText('Unable to Load Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Failed to load user data')).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });

    it('calls refetch when retry button is clicked', async () => {
      mockUseCurrentUser.mockReturnValue({
        user: null,
        loading: false,
        error: 'Network error',
        retryCount: 0,
        lastKnownRole: null,
        isStudent: false,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      render(<DashboardContent />);

      const retryButton = screen.getByText('Try Again');
      fireEvent.click(retryButton);

      expect(mockRefetch).toHaveBeenCalledTimes(1);
    });
  });

  describe('No User State', () => {
    it('shows no user message when user is null and not loading', () => {
      mockUseCurrentUser.mockReturnValue({
        user: null,
        loading: false,
        error: null,
        retryCount: 0,
        lastKnownRole: null,
        isStudent: false,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      render(<DashboardContent />);

      expect(screen.getByText('No User Data')).toBeInTheDocument();
      expect(screen.getByText('Please sign in to access your dashboard.')).toBeInTheDocument();
    });
  });

  describe('Role-based Dashboard Rendering', () => {
    const baseUser = {
      id: 'user-1',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
    };

    it('renders student dashboard for student role', () => {
      mockUseCurrentUser.mockReturnValue({
        user: { ...baseUser, role: 'student' },
        loading: false,
        error: null,
        retryCount: 0,
        lastKnownRole: 'student',
        isStudent: true,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      render(<DashboardContent />);

      expect(screen.getByTestId('student-dashboard')).toBeInTheDocument();
      expect(screen.queryByTestId('consultant-dashboard')).not.toBeInTheDocument();
      expect(screen.queryByTestId('admin-dashboard')).not.toBeInTheDocument();
    });

    it('renders consultant dashboard for consultant role', () => {
      mockUseCurrentUser.mockReturnValue({
        user: { ...baseUser, role: 'consultant' },
        loading: false,
        error: null,
        retryCount: 0,
        lastKnownRole: 'consultant',
        isStudent: false,
        isConsultant: true,
        isAdmin: false,
        refetch: mockRefetch,
      });

      render(<DashboardContent />);

      expect(screen.getByTestId('consultant-dashboard')).toBeInTheDocument();
      expect(screen.queryByTestId('student-dashboard')).not.toBeInTheDocument();
      expect(screen.queryByTestId('admin-dashboard')).not.toBeInTheDocument();
    });

    it('renders admin dashboard for admin role', () => {
      mockUseCurrentUser.mockReturnValue({
        user: { ...baseUser, role: 'admin' },
        loading: false,
        error: null,
        retryCount: 0,
        lastKnownRole: 'admin',
        isStudent: false,
        isConsultant: false,
        isAdmin: true,
        refetch: mockRefetch,
      });

      render(<DashboardContent />);

      expect(screen.getByTestId('admin-dashboard')).toBeInTheDocument();
      expect(screen.queryByTestId('student-dashboard')).not.toBeInTheDocument();
      expect(screen.queryByTestId('consultant-dashboard')).not.toBeInTheDocument();
    });

    it('shows unknown role message for unrecognized role', () => {
      mockUseCurrentUser.mockReturnValue({
        user: { ...baseUser, role: 'unknown' },
        loading: false,
        error: null,
        retryCount: 0,
        lastKnownRole: 'unknown',
        isStudent: false,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      render(<DashboardContent />);

      expect(screen.getByText('Unknown Role')).toBeInTheDocument();
      expect(screen.getByText('Your account role is not recognized. Please contact support.')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels for error state', () => {
      mockUseCurrentUser.mockReturnValue({
        user: null,
        loading: false,
        error: 'Test error',
        retryCount: 0,
        lastKnownRole: null,
        isStudent: false,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      render(<DashboardContent />);

      const alert = screen.getByRole('alert');
      expect(alert).toBeInTheDocument();
    });

    it('has accessible retry button', () => {
      mockUseCurrentUser.mockReturnValue({
        user: null,
        loading: false,
        error: 'Test error',
        retryCount: 0,
        lastKnownRole: null,
        isStudent: false,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      render(<DashboardContent />);

      const retryButton = screen.getByRole('button', { name: /try again/i });
      expect(retryButton).toBeInTheDocument();
      expect(retryButton).toBeEnabled();
    });
  });

  describe('Edge Cases', () => {
    it('handles user with missing role gracefully', () => {
      mockUseCurrentUser.mockReturnValue({
        user: { ...{ id: 'user-1', email: '<EMAIL>' }, role: undefined },
        loading: false,
        error: null,
        retryCount: 0,
        lastKnownRole: null,
        isStudent: false,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      render(<DashboardContent />);

      expect(screen.getByText('Unknown Role')).toBeInTheDocument();
    });

    it('handles rapid state changes without errors', async () => {
      const { rerender } = render(<DashboardContent />);

      // Start with loading
      mockUseCurrentUser.mockReturnValue({
        user: null,
        loading: true,
        error: null,
        retryCount: 0,
        lastKnownRole: null,
        isStudent: false,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      rerender(<DashboardContent />);

      // Then error
      mockUseCurrentUser.mockReturnValue({
        user: null,
        loading: false,
        error: 'Error',
        retryCount: 0,
        lastKnownRole: null,
        isStudent: false,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      rerender(<DashboardContent />);

      // Then success
      mockUseCurrentUser.mockReturnValue({
        user: { id: 'user-1', email: '<EMAIL>', role: 'student' },
        loading: false,
        error: null,
        retryCount: 0,
        lastKnownRole: 'student',
        isStudent: true,
        isConsultant: false,
        isAdmin: false,
        refetch: mockRefetch,
      });

      rerender(<DashboardContent />);

      await waitFor(() => {
        expect(screen.getByTestId('student-dashboard')).toBeInTheDocument();
      });
    });
  });
});
