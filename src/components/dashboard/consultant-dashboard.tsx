'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { useEffect } from 'react';
import { useApiGet } from '@/lib/api-client';
import type { User } from '@/types/application';

interface ConsultantDashboardProps {
  user: User;
}

interface ConsultantDashboardStats {
  students: {
    totalStudents: number;
    activeStudents: number;
    studentsNeedingAttention: number;
  };
  documents: {
    documentsToReview: number;
    documentsReviewedThisWeek: number;
    totalDocumentsManaged: number;
  };
  meetings: {
    upcomingMeetings: number;
    meetingsThisWeek: number;
    totalMeetingsHeld: number;
  };
  performance: {
    averageStudentProgress: number;
    studentsWithHighProgress: number;
    studentsWithLowProgress: number;
  };
}

export default function ConsultantDashboard({
  user
}: ConsultantDashboardProps) {
  const {
    data: stats,
    loading,
    error,
    fetch: fetchStats
  } = useApiGet<ConsultantDashboardStats>('/api/stats/dashboard', {
    role: 'consultant'
  });

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  // Use real data if available, fallback to defaults
  const totalStudents = stats?.students.totalStudents || 0;
  const activeStudents = stats?.students.activeStudents || 0;
  const documentsToReview = stats?.documents.documentsToReview || 0;
  const upcomingMeetings = stats?.meetings.upcomingMeetings || 0;

  const recentStudents = [
    {
      id: '1',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      status: 'Active',
      lastActivity: '2 hours ago',
      progress: 75
    },
    {
      id: '2',
      name: 'Michael Chen',
      email: '<EMAIL>',
      status: 'Active',
      lastActivity: '1 day ago',
      progress: 45
    },
    {
      id: '3',
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      status: 'Needs Attention',
      lastActivity: '3 days ago',
      progress: 30
    }
  ];

  return (
    <div className='space-y-6'>
      {/* Welcome Section */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold'>Advisor Dashboard</h1>
          <p className='text-muted-foreground'>
            Manage your students, review documents, and track progress
          </p>
        </div>
        <div className='flex gap-2'>
          <Button asChild variant='outline'>
            <Link href='/dashboard/assignments/create'>
              <Icons.plus className='mr-2 h-4 w-4' />
              Create Assignment
            </Link>
          </Button>
          <Button asChild>
            <Link href='/dashboard/documents/review'>
              <Icons.eye className='mr-2 h-4 w-4' />
              Review Documents
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Students
            </CardTitle>
            <Icons.users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{totalStudents}</div>
            <p className='text-muted-foreground text-xs'>
              {activeStudents} currently active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Documents to Review
            </CardTitle>
            <Icons.eye className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{documentsToReview}</div>
            <p className='text-muted-foreground text-xs'>
              Awaiting your feedback
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Upcoming Meetings
            </CardTitle>
            <Icons.calendar className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{upcomingMeetings}</div>
            <p className='text-muted-foreground text-xs'>This week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Students Needing Attention
            </CardTitle>
            <Icons.alertTriangle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-orange-600'>3</div>
            <p className='text-muted-foreground text-xs'>Behind schedule</p>
          </CardContent>
        </Card>
      </div>

      {/* Student Progress Alerts & Assignment Management */}
      <div className='grid gap-4 md:grid-cols-2'>
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.alertTriangle className='h-5 w-5 text-orange-500' />
              Student Progress Alerts
            </CardTitle>
            <CardDescription>
              Students who need immediate attention
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='rounded-lg border border-red-200 bg-red-50 p-3'>
              <div className='flex items-start justify-between'>
                <div>
                  <p className='font-medium text-red-900'>Emily Rodriguez</p>
                  <p className='text-sm text-red-700'>No activity for 5 days</p>
                  <p className='text-xs text-red-600'>
                    Progress: 30% (Behind schedule)
                  </p>
                </div>
                <Badge variant='destructive' className='text-xs'>
                  Critical
                </Badge>
              </div>
            </div>

            <div className='rounded-lg border border-orange-200 bg-orange-50 p-3'>
              <div className='flex items-start justify-between'>
                <div>
                  <p className='font-medium text-orange-900'>Michael Chen</p>
                  <p className='text-sm text-orange-700'>
                    Missing 2 assignments
                  </p>
                  <p className='text-xs text-orange-600'>
                    Progress: 45% (Needs support)
                  </p>
                </div>
                <Badge variant='secondary' className='text-xs'>
                  Warning
                </Badge>
              </div>
            </div>

            <div className='rounded-lg border border-yellow-200 bg-yellow-50 p-3'>
              <div className='flex items-start justify-between'>
                <div>
                  <p className='font-medium text-yellow-900'>Alex Thompson</p>
                  <p className='text-sm text-yellow-700'>
                    Deadline approaching
                  </p>
                  <p className='text-xs text-yellow-600'>
                    Harvard essay due tomorrow
                  </p>
                </div>
                <Badge variant='outline' className='text-xs'>
                  Reminder
                </Badge>
              </div>
            </div>

            <Button size='sm' variant='outline' className='w-full'>
              View All Alerts
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.clipboard className='h-5 w-5 text-blue-500' />
              Assignment Management
            </CardTitle>
            <CardDescription>
              Create and track student assignments
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='rounded-lg border p-3'>
              <div className='mb-2 flex items-center justify-between'>
                <p className='font-medium'>Harvard Supplemental Essays</p>
                <Badge variant='default' className='text-xs'>
                  Active
                </Badge>
              </div>
              <p className='text-muted-foreground mb-2 text-sm'>
                Due: Dec 15, 2024
              </p>
              <div className='flex items-center justify-between'>
                <span className='text-muted-foreground text-xs'>
                  8/12 students completed
                </span>
                <div className='h-2 w-20 rounded-full bg-gray-200'>
                  <div className='h-2 w-16 rounded-full bg-blue-600'></div>
                </div>
              </div>
            </div>

            <div className='rounded-lg border p-3'>
              <div className='mb-2 flex items-center justify-between'>
                <p className='font-medium'>Personal Statement Draft</p>
                <Badge variant='secondary' className='text-xs'>
                  Review
                </Badge>
              </div>
              <p className='text-muted-foreground mb-2 text-sm'>
                Due: Nov 30, 2024
              </p>
              <div className='flex items-center justify-between'>
                <span className='text-muted-foreground text-xs'>
                  12/12 students completed
                </span>
                <div className='h-2 w-20 rounded-full bg-gray-200'>
                  <div className='h-2 w-20 rounded-full bg-green-600'></div>
                </div>
              </div>
            </div>

            <div className='flex gap-2'>
              <Button size='sm' className='flex-1'>
                <Icons.plus className='mr-2 h-4 w-4' />
                New Assignment
              </Button>
              <Button size='sm' variant='outline' className='flex-1'>
                View All
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Students Overview & Communication Tools */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-7'>
        <Card className='md:col-span-4'>
          <CardHeader>
            <CardTitle>Recent Student Activity</CardTitle>
            <CardDescription>
              Your students&apos; latest updates
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            {recentStudents.map((student) => (
              <div key={student.id} className='flex items-center space-x-4'>
                <Avatar>
                  <AvatarImage
                    src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${student.name}`}
                  />
                  <AvatarFallback>
                    {student.name
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
                <div className='flex-1 space-y-1'>
                  <div className='flex items-center justify-between'>
                    <p className='text-sm font-medium'>{student.name}</p>
                    <Badge
                      variant={
                        student.status === 'Active' ? 'default' : 'destructive'
                      }
                      className='text-xs'
                    >
                      {student.status}
                    </Badge>
                  </div>
                  <div className='flex items-center justify-between'>
                    <p className='text-muted-foreground text-xs'>
                      {student.email}
                    </p>
                    <p className='text-muted-foreground text-xs'>
                      {student.lastActivity}
                    </p>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <div className='h-2 flex-1 rounded-full bg-gray-200'>
                      <div
                        className='h-2 rounded-full bg-blue-600'
                        style={{ width: `${student.progress}%` }}
                      />
                    </div>
                    <span className='text-muted-foreground text-xs'>
                      {student.progress}%
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card className='md:col-span-3'>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.messageSquare className='h-5 w-5' />
              Communication & Quick Actions
            </CardTitle>
            <CardDescription>
              Student communication and common tasks
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='rounded-lg border border-blue-200 bg-blue-50 p-3'>
              <div className='mb-2 flex items-center justify-between'>
                <p className='text-sm font-medium text-blue-900'>
                  Unread Messages
                </p>
                <Badge variant='default' className='text-xs'>
                  7
                </Badge>
              </div>
              <div className='flex gap-2'>
                <Button size='sm' className='flex-1'>
                  <Icons.messageSquare className='mr-2 h-4 w-4' />
                  View Messages
                </Button>
                <Button size='sm' variant='outline' className='flex-1'>
                  <Icons.send className='mr-2 h-4 w-4' />
                  Send Message
                </Button>
              </div>
            </div>

            <Button asChild className='w-full justify-start'>
              <Link href='/dashboard/documents/review'>
                <Icons.eye className='mr-2 h-4 w-4' />
                Review Documents (5 pending)
              </Link>
            </Button>

            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/students'>
                <Icons.users className='mr-2 h-4 w-4' />
                View All Students
              </Link>
            </Button>

            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/meetings'>
                <Icons.calendar className='mr-2 h-4 w-4' />
                Schedule Meeting
              </Link>
            </Button>

            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/analytics'>
                <Icons.barChart className='mr-2 h-4 w-4' />
                Performance Analytics
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Documents to Review Queue with Priority Sorting */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div>
              <CardTitle className='flex items-center gap-2'>
                <Icons.eye className='h-5 w-5' />
                Documents to Review Queue
              </CardTitle>
              <CardDescription>
                Priority-sorted essays and documents awaiting feedback
              </CardDescription>
            </div>
            <div className='flex gap-2'>
              <Button size='sm' variant='outline'>
                <Icons.filter className='mr-2 h-4 w-4' />
                Filter
              </Button>
              <Button size='sm' variant='outline'>
                <Icons.sort className='mr-2 h-4 w-4' />
                Sort by Priority
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {/* High Priority */}
            <div className='flex items-center justify-between rounded-lg border-2 border-red-200 bg-red-50 p-3'>
              <div className='flex items-center space-x-3'>
                <Icons.alertTriangle className='h-5 w-5 text-red-500' />
                <div>
                  <div className='flex items-center gap-2'>
                    <p className='font-medium'>Alex Thompson - Harvard Essay</p>
                    <Badge variant='destructive' className='text-xs'>
                      URGENT
                    </Badge>
                  </div>
                  <p className='text-muted-foreground text-sm'>
                    Submitted 3 hours ago • Deadline: Tomorrow
                  </p>
                </div>
              </div>
              <div className='flex items-center space-x-2'>
                <Badge variant='secondary'>Supplemental</Badge>
                <Button size='sm' className='bg-red-600 hover:bg-red-700'>
                  <Link href='/dashboard/documents/review/urgent'>
                    Review Now
                  </Link>
                </Button>
              </div>
            </div>

            {/* Medium Priority */}
            <div className='flex items-center justify-between rounded-lg border border-orange-200 bg-orange-50 p-3'>
              <div className='flex items-center space-x-3'>
                <Icons.clock className='h-5 w-5 text-orange-500' />
                <div>
                  <div className='flex items-center gap-2'>
                    <p className='font-medium'>
                      Sarah Johnson - Personal Statement
                    </p>
                    <Badge
                      variant='secondary'
                      className='bg-orange-100 text-xs'
                    >
                      HIGH
                    </Badge>
                  </div>
                  <p className='text-muted-foreground text-sm'>
                    Submitted 2 hours ago • 2nd revision
                  </p>
                </div>
              </div>
              <div className='flex items-center space-x-2'>
                <Badge variant='secondary'>Essay</Badge>
                <Button size='sm' asChild>
                  <Link href='/dashboard/documents/review/1'>Review</Link>
                </Button>
              </div>
            </div>

            {/* Normal Priority */}
            <div className='flex items-center justify-between rounded-lg border p-3'>
              <div className='flex items-center space-x-3'>
                <Icons.fileText className='h-5 w-5 text-blue-500' />
                <div>
                  <div className='flex items-center gap-2'>
                    <p className='font-medium'>Michael Chen - Activity List</p>
                    <Badge variant='outline' className='text-xs'>
                      NORMAL
                    </Badge>
                  </div>
                  <p className='text-muted-foreground text-sm'>
                    Submitted 1 day ago • First submission
                  </p>
                </div>
              </div>
              <div className='flex items-center space-x-2'>
                <Badge variant='secondary'>Activities</Badge>
                <Button size='sm' variant='outline' asChild>
                  <Link href='/dashboard/documents/review/2'>Review</Link>
                </Button>
              </div>
            </div>

            <div className='flex items-center justify-between rounded-lg border p-3'>
              <div className='flex items-center space-x-3'>
                <Icons.trophy className='h-5 w-5 text-purple-500' />
                <div>
                  <div className='flex items-center gap-2'>
                    <p className='font-medium'>
                      Emily Rodriguez - Activity Resume
                    </p>
                    <Badge variant='outline' className='text-xs'>
                      NORMAL
                    </Badge>
                  </div>
                  <p className='text-muted-foreground text-sm'>
                    Submitted 2 days ago • Needs feedback
                  </p>
                </div>
              </div>
              <div className='flex items-center space-x-2'>
                <Badge variant='secondary'>Resume</Badge>
                <Button size='sm' variant='outline' asChild>
                  <Link href='/dashboard/documents/review/3'>Review</Link>
                </Button>
              </div>
            </div>

            <div className='flex justify-center pt-2'>
              <Button variant='outline' size='sm'>
                View All Documents ({documentsToReview + 3} total)
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
