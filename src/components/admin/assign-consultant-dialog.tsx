'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';

interface StudentWithUser {
  id: string;
  users: {
    id: string;
    email: string;
    profile_data: {
      first_name?: string;
      last_name?: string;
    };
  };
  current_consultant?: {
    id: string;
    users: {
      profile_data: {
        first_name?: string;
        last_name?: string;
      };
    };
  };
}

interface Consultant {
  id: string;
  users: {
    id: string;
    email: string;
    profile_data: {
      first_name?: string;
      last_name?: string;
    };
  };
  specialties: string[];
  student_count?: number;
}

interface AssignConsultantDialogProps {
  student: StudentWithUser | null;
  isOpen: boolean;
  onClose: () => void;
  onAssignmentComplete: () => void;
}

export function AssignConsultantDialog({
  student,
  isOpen,
  onClose,
  onAssignmentComplete
}: AssignConsultantDialogProps) {
  const [consultants, setConsultants] = useState<Consultant[]>([]);
  const [selectedConsultantId, setSelectedConsultantId] = useState<string>('');
  const [roleType, setRoleType] = useState<'main_advisor' | 'essay_teacher' | 'planning_assistant' | 'subject_specialist'>('main_advisor');
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingConsultants, setIsLoadingConsultants] = useState(false);

  const fetchConsultants = async () => {
    setIsLoadingConsultants(true);
    try {
      const response = await fetch('/api/consultants');
      const result = await response.json();

      if (result.success) {
        setConsultants(result.data);
      } else {
        toast.error('Failed to load consultants');
      }
    } catch (error) {
      console.error('Error fetching consultants:', error);
      toast.error('Failed to load consultants');
    } finally {
      setIsLoadingConsultants(false);
    }
  };

  const handleAssign = async () => {
    if (!student || !selectedConsultantId) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/students/${student.id}/assignments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          consultant_id: selectedConsultantId,
          role_type: roleType,
          start_date: new Date().toISOString().split('T')[0],
          notes
        })
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Consultant assigned successfully');
        onAssignmentComplete();
        handleClose();
      } else {
        toast.error(result.message || 'Failed to assign consultant');
      }
    } catch (error) {
      console.error('Error assigning consultant:', error);
      toast.error('Failed to assign consultant');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedConsultantId('');
    setRoleType('main_advisor');
    setNotes('');
    onClose();
  };

  useEffect(() => {
    if (isOpen) {
      fetchConsultants();
    }
  }, [isOpen]);

  if (!student) return null;

  const studentName = `${student.users.profile_data.first_name || ''} ${student.users.profile_data.last_name || ''}`.trim();
  const currentConsultant = student.current_consultant;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Assign Consultant</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Student Info */}
          <div className="rounded-lg border p-3 bg-muted/50">
            <p className="font-medium">{studentName}</p>
            <p className="text-sm text-muted-foreground">{student.users.email}</p>
            {currentConsultant && (
              <div className="mt-2">
                <Badge variant="outline" className="text-xs">
                  Currently assigned to: {currentConsultant.users.profile_data.first_name} {currentConsultant.users.profile_data.last_name}
                </Badge>
              </div>
            )}
          </div>

          {/* Consultant Selection */}
          <div className="space-y-2">
            <Label htmlFor="consultant">Select Consultant</Label>
            {isLoadingConsultants ? (
              <div className="flex items-center justify-center py-4">
                <Icons.spinner className="h-4 w-4 animate-spin" />
                <span className="ml-2 text-sm">Loading consultants...</span>
              </div>
            ) : (
              <Select value={selectedConsultantId} onValueChange={setSelectedConsultantId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a consultant" />
                </SelectTrigger>
                <SelectContent>
                  {consultants.map((consultant) => {
                    const consultantName = `${consultant.users.profile_data.first_name || ''} ${consultant.users.profile_data.last_name || ''}`.trim();
                    return (
                      <SelectItem key={consultant.id} value={consultant.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{consultantName}</span>
                          {consultant.specialties.length > 0 && (
                            <span className="text-xs text-muted-foreground ml-2">
                              {consultant.specialties.slice(0, 2).join(', ')}
                            </span>
                          )}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            )}
          </div>

          {/* Role Type Selection */}
          <div className="space-y-2">
            <Label htmlFor="role_type">Assignment Role</Label>
            <Select value={roleType} onValueChange={(value: 'main_advisor' | 'essay_teacher' | 'planning_assistant' | 'subject_specialist') => setRoleType(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="main_advisor">Main Advisor</SelectItem>
                <SelectItem value="essay_teacher">Essay Teacher</SelectItem>
                <SelectItem value="planning_assistant">Planning Assistant</SelectItem>
                <SelectItem value="subject_specialist">Subject Specialist</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {roleType === 'main_advisor' && 'Primary advisor responsible for overall guidance (only one per student)'}
              {roleType === 'essay_teacher' && 'Specialized in essay writing and review'}
              {roleType === 'planning_assistant' && 'Helps with application planning and deadlines'}
              {roleType === 'subject_specialist' && 'Expert in specific academic subjects or areas'}
            </p>
          </div>

          {/* Selected Consultant Info */}
          {selectedConsultantId && (
            <div className="rounded-lg border p-3 bg-blue-50">
              {(() => {
                const selectedConsultant = consultants.find(c => c.id === selectedConsultantId);
                if (!selectedConsultant) return null;
                
                const consultantName = `${selectedConsultant.users.profile_data.first_name || ''} ${selectedConsultant.users.profile_data.last_name || ''}`.trim();
                return (
                  <div>
                    <p className="font-medium text-blue-900">{consultantName}</p>
                    <p className="text-sm text-blue-700">{selectedConsultant.users.email}</p>
                    {selectedConsultant.specialties.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {selectedConsultant.specialties.map((specialty, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {specialty}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Add any notes about this assignment..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button
              onClick={handleAssign}
              disabled={!selectedConsultantId || isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  Assigning...
                </>
              ) : (
                <>
                  <Icons.userCheck className="mr-2 h-4 w-4" />
                  Assign Consultant
                </>
              )}
            </Button>
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
