import { render, screen } from '@testing-library/react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  CardAction,
} from '../card';

describe('Card Components', () => {
  describe('Card', () => {
    it('renders with default props', () => {
      render(<Card data-testid="card">Card content</Card>);
      
      const card = screen.getByTestId('card');
      expect(card).toBeInTheDocument();
      expect(card).toHaveAttribute('data-slot', 'card');
      expect(card).toHaveClass('bg-card', 'text-card-foreground', 'rounded-xl', 'border', 'shadow-sm');
    });

    it('applies custom className', () => {
      render(<Card className="custom-class" data-testid="card">Content</Card>);
      
      const card = screen.getByTestId('card');
      expect(card).toHaveClass('custom-class');
    });

    it('forwards props correctly', () => {
      render(<Card id="test-card" data-testid="card">Content</Card>);
      
      const card = screen.getByTestId('card');
      expect(card).toHaveAttribute('id', 'test-card');
    });
  });

  describe('CardHeader', () => {
    it('renders with correct classes', () => {
      render(<CardHeader data-testid="card-header">Header content</CardHeader>);
      
      const header = screen.getByTestId('card-header');
      expect(header).toBeInTheDocument();
      expect(header).toHaveAttribute('data-slot', 'card-header');
      expect(header).toHaveClass('grid', 'auto-rows-min', 'px-6');
    });

    it('applies custom className', () => {
      render(<CardHeader className="custom-header" data-testid="card-header">Content</CardHeader>);
      
      const header = screen.getByTestId('card-header');
      expect(header).toHaveClass('custom-header');
    });
  });

  describe('CardTitle', () => {
    it('renders with correct classes', () => {
      render(<CardTitle data-testid="card-title">Title text</CardTitle>);
      
      const title = screen.getByTestId('card-title');
      expect(title).toBeInTheDocument();
      expect(title).toHaveAttribute('data-slot', 'card-title');
      expect(title).toHaveClass('leading-none', 'font-semibold');
      expect(title).toHaveTextContent('Title text');
    });
  });

  describe('CardDescription', () => {
    it('renders with correct classes', () => {
      render(<CardDescription data-testid="card-description">Description text</CardDescription>);
      
      const description = screen.getByTestId('card-description');
      expect(description).toBeInTheDocument();
      expect(description).toHaveAttribute('data-slot', 'card-description');
      expect(description).toHaveClass('text-muted-foreground', 'text-sm');
      expect(description).toHaveTextContent('Description text');
    });
  });

  describe('CardContent', () => {
    it('renders with correct classes', () => {
      render(<CardContent data-testid="card-content">Content text</CardContent>);
      
      const content = screen.getByTestId('card-content');
      expect(content).toBeInTheDocument();
      expect(content).toHaveAttribute('data-slot', 'card-content');
      expect(content).toHaveClass('px-6');
      expect(content).toHaveTextContent('Content text');
    });
  });

  describe('CardFooter', () => {
    it('renders with correct classes', () => {
      render(<CardFooter data-testid="card-footer">Footer content</CardFooter>);
      
      const footer = screen.getByTestId('card-footer');
      expect(footer).toBeInTheDocument();
      expect(footer).toHaveAttribute('data-slot', 'card-footer');
      expect(footer).toHaveClass('flex', 'items-center', 'px-6');
      expect(footer).toHaveTextContent('Footer content');
    });
  });

  describe('CardAction', () => {
    it('renders with correct classes', () => {
      render(<CardAction data-testid="card-action">Action content</CardAction>);
      
      const action = screen.getByTestId('card-action');
      expect(action).toBeInTheDocument();
      expect(action).toHaveAttribute('data-slot', 'card-action');
      expect(action).toHaveClass('col-start-2', 'row-span-2', 'justify-self-end');
      expect(action).toHaveTextContent('Action content');
    });
  });

  describe('Complete Card Structure', () => {
    it('renders a complete card with all components', () => {
      render(
        <Card data-testid="complete-card">
          <CardHeader>
            <CardTitle>Test Title</CardTitle>
            <CardDescription>Test Description</CardDescription>
            <CardAction>Action Button</CardAction>
          </CardHeader>
          <CardContent>
            <p>This is the main content of the card.</p>
          </CardContent>
          <CardFooter>
            <button>Footer Button</button>
          </CardFooter>
        </Card>
      );

      // Check that all parts are rendered
      expect(screen.getByTestId('complete-card')).toBeInTheDocument();
      expect(screen.getByText('Test Title')).toBeInTheDocument();
      expect(screen.getByText('Test Description')).toBeInTheDocument();
      expect(screen.getByText('Action Button')).toBeInTheDocument();
      expect(screen.getByText('This is the main content of the card.')).toBeInTheDocument();
      expect(screen.getByText('Footer Button')).toBeInTheDocument();
    });

    it('works without optional components', () => {
      render(
        <Card data-testid="minimal-card">
          <CardContent>
            <p>Minimal card content</p>
          </CardContent>
        </Card>
      );

      expect(screen.getByTestId('minimal-card')).toBeInTheDocument();
      expect(screen.getByText('Minimal card content')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('maintains semantic structure', () => {
      render(
        <Card>
          <CardHeader>
            <CardTitle>Accessible Title</CardTitle>
            <CardDescription>Accessible Description</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Accessible content</p>
          </CardContent>
        </Card>
      );

      // Check that content is accessible
      expect(screen.getByText('Accessible Title')).toBeInTheDocument();
      expect(screen.getByText('Accessible Description')).toBeInTheDocument();
      expect(screen.getByText('Accessible content')).toBeInTheDocument();
    });

    it('supports custom attributes for accessibility', () => {
      render(
        <Card role="article" aria-labelledby="card-title" data-testid="accessible-card">
          <CardTitle id="card-title">Article Title</CardTitle>
          <CardContent>Article content</CardContent>
        </Card>
      );

      const card = screen.getByTestId('accessible-card');
      expect(card).toHaveAttribute('role', 'article');
      expect(card).toHaveAttribute('aria-labelledby', 'card-title');
      
      const title = screen.getByText('Article Title');
      expect(title).toHaveAttribute('id', 'card-title');
    });
  });
});
