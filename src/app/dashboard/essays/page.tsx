'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Icons } from '@/components/icons';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useApiGet } from '@/lib/api-client';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Essay {
  id: string;
  type: 'main' | 'supplemental';
  title: string;
  schools: string[];
  school_id?: string;
  source_essay_id?: string;
  word_limit: number;
  word_count?: number;
  status: 'draft' | 'in_review' | 'completed';
  google_doc_id?: string;
  google_doc_url?: string;
  created_at: string;
  updated_at: string;
  student_id: string;
}

export default function EssaysPage() {
  const { user } = useCurrentUser();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('all');

  // Get all essays
  const {
    data: allEssays,
    loading,
    error,
    fetch: fetchEssays
  } = useApiGet<Essay[]>('/api/essays');

  useEffect(() => {
    fetchEssays();
  }, [fetchEssays]);

  if (loading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>Essays</h2>
            <p className='text-muted-foreground'>Loading your essays...</p>
          </div>
        </div>
        <div className='grid gap-4'>
          {[1, 2, 3].map((i) => (
            <Card key={i} className='animate-pulse'>
              <CardHeader>
                <div className='h-4 bg-muted rounded w-3/4'></div>
                <div className='h-3 bg-muted rounded w-1/2'></div>
              </CardHeader>
              <CardContent>
                <div className='h-2 bg-muted rounded w-full'></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>Essays</h2>
            <p className='text-muted-foreground text-red-600'>Error loading essays: {error}</p>
          </div>
        </div>
      </div>
    );
  }

  const essays = allEssays || [];
  // Filter essays based on search and filters
  const filteredEssays = essays.filter(essay => {
    const matchesSearch = essay.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || essay.status === statusFilter;
    const matchesTab = activeTab === 'all' || essay.type === activeTab;
    return matchesSearch && matchesStatus && matchesTab;
  });

  // Separate main and supplemental essays
  const mainEssays = essays.filter(essay => essay.type === 'main');
  const supplementalEssays = essays.filter(essay => essay.type === 'supplemental');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_review':
        return 'bg-blue-100 text-blue-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'in_review':
        return 'In Review';
      case 'draft':
        return 'Draft';
      default:
        return 'Draft';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    if (diffInHours < 48) return '1 day ago';
    return `${Math.floor(diffInHours / 24)} days ago`;
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Essays</h2>
          <p className='text-muted-foreground'>
            Manage your personal statement and supplemental essays
          </p>
        </div>
        <Button asChild>
          <Link href='/dashboard/essays/new'>
            <Icons.add className='mr-2 h-4 w-4' />
            New Essay
          </Link>
        </Button>
      </div>

      {/* Overview Stats */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Essays</CardTitle>
            <Icons.fileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{essays.length}</div>
            <p className='text-muted-foreground text-xs'>
              {essays.filter((e) => e.status === 'completed').length} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Main Essays</CardTitle>
            <Icons.fileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{mainEssays.length}</div>
            <p className='text-muted-foreground text-xs'>Reusable across schools</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Supplemental Essays</CardTitle>
            <Icons.fileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{supplementalEssays.length}</div>
            <p className='text-muted-foreground text-xs'>School-specific</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Word Count</CardTitle>
            <Icons.fileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {essays.reduce((sum, essay) => sum + (essay.word_count || 0), 0)}
            </div>
            <p className='text-muted-foreground text-xs'>Total words written</p>
          </CardContent>
        </Card>

      </div>

      {/* Filters and Search */}
      <div className='flex flex-col gap-4 md:flex-row md:items-center md:justify-between'>
        <div className='flex flex-1 items-center space-x-2'>
          <Input
            placeholder='Search essays...'
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className='max-w-sm'
          />
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className='w-[180px]'>
              <SelectValue placeholder='Filter by status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Status</SelectItem>
              <SelectItem value='draft'>Draft</SelectItem>
              <SelectItem value='in_review'>In Review</SelectItem>
              <SelectItem value='completed'>Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Tabs for Main vs Supplemental Essays */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
        <TabsList className='grid w-full grid-cols-3'>
          <TabsTrigger value='all'>All Essays ({essays.length})</TabsTrigger>
          <TabsTrigger value='main'>Main Essays ({mainEssays.length})</TabsTrigger>
          <TabsTrigger value='supplemental'>Supplemental ({supplementalEssays.length})</TabsTrigger>
        </TabsList>

        <TabsContent value='all' className='space-y-4'>
          {filteredEssays.length === 0 ? (
            <Card>
              <CardContent className='flex flex-col items-center justify-center py-12'>
                <Icons.fileText className='text-muted-foreground mb-4 h-12 w-12' />
                <h3 className='mb-2 text-lg font-semibold'>No essays found</h3>
                <p className='text-muted-foreground mb-4 text-center'>
                  {searchTerm || statusFilter !== 'all'
                    ? 'Try adjusting your search or filters'
                    : 'Get started by creating your first essay'}
                </p>
                <Button asChild>
                  <Link href='/dashboard/essays/new'>
                    <Icons.plus className='mr-2 h-4 w-4' />
                    Create Essay
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className='space-y-4'>
              {filteredEssays.map((essay) => (
                <Card key={essay.id} className='transition-shadow hover:shadow-md'>
                  <CardHeader>
                    <div className='flex items-center justify-between'>
                      <div className='space-y-1'>
                        <div className='flex items-center space-x-2'>
                          <CardTitle className='text-lg'>{essay.title}</CardTitle>
                          <Badge variant={essay.type === 'main' ? 'default' : 'secondary'}>
                            {essay.type === 'main' ? '主文书' : '补充文书'}
                          </Badge>
                          {essay.source_essay_id && (
                            <Badge variant='outline' className='text-xs'>
                              <Icons.copy className='mr-1 h-3 w-3' />
                              Copied
                            </Badge>
                          )}
                        </div>
                        <CardDescription>
                          For: {essay.schools.join(', ')} • Last modified: {formatDate(essay.updated_at)}
                        </CardDescription>
                      </div>
                      <div className='flex items-center space-x-2'>
                        <Badge className={getStatusColor(essay.status)}>
                          {getStatusText(essay.status)}
                        </Badge>
                        {essay.google_doc_url ? (
                          <Button asChild size='sm'>
                            <a href={essay.google_doc_url} target='_blank' rel='noopener noreferrer'>
                              <Icons.externalLink className='mr-2 h-4 w-4' />
                              Edit in Google Docs
                            </a>
                          </Button>
                        ) : (
                          <Button asChild size='sm'>
                            <Link href={`/dashboard/essays/${essay.id}`}>
                              Edit
                            </Link>
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center space-x-4'>
                        <div className='flex items-center space-x-2'>
                          <Icons.fileText className='text-muted-foreground h-4 w-4' />
                          <span className='text-muted-foreground text-sm'>
                            {essay.word_count || 0} / {essay.word_limit} words
                          </span>
                        </div>
                        <div className='flex items-center space-x-2'>
                          <Icons.building className='text-muted-foreground h-4 w-4' />
                          <span className='text-muted-foreground text-sm'>
                            {essay.schools.length} school{essay.schools.length !== 1 ? 's' : ''}
                          </span>
                        </div>
                      </div>
                      <div className='flex items-center space-x-2'>
                        <div className='h-2 w-32 rounded-full bg-gray-200'>
                          <div
                            className='h-2 rounded-full bg-blue-600'
                            style={{
                              width: `${Math.min(((essay.word_count || 0) / essay.word_limit) * 100, 100)}%`
                            }}
                          />
                        </div>
                        <span className='text-muted-foreground text-xs'>
                          {Math.round(((essay.word_count || 0) / essay.word_limit) * 100)}%
                        </span>
                        {essay.type === 'main' && (
                          <Button
                            size='sm'
                            variant='outline'
                            onClick={() => {
                              // TODO: Implement copy functionality
                              console.log('Copy essay:', essay.id);
                            }}
                          >
                            <Icons.copy className='mr-2 h-4 w-4' />
                            Copy for School
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value='main' className='space-y-4'>
          {mainEssays.filter(essay => {
            const matchesSearch = essay.title.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesStatus = statusFilter === 'all' || essay.status === statusFilter;
            return matchesSearch && matchesStatus;
          }).length === 0 ? (
            <Card>
              <CardContent className='flex flex-col items-center justify-center py-12'>
                <Icons.fileText className='text-muted-foreground mb-4 h-12 w-12' />
                <h3 className='mb-2 text-lg font-semibold'>No main essays found</h3>
                <p className='text-muted-foreground mb-4 text-center'>
                  Main essays are reusable across multiple schools
                </p>
                <Button asChild>
                  <Link href='/dashboard/essays/new?type=main'>
                    <Icons.plus className='mr-2 h-4 w-4' />
                    Create Main Essay
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className='space-y-4'>
              {/* Main essays content would be rendered here */}
            </div>
          )}
        </TabsContent>

        <TabsContent value='supplemental' className='space-y-4'>
          {supplementalEssays.filter(essay => {
            const matchesSearch = essay.title.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesStatus = statusFilter === 'all' || essay.status === statusFilter;
            return matchesSearch && matchesStatus;
          }).length === 0 ? (
            <Card>
              <CardContent className='flex flex-col items-center justify-center py-12'>
                <Icons.fileText className='text-muted-foreground mb-4 h-12 w-12' />
                <h3 className='mb-2 text-lg font-semibold'>No supplemental essays found</h3>
                <p className='text-muted-foreground mb-4 text-center'>
                  Supplemental essays are school-specific
                </p>
                <Button asChild>
                  <Link href='/dashboard/essays/new?type=supplemental'>
                    <Icons.plus className='mr-2 h-4 w-4' />
                    Create Supplemental Essay
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className='space-y-4'>
              {/* Supplemental essays content would be rendered here */}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Manage your essay portfolio efficiently</CardDescription>
        </CardHeader>
        <CardContent className='flex flex-wrap gap-2'>
          <Button asChild variant='outline'>
            <Link href='/dashboard/essays/new?type=main'>
              <Icons.fileText className='mr-2 h-4 w-4' />
              Create Main Essay
            </Link>
          </Button>
          <Button asChild variant='outline'>
            <Link href='/dashboard/essays/new?type=supplemental'>
              <Icons.plus className='mr-2 h-4 w-4' />
              Create Supplemental Essay
            </Link>
          </Button>
          <Button asChild variant='outline'>
            <Link href='/dashboard/essays/templates'>
              <Icons.files className='mr-2 h-4 w-4' />
              Browse Templates
            </Link>
          </Button>
          <Button asChild variant='outline'>
            <Link href='/dashboard/essays/tips'>
              <Icons.help className='mr-2 h-4 w-4' />
              Writing Tips
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
