'use client';

import { useState, useEffect } from 'react';
import { useCurrentUser } from '@/hooks/use-current-user';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Icons } from '@/components/icons';
import Link from 'next/link';

interface PersonalStatementDocument {
  id: string;
  metadata: {
    title: string;
    status: 'draft' | 'in_review' | 'completed';
    word_limit?: number;
    deadline?: string;
    google_doc_url?: string;
  };
  created_at: string;
  updated_at: string;
}

export default function PersonalStatementPage() {
  const { user } = useCurrentUser();
  const [documents, setDocuments] = useState<PersonalStatementDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchPersonalStatements();
  }, []);

  const fetchPersonalStatements = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        '/api/documents?doc_type=personal_statement'
      );
      const result = await response.json();

      if (response.ok) {
        setDocuments(result.data || []);
      } else {
        setError(result.error || 'Failed to fetch personal statements');
      }
    } catch (err) {
      setError('Failed to fetch personal statements');
      console.error('Error fetching personal statements:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <Icons.check className='h-4 w-4' />;
      case 'in_review':
        return <Icons.clock className='h-4 w-4' />;
      case 'draft':
      default:
        return <Icons.edit className='h-4 w-4' />;
    }
  };

  const calculateProgress = () => {
    if (documents.length === 0) return 0;
    const completed = documents.filter(
      (doc) => doc.metadata.status === 'completed'
    ).length;
    return Math.round((completed / documents.length) * 100);
  };

  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* Header */}
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>
              Personal Statement
            </h2>
            <p className='text-muted-foreground'>
              Manage your personal statement essays for college applications
            </p>
          </div>
          <Button asChild>
            <Link href='/dashboard/documents/new?type=personal_statement'>
              <Icons.plus className='mr-2 h-4 w-4' />
              New Personal Statement
            </Link>
          </Button>
        </div>

        {/* Progress Overview */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.target className='h-5 w-5' />
              Progress Overview
            </CardTitle>
            <CardDescription>
              Track your personal statement completion progress
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <span className='text-sm font-medium'>Overall Progress</span>
                <span className='text-muted-foreground text-sm'>
                  {
                    documents.filter((d) => d.metadata.status === 'completed')
                      .length
                  }{' '}
                  of {documents.length} completed
                </span>
              </div>
              <Progress value={calculateProgress()} className='h-2' />
            </div>
          </CardContent>
        </Card>

        {/* Personal Statements List */}
        <Card>
          <CardHeader>
            <CardTitle>Your Personal Statements</CardTitle>
            <CardDescription>
              All your personal statement essays in one place
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className='flex items-center justify-center py-8'>
                <div className='border-primary h-8 w-8 animate-spin rounded-full border-b-2'></div>
              </div>
            ) : error ? (
              <div className='py-8 text-center'>
                <p className='text-muted-foreground mb-4'>{error}</p>
                <Button onClick={fetchPersonalStatements} variant='outline'>
                  Try Again
                </Button>
              </div>
            ) : documents.length === 0 ? (
              <div className='py-8 text-center'>
                <Icons.fileText className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
                <h3 className='mb-2 text-lg font-medium'>
                  No Personal Statements Yet
                </h3>
                <p className='text-muted-foreground mb-4'>
                  Get started by creating your first personal statement
                </p>
                <Button asChild>
                  <Link href='/dashboard/documents/new?type=personal_statement'>
                    <Icons.plus className='mr-2 h-4 w-4' />
                    Create Personal Statement
                  </Link>
                </Button>
              </div>
            ) : (
              <div className='space-y-4'>
                {documents.map((doc) => (
                  <div
                    key={doc.id}
                    className='hover:bg-muted/50 flex items-center justify-between rounded-lg border p-4 transition-colors'
                  >
                    <div className='flex items-center space-x-4'>
                      <div className='flex-shrink-0'>
                        {getStatusIcon(doc.metadata.status)}
                      </div>
                      <div className='min-w-0 flex-1'>
                        <h3 className='truncate text-sm font-medium'>
                          {doc.metadata.title}
                        </h3>
                        <div className='mt-1 flex items-center space-x-2'>
                          <Badge
                            className={getStatusColor(doc.metadata.status)}
                          >
                            {doc.metadata.status.replace('_', ' ')}
                          </Badge>
                          {doc.metadata.word_limit && (
                            <span className='text-muted-foreground text-xs'>
                              Limit: {doc.metadata.word_limit} words
                            </span>
                          )}
                          {doc.metadata.deadline && (
                            <span className='text-muted-foreground text-xs'>
                              Due:{' '}
                              {new Date(
                                doc.metadata.deadline
                              ).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className='flex items-center space-x-2'>
                      {doc.metadata.google_doc_url && (
                        <Button size='sm' variant='outline' asChild>
                          <a
                            href={doc.metadata.google_doc_url}
                            target='_blank'
                            rel='noopener noreferrer'
                          >
                            <Icons.externalLink className='h-4 w-4' />
                          </a>
                        </Button>
                      )}
                      <Button size='sm' asChild>
                        <Link href={`/dashboard/documents/${doc.id}`}>
                          View
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Tips */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.lightbulb className='h-5 w-5' />
              Personal Statement Tips
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className='text-muted-foreground space-y-2 text-sm'>
              <li>• Start with a compelling hook that draws the reader in</li>
              <li>
                • Show, don&apos;t tell - use specific examples and stories
              </li>
              <li>
                • Focus on growth and what you&apos;ve learned from experiences
              </li>
              <li>• Keep it authentic and in your own voice</li>
              <li>• Stay within the word limit and proofread carefully</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </ProfileSetupCheck>
  );
}
