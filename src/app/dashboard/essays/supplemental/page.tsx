'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Icons } from '@/components/icons';
import { Progress } from '@/components/ui/progress';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useApiGet } from '@/lib/api-client';
import { Skeleton } from '@/components/ui/skeleton';

interface SupplementalEssay {
  id: string;
  school: string;
  prompt: string;
  wordLimit: number;
  currentWords: number;
  status: 'not_started' | 'draft' | 'in_review' | 'completed';
  deadline: string;
  priority: 'high' | 'medium' | 'low';
}

interface Document {
  id: string;
  student_id: string;
  google_doc_id?: string;
  doc_type: 'essay' | 'personal_statement' | 'transcript' | 'activity_resume' | 'other';
  metadata: {
    title?: string;
    school_id?: string;
    word_count?: number;
    word_limit?: number;
    status?: 'draft' | 'in_review' | 'completed';
    deadline?: string;
    priority?: 'high' | 'medium' | 'low';
    google_doc_url?: string;
    folder_id?: string;
  };
  created_at: string;
  updated_at: string;
}

interface StudentProfile {
  id: string;
  user_id: string;
  academic_info?: any;
  created_at: string;
  updated_at: string;
}

const statusColors = {
  not_started: 'bg-gray-100 text-gray-800',
  draft: 'bg-yellow-100 text-yellow-800',
  in_review: 'bg-blue-100 text-blue-800',
  completed: 'bg-green-100 text-green-800'
};

const priorityColors = {
  high: 'bg-red-100 text-red-800',
  medium: 'bg-yellow-100 text-yellow-800',
  low: 'bg-green-100 text-green-800'
};

export default function SupplementalEssaysPage() {
  const { user } = useCurrentUser();
  const [essays, setEssays] = useState<SupplementalEssay[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [studentId, setStudentId] = useState<string | null>(null);

  // Get student profile ID
  const { data: studentProfile, loading: profileLoading } = useApiGet<StudentProfile>('/api/profile/student');

  // Get documents (essays) for the student
  const {
    data: documents,
    loading: documentsLoading,
    error: documentsError,
    fetch: fetchDocuments
  } = useApiGet<Document[]>(
    studentId ? `/api/documents?student_id=${studentId}&doc_type=essay` : ''
  );

  // Set student ID when profile is loaded
  useEffect(() => {
    if (studentProfile?.id) {
      setStudentId(studentProfile.id);
    }
  }, [studentProfile]);

  // Fetch documents when student ID is available
  useEffect(() => {
    if (studentId) {
      fetchDocuments();
    }
  }, [studentId, fetchDocuments]);

  // Transform documents to SupplementalEssay format
  useEffect(() => {
    if (documents) {
      const transformedEssays: SupplementalEssay[] = documents
        .filter(doc => doc.doc_type === 'essay') // Only supplemental essays, not personal statements
        .map(doc => ({
          id: doc.id,
          school: doc.metadata.title || 'Untitled Essay',
          prompt: 'Essay prompt', // We'll need to get this from school requirements
          wordLimit: doc.metadata.word_limit || 500,
          currentWords: doc.metadata.word_count || 0,
          status: mapDocumentStatus(doc.metadata.status),
          deadline: doc.metadata.deadline || '2025-01-01',
          priority: doc.metadata.priority || 'medium'
        }));
      setEssays(transformedEssays);
    }
  }, [documents]);

  // Helper function to map document status to essay status
  const mapDocumentStatus = (status?: string): 'not_started' | 'draft' | 'in_review' | 'completed' => {
    switch (status) {
      case 'completed':
        return 'completed';
      case 'in_review':
        return 'in_review';
      case 'draft':
        return 'draft';
      default:
        return 'not_started';
    }
  };

  const filteredEssays = essays.filter((essay) => {
    const matchesSearch =
      essay.school.toLowerCase().includes(searchTerm.toLowerCase()) ||
      essay.prompt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' || essay.status === statusFilter;
    const matchesPriority =
      priorityFilter === 'all' || essay.priority === priorityFilter;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  const getStatusStats = () => {
    const stats = {
      total: essays.length,
      not_started: essays.filter((e) => e.status === 'not_started').length,
      draft: essays.filter((e) => e.status === 'draft').length,
      in_review: essays.filter((e) => e.status === 'in_review').length,
      completed: essays.filter((e) => e.status === 'completed').length
    };
    return stats;
  };

  const stats = getStatusStats();
  const completionRate = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;

  // Loading state
  if (profileLoading || documentsLoading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>
              Supplemental Essays
            </h2>
            <p className='text-muted-foreground'>
              Manage your school-specific supplemental essays and prompts
            </p>
          </div>
          <Button disabled>
            <Icons.plus className='mr-2 h-4 w-4' />
            Add Essay
          </Button>
        </div>

        {/* Loading skeleton */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className='pb-2'>
                <Skeleton className='h-4 w-20' />
              </CardHeader>
              <CardContent>
                <Skeleton className='h-8 w-16' />
              </CardContent>
            </Card>
          ))}
        </div>
        <Skeleton className='h-[400px] w-full' />
      </div>
    );
  }

  // Error state
  if (documentsError) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>
              Supplemental Essays
            </h2>
            <p className='text-muted-foreground'>
              Manage your school-specific supplemental essays and prompts
            </p>
          </div>
        </div>

        <Card>
          <CardContent className='flex flex-col items-center justify-center py-12'>
            <Icons.alertCircle className='text-muted-foreground mb-4 h-12 w-12' />
            <h3 className='mb-2 text-lg font-semibold'>Error loading essays</h3>
            <p className='text-muted-foreground mb-4 text-center'>
              There was an error loading your supplemental essays. Please try again.
            </p>
            <Button onClick={() => fetchDocuments()}>
              <Icons.refresh className='mr-2 h-4 w-4' />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      {/* Header */}
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>
            Supplemental Essays
          </h2>
          <p className='text-muted-foreground'>
            Manage your school-specific supplemental essays and prompts
          </p>
        </div>
        <Button>
          <Icons.plus className='mr-2 h-4 w-4' />
          Add Essay
        </Button>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Essays</CardTitle>
            <Icons.fileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.total}</div>
            <p className='text-muted-foreground text-xs'>
              Across all target schools
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Completion Rate
            </CardTitle>
            <Icons.checkCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{completionRate}%</div>
            <Progress value={completionRate} className='mt-2' />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>In Progress</CardTitle>
            <Icons.clock className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {stats.draft + stats.in_review}
            </div>
            <p className='text-muted-foreground text-xs'>
              {stats.draft} drafts, {stats.in_review} in review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Not Started</CardTitle>
            <Icons.alertCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.not_started}</div>
            <p className='text-muted-foreground text-xs'>Essays to begin</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Essays</CardTitle>
          <CardDescription>
            {filteredEssays.length} of {essays.length} essays
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex flex-col gap-4 md:flex-row md:items-center'>
            <div className='flex-1'>
              <Input
                placeholder='Search essays by school or prompt...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='max-w-sm'
              />
            </div>
            <div className='flex gap-2'>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className='w-[140px]'>
                  <SelectValue placeholder='All Status' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Status</SelectItem>
                  <SelectItem value='not_started'>Not Started</SelectItem>
                  <SelectItem value='draft'>Draft</SelectItem>
                  <SelectItem value='in_review'>In Review</SelectItem>
                  <SelectItem value='completed'>Completed</SelectItem>
                </SelectContent>
              </Select>

              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className='w-[140px]'>
                  <SelectValue placeholder='All Priority' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Priority</SelectItem>
                  <SelectItem value='high'>High</SelectItem>
                  <SelectItem value='medium'>Medium</SelectItem>
                  <SelectItem value='low'>Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Essays List */}
      <div className='space-y-4'>
        {filteredEssays.length === 0 ? (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-12'>
              <Icons.fileText className='text-muted-foreground mb-4 h-12 w-12' />
              <h3 className='mb-2 text-lg font-semibold'>No essays found</h3>
              <p className='text-muted-foreground mb-4 text-center'>
                {searchTerm ||
                statusFilter !== 'all' ||
                priorityFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Get started by adding your first supplemental essay'}
              </p>
              <Button>
                <Icons.plus className='mr-2 h-4 w-4' />
                Add Essay
              </Button>
            </CardContent>
          </Card>
        ) : (
          filteredEssays.map((essay) => (
            <Card key={essay.id} className='transition-shadow hover:shadow-md'>
              <CardContent className='p-6'>
                <div className='flex items-start justify-between'>
                  <div className='flex-1'>
                    <div className='mb-2 flex items-center gap-2'>
                      <h3 className='text-lg font-semibold'>{essay.school}</h3>
                      <Badge className={priorityColors[essay.priority]}>
                        {essay.priority}
                      </Badge>
                      <Badge className={statusColors[essay.status]}>
                        {essay.status.replace('_', ' ')}
                      </Badge>
                    </div>

                    <p className='text-muted-foreground mb-4 line-clamp-2'>
                      {essay.prompt}
                    </p>

                    <div className='text-muted-foreground flex items-center gap-4 text-sm'>
                      <div className='flex items-center gap-1'>
                        <Icons.fileText className='h-4 w-4' />
                        {essay.currentWords}/{essay.wordLimit} words
                      </div>
                      <div className='flex items-center gap-1'>
                        <Icons.calendar className='h-4 w-4' />
                        Due {new Date(essay.deadline).toLocaleDateString()}
                      </div>
                    </div>

                    {essay.currentWords > 0 && (
                      <div className='mt-3'>
                        <div className='mb-1 flex items-center justify-between text-sm'>
                          <span>Progress</span>
                          <span>
                            {Math.round(
                              (essay.currentWords / essay.wordLimit) * 100
                            )}
                            %
                          </span>
                        </div>
                        <Progress
                          value={(essay.currentWords / essay.wordLimit) * 100}
                          className='h-2'
                        />
                      </div>
                    )}
                  </div>

                  <div className='ml-4 flex gap-2'>
                    <Button variant='outline' size='sm'>
                      <Icons.edit className='h-4 w-4' />
                    </Button>
                    <Button variant='outline' size='sm'>
                      <Icons.externalLink className='h-4 w-4' />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
