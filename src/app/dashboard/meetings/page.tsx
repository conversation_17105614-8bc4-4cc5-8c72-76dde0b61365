'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
// import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Icons } from '@/components/icons';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Calendar } from '@/components/ui/calendar';

interface Meeting {
  id: string;
  title: string;
  type: 'consultation' | 'review' | 'planning' | 'check_in';
  student: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  startTime: string;
  endTime: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  location: 'zoom' | 'in_person' | 'phone';
  agenda?: string;
  notes?: string;
  meetingUrl?: string;
}

const mockMeetings: Meeting[] = [
  {
    id: '1',
    title: 'College Application Strategy Session',
    type: 'consultation',
    student: {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>'
    },
    startTime: '2025-07-05T14:00:00Z',
    endTime: '2025-07-05T15:00:00Z',
    status: 'scheduled',
    location: 'zoom',
    agenda: 'Discuss target schools, application timeline, and essay topics',
    meetingUrl: 'https://zoom.us/j/123456789'
  },
  {
    id: '2',
    title: 'Essay Review - Personal Statement',
    type: 'review',
    student: {
      id: '2',
      name: 'Michael Chen',
      email: '<EMAIL>'
    },
    startTime: '2025-07-04T16:00:00Z',
    endTime: '2025-07-04T16:30:00Z',
    status: 'completed',
    location: 'zoom',
    agenda: 'Review first draft of personal statement',
    notes:
      'Great progress on the opening paragraph. Need to work on conclusion.',
    meetingUrl: 'https://zoom.us/j/987654321'
  },
  {
    id: '3',
    title: 'Weekly Check-in',
    type: 'check_in',
    student: {
      id: '3',
      name: 'Emily Rodriguez',
      email: '<EMAIL>'
    },
    startTime: '2025-07-06T10:00:00Z',
    endTime: '2025-07-06T10:30:00Z',
    status: 'scheduled',
    location: 'phone',
    agenda: 'Progress update on application materials'
  }
];

const statusColors = {
  scheduled: 'bg-blue-100 text-blue-800',
  completed: 'bg-green-100 text-green-800',
  cancelled: 'bg-red-100 text-red-800',
  no_show: 'bg-orange-100 text-orange-800'
};

const typeColors = {
  consultation: 'bg-purple-100 text-purple-800',
  review: 'bg-blue-100 text-blue-800',
  planning: 'bg-green-100 text-green-800',
  check_in: 'bg-yellow-100 text-yellow-800'
};

const locationIcons = {
  zoom: Icons.video,
  in_person: Icons.mapPin,
  phone: Icons.phone
};

export default function MeetingsPage() {
  const [meetings, setMeetings] = useState<Meeting[]>(mockMeetings);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    new Date()
  );
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list');

  const filteredMeetings = meetings.filter((meeting) => {
    const matchesSearch =
      meeting.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      meeting.student.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' || meeting.status === statusFilter;
    const matchesType = typeFilter === 'all' || meeting.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  const getStats = () => {
    const today = new Date();
    const todayMeetings = meetings.filter((m) => {
      const meetingDate = new Date(m.startTime);
      return meetingDate.toDateString() === today.toDateString();
    });

    const thisWeekMeetings = meetings.filter((m) => {
      const meetingDate = new Date(m.startTime);
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - today.getDay());
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      return meetingDate >= weekStart && meetingDate <= weekEnd;
    });

    return {
      total: meetings.length,
      today: todayMeetings.length,
      thisWeek: thisWeekMeetings.length,
      scheduled: meetings.filter((m) => m.status === 'scheduled').length,
      completed: meetings.filter((m) => m.status === 'completed').length
    };
  };

  const stats = getStats();

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const isToday = (dateString: string) => {
    const meetingDate = new Date(dateString);
    const today = new Date();
    return meetingDate.toDateString() === today.toDateString();
  };

  const isTomorrow = (dateString: string) => {
    const meetingDate = new Date(dateString);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return meetingDate.toDateString() === tomorrow.toDateString();
  };

  const getRelativeDate = (dateString: string) => {
    if (isToday(dateString)) return 'Today';
    if (isTomorrow(dateString)) return 'Tomorrow';
    return formatDate(dateString);
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Meetings</h1>
          <p className='text-muted-foreground'>
            Schedule and manage meetings with your students
          </p>
        </div>
        <div className='flex gap-2'>
          <Button
            variant='outline'
            onClick={() =>
              setViewMode(viewMode === 'list' ? 'calendar' : 'list')
            }
          >
            {viewMode === 'list' ? (
              <Icons.calendar className='mr-2 h-4 w-4' />
            ) : (
              <Icons.list className='mr-2 h-4 w-4' />
            )}
            {viewMode === 'list' ? 'Calendar View' : 'List View'}
          </Button>
          <Button>
            <Icons.plus className='mr-2 h-4 w-4' />
            Schedule Meeting
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-5'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Meetings
            </CardTitle>
            <Icons.calendar className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.total}</div>
            <p className='text-muted-foreground text-xs'>All time</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Today</CardTitle>
            <Icons.clock className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.today}</div>
            <p className='text-muted-foreground text-xs'>Meetings scheduled</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>This Week</CardTitle>
            <Icons.calendar className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.thisWeek}</div>
            <p className='text-muted-foreground text-xs'>Meetings scheduled</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Scheduled</CardTitle>
            <Icons.checkCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.scheduled}</div>
            <p className='text-muted-foreground text-xs'>Upcoming meetings</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Completed</CardTitle>
            <Icons.checkCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.completed}</div>
            <p className='text-muted-foreground text-xs'>Past meetings</p>
          </CardContent>
        </Card>
      </div>

      {viewMode === 'calendar' ? (
        /* Calendar View */
        <div className='grid gap-6 md:grid-cols-3'>
          <Card className='md:col-span-1'>
            <CardHeader>
              <CardTitle>Calendar</CardTitle>
            </CardHeader>
            <CardContent>
              <Calendar
                mode='single'
                selected={selectedDate}
                onSelect={setSelectedDate as any}
                className='rounded-md border'
              />
            </CardContent>
          </Card>

          <Card className='md:col-span-2'>
            <CardHeader>
              <CardTitle>
                {selectedDate
                  ? `Meetings for ${formatDate(selectedDate.toISOString())}`
                  : 'Select a date'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedDate ? (
                <div className='space-y-3'>
                  {meetings
                    .filter((meeting) => {
                      const meetingDate = new Date(meeting.startTime);
                      return (
                        meetingDate.toDateString() ===
                        selectedDate.toDateString()
                      );
                    })
                    .map((meeting) => (
                      <div
                        key={meeting.id}
                        className='flex items-center justify-between rounded-lg border p-3'
                      >
                        <div>
                          <h4 className='font-medium'>{meeting.title}</h4>
                          <p className='text-muted-foreground text-sm'>
                            {formatTime(meeting.startTime)} -{' '}
                            {formatTime(meeting.endTime)} with{' '}
                            {meeting.student.name}
                          </p>
                        </div>
                        <Badge className={statusColors[meeting.status]}>
                          {meeting.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    ))}
                  {meetings.filter((meeting) => {
                    const meetingDate = new Date(meeting.startTime);
                    return (
                      meetingDate.toDateString() === selectedDate.toDateString()
                    );
                  }).length === 0 && (
                    <p className='text-muted-foreground py-4 text-center'>
                      No meetings scheduled for this date
                    </p>
                  )}
                </div>
              ) : (
                <p className='text-muted-foreground py-4 text-center'>
                  Select a date to view meetings
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      ) : (
        /* List View */
        <>
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Meetings</CardTitle>
              <CardDescription>
                {filteredMeetings.length} of {meetings.length} meetings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='flex flex-col gap-4 md:flex-row md:items-center'>
                <div className='flex-1'>
                  <Input
                    placeholder='Search meetings by title or student name...'
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className='max-w-sm'
                  />
                </div>
                <div className='flex gap-2'>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className='w-[140px]'>
                      <SelectValue placeholder='All Status' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='all'>All Status</SelectItem>
                      <SelectItem value='scheduled'>Scheduled</SelectItem>
                      <SelectItem value='completed'>Completed</SelectItem>
                      <SelectItem value='cancelled'>Cancelled</SelectItem>
                      <SelectItem value='no_show'>No Show</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className='w-[140px]'>
                      <SelectValue placeholder='All Types' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='all'>All Types</SelectItem>
                      <SelectItem value='consultation'>Consultation</SelectItem>
                      <SelectItem value='review'>Review</SelectItem>
                      <SelectItem value='planning'>Planning</SelectItem>
                      <SelectItem value='check_in'>Check-in</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Meetings List */}
          <div className='space-y-4'>
            {filteredMeetings.length === 0 ? (
              <Card>
                <CardContent className='flex flex-col items-center justify-center py-12'>
                  <Icons.calendar className='text-muted-foreground mb-4 h-12 w-12' />
                  <h3 className='mb-2 text-lg font-semibold'>
                    No meetings found
                  </h3>
                  <p className='text-muted-foreground mb-4 text-center'>
                    {searchTerm ||
                    statusFilter !== 'all' ||
                    typeFilter !== 'all'
                      ? 'Try adjusting your search or filters'
                      : 'Schedule your first meeting with a student'}
                  </p>
                  <Button>
                    <Icons.plus className='mr-2 h-4 w-4' />
                    Schedule Meeting
                  </Button>
                </CardContent>
              </Card>
            ) : (
              filteredMeetings.map((meeting) => {
                const LocationIcon = locationIcons[meeting.location];
                return (
                  <Card
                    key={meeting.id}
                    className='transition-shadow hover:shadow-md'
                  >
                    <CardContent className='p-6'>
                      <div className='flex items-start justify-between'>
                        <div className='flex-1'>
                          <div className='mb-3 flex items-center gap-3'>
                            <Avatar className='h-8 w-8'>
                              <AvatarImage src={meeting.student.avatar} />
                              <AvatarFallback>
                                {meeting.student.name
                                  .split(' ')
                                  .map((n) => n[0])
                                  .join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <h3 className='font-semibold'>{meeting.title}</h3>
                              <p className='text-muted-foreground text-sm'>
                                with {meeting.student.name}
                              </p>
                            </div>
                          </div>

                          <div className='mb-3 flex items-center gap-2'>
                            <Badge className={statusColors[meeting.status]}>
                              {meeting.status.replace('_', ' ')}
                            </Badge>
                            <Badge className={typeColors[meeting.type]}>
                              {meeting.type.replace('_', ' ')}
                            </Badge>
                            <Badge variant='outline'>
                              <LocationIcon className='mr-1 h-3 w-3' />
                              {meeting.location}
                            </Badge>
                          </div>

                          <div className='text-muted-foreground mb-3 flex items-center gap-4 text-sm'>
                            <div className='flex items-center gap-1'>
                              <Icons.calendar className='h-4 w-4' />
                              {getRelativeDate(meeting.startTime)}
                            </div>
                            <div className='flex items-center gap-1'>
                              <Icons.clock className='h-4 w-4' />
                              {formatTime(meeting.startTime)} -{' '}
                              {formatTime(meeting.endTime)}
                            </div>
                          </div>

                          {meeting.agenda && (
                            <p className='text-muted-foreground mb-3 text-sm'>
                              <strong>Agenda:</strong> {meeting.agenda}
                            </p>
                          )}

                          {meeting.notes && (
                            <p className='text-muted-foreground text-sm'>
                              <strong>Notes:</strong> {meeting.notes}
                            </p>
                          )}
                        </div>

                        <div className='ml-4 flex gap-2'>
                          {meeting.meetingUrl &&
                            meeting.status === 'scheduled' && (
                              <Button size='sm'>
                                <Icons.video className='mr-2 h-4 w-4' />
                                Join
                              </Button>
                            )}
                          <Button variant='outline' size='sm'>
                            <Icons.edit className='h-4 w-4' />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>
        </>
      )}
    </div>
  );
}
