'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Icons } from '@/components/icons';
import { useCurrentUser } from '@/hooks/use-current-user';
import { toast } from 'sonner';

interface RegistrationRequest {
  id: string;
  email: string;
  invitation_code: string;
  role_requested: 'student' | 'consultant';
  profile_data: {
    first_name: string;
    last_name: string;
    [key: string]: any;
  };
  status: 'pending_approval' | 'approved' | 'rejected';
  admin_notes?: string;
  created_at: string;
  invitation?: {
    id: string;
    code: string;
    role: string;
  };
  approved_by_user?: {
    id: string;
    email: string;
    profile_data: any;
  };
}

export default function RegistrationRequestsPage() {
  const { user, loading } = useCurrentUser();
  const [requests, setRequests] = useState<RegistrationRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>('pending_approval');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [selectedRequest, setSelectedRequest] = useState<RegistrationRequest | null>(null);
  const [isProcessDialogOpen, setIsProcessDialogOpen] = useState(false);
  const [processAction, setProcessAction] = useState<'approve' | 'reject'>('approve');
  const [adminNotes, setAdminNotes] = useState('');
  const [clerkId, setClerkId] = useState('');

  const fetchRequests = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (roleFilter !== 'all') params.append('role', roleFilter);

      const response = await fetch(`/api/registration-requests?${params.toString()}`);
      const result = await response.json();

      if (result.success) {
        setRequests(result.data);
      } else {
        toast.error('Failed to load registration requests');
      }
    } catch (error) {
      console.error('Error fetching registration requests:', error);
      toast.error('Failed to load registration requests');
    } finally {
      setIsLoading(false);
    }
  }, [statusFilter, roleFilter]);

  const processRequest = async () => {
    if (!selectedRequest) return;

    try {
      const body: any = {
        action: processAction,
        admin_notes: adminNotes || undefined
      };

      if (processAction === 'approve' && clerkId) {
        body.clerk_id = clerkId;
      }

      const response = await fetch(`/api/registration-requests/${selectedRequest.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      });

      const result = await response.json();

      if (result.success) {
        toast.success(`Registration request ${processAction}d successfully`);
        setIsProcessDialogOpen(false);
        setSelectedRequest(null);
        setAdminNotes('');
        setClerkId('');
        fetchRequests();
      } else {
        toast.error(result.message || `Failed to ${processAction} request`);
      }
    } catch (error) {
      console.error(`Error ${processAction}ing request:`, error);
      toast.error(`Failed to ${processAction} request`);
    }
  };

  const openProcessDialog = (request: RegistrationRequest, action: 'approve' | 'reject') => {
    setSelectedRequest(request);
    setProcessAction(action);
    setIsProcessDialogOpen(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending_approval':
        return <Badge variant="outline" className="text-yellow-600">Pending</Badge>;
      case 'approved':
        return <Badge variant="outline" className="text-green-600">Approved</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="text-red-600">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getRoleBadge = (role: string) => {
    return (
      <Badge variant={role === 'student' ? 'default' : 'secondary'}>
        {role === 'student' ? 'Student' : 'Consultant'}
      </Badge>
    );
  };

  useEffect(() => {
    if (user && user.role === 'admin') {
      fetchRequests();
    }
  }, [user, statusFilter, roleFilter, fetchRequests]);

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading...</div>;
  }

  if (!user || user.role !== 'admin') {
    return <div className="flex items-center justify-center h-64">Access denied</div>;
  }

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Registration Requests</h1>
          <p className="text-muted-foreground">
            Review and approve user registration requests
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending_approval">Pending Approval</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
          </SelectContent>
        </Select>
        <Select value={roleFilter} onValueChange={setRoleFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Filter by role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="student">Student</SelectItem>
            <SelectItem value="consultant">Consultant</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Registration Requests List */}
      <div className="grid gap-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <Icons.spinner className="h-6 w-6 animate-spin" />
          </div>
        ) : requests.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Icons.userPlus className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="mb-2 text-lg font-semibold">No registration requests found</h3>
              <p className="text-muted-foreground mb-4 text-center">
                Registration requests will appear here when users sign up with invitation codes
              </p>
            </CardContent>
          </Card>
        ) : (
          requests.map((request) => (
            <Card key={request.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">
                      {request.profile_data.first_name} {request.profile_data.last_name}
                    </CardTitle>
                    <p className="text-muted-foreground">{request.email}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(request.status)}
                    {getRoleBadge(request.role_requested)}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm mb-4">
                  <div>
                    <p className="text-muted-foreground">Invitation Code</p>
                    <p className="font-mono">{request.invitation_code}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Requested Role</p>
                    <p>{request.role_requested}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Submitted</p>
                    <p>{new Date(request.created_at).toLocaleDateString()}</p>
                  </div>
                </div>

                {request.admin_notes && (
                  <div className="mb-4">
                    <p className="text-muted-foreground text-sm">Admin Notes</p>
                    <p className="text-sm">{request.admin_notes}</p>
                  </div>
                )}

                {request.status === 'pending_approval' && (
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={() => openProcessDialog(request, 'approve')}
                    >
                      <Icons.check className="mr-2 h-4 w-4" />
                      Approve
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openProcessDialog(request, 'reject')}
                    >
                      <Icons.x className="mr-2 h-4 w-4" />
                      Reject
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Process Request Dialog */}
      <Dialog open={isProcessDialogOpen} onOpenChange={setIsProcessDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {processAction === 'approve' ? 'Approve' : 'Reject'} Registration Request
            </DialogTitle>
          </DialogHeader>
          {selectedRequest && (
            <div className="space-y-4">
              <div>
                <p className="font-medium">
                  {selectedRequest.profile_data.first_name} {selectedRequest.profile_data.last_name}
                </p>
                <p className="text-muted-foreground">{selectedRequest.email}</p>
                <p className="text-sm">Role: {selectedRequest.role_requested}</p>
              </div>

              {processAction === 'approve' && (
                <div>
                  <Label htmlFor="clerk_id">Clerk User ID *</Label>
                  <input
                    id="clerk_id"
                    className="w-full p-2 border rounded"
                    placeholder="Enter Clerk user ID from authentication"
                    value={clerkId}
                    onChange={(e) => setClerkId(e.target.value)}
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Get this from Clerk dashboard after user completes authentication
                  </p>
                </div>
              )}

              <div>
                <Label htmlFor="admin_notes">Admin Notes (Optional)</Label>
                <Textarea
                  id="admin_notes"
                  placeholder="Add notes about this decision"
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                />
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={processRequest}
                  disabled={processAction === 'approve' && !clerkId}
                  className="flex-1"
                >
                  {processAction === 'approve' ? 'Approve Request' : 'Reject Request'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsProcessDialogOpen(false)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
