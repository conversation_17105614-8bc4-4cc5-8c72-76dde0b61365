'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Icons } from '@/components/icons';
import { useApiGet, useApiPost } from '@/lib/api-client';
import { useCurrentUser } from '@/hooks/use-current-user';
import { toast } from 'sonner';

const applicationTypes = [
  { value: 'early_decision', label: 'Early Decision' },
  { value: 'early_action', label: 'Early Action' },
  { value: 'regular_decision', label: 'Regular Decision' },
  { value: 'rolling_admission', label: 'Rolling Admission' }
];

const schoolTypes = [
  'Public University',
  'Private University',
  'Liberal Arts College',
  'Community College',
  'Technical College',
  'Art School',
  'Music Conservatory'
];

interface School {
  id: string;
  name: string;
  details: {
    location?: string;
    type?: string;
    website?: string;
  };
}

interface TargetSchoolFormData {
  school_id: string;
  application_type: string;
  deadline: string;
  priority: 'safety' | 'target' | 'reach';
  notes: string;
}

export default function AddSchoolPage() {
  const router = useRouter();
  const { user } = useCurrentUser();
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSchool, setSelectedSchool] = useState<School | null>(null);
  
  const [formData, setFormData] = useState<TargetSchoolFormData>({
    school_id: '',
    application_type: '',
    deadline: '',
    priority: 'target',
    notes: ''
  });

  // Fetch schools for search
  const { data: schools, loading: schoolsLoading } = useApiGet<School[]>('/api/schools');
  const { post: addTargetSchool } = useApiPost();

  const filteredSchools = schools?.filter(school =>
    school.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleSchoolSelect = (school: School) => {
    setSelectedSchool(school);
    setFormData(prev => ({
      ...prev,
      school_id: school.id
    }));
    setSearchTerm(school.name);
  };

  const handleInputChange = (field: keyof TargetSchoolFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.school_id || !formData.application_type || !formData.deadline) {
      toast.error('Please fill in all required fields');
      return;
    }

    setLoading(true);
    
    try {
      const response = await addTargetSchool('/api/students/target-schools', formData);
      
      if (response.success) {
        toast.success('School added to your target list!');
        router.push('/dashboard/schools/target');
      } else {
        toast.error('Failed to add school');
      }
    } catch (error) {
      console.error('Error adding school:', error);
      toast.error('Failed to add school');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Add Target School</h2>
          <p className='text-muted-foreground'>
            Add a school to your college application target list
          </p>
        </div>
        <Button variant='outline' onClick={() => router.back()}>
          <Icons.chevronLeft className='mr-2 h-4 w-4' />
          Back
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>School Selection</CardTitle>
          <CardDescription>
            Search and select a school to add to your target list
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            {/* School Search */}
            <div className='space-y-2'>
              <Label htmlFor='school-search'>Search Schools *</Label>
              <div className='relative'>
                <Input
                  id='school-search'
                  placeholder='Type to search for schools...'
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  required
                />
                {searchTerm && !selectedSchool && (
                  <div className='absolute top-full left-0 right-0 z-10 mt-1 max-h-60 overflow-y-auto rounded-md border bg-background shadow-lg'>
                    {schoolsLoading ? (
                      <div className='flex items-center justify-center p-4'>
                        <Icons.spinner className='h-4 w-4 animate-spin' />
                        <span className='ml-2'>Searching...</span>
                      </div>
                    ) : filteredSchools.length > 0 ? (
                      filteredSchools.slice(0, 10).map((school) => (
                        <button
                          key={school.id}
                          type='button'
                          className='w-full text-left p-3 hover:bg-muted/50 border-b last:border-b-0'
                          onClick={() => handleSchoolSelect(school)}
                        >
                          <div className='font-medium'>{school.name}</div>
                          <div className='text-sm text-muted-foreground'>
                            {school.details.location} • {school.details.type}
                          </div>
                        </button>
                      ))
                    ) : (
                      <div className='p-4 text-center text-muted-foreground'>
                        No schools found. Try a different search term.
                      </div>
                    )}
                  </div>
                )}
              </div>
              {selectedSchool && (
                <div className='mt-2 p-3 bg-muted/50 rounded-md'>
                  <div className='font-medium'>{selectedSchool.name}</div>
                  <div className='text-sm text-muted-foreground'>
                    {selectedSchool.details.location} • {selectedSchool.details.type}
                  </div>
                </div>
              )}
            </div>

            {/* Application Details */}
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='application_type'>Application Type *</Label>
                <Select
                  value={formData.application_type}
                  onValueChange={(value) => handleInputChange('application_type', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select application type' />
                  </SelectTrigger>
                  <SelectContent>
                    {applicationTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='deadline'>Application Deadline *</Label>
                <Input
                  id='deadline'
                  type='date'
                  value={formData.deadline}
                  onChange={(e) => handleInputChange('deadline', e.target.value)}
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='priority'>School Priority</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) => handleInputChange('priority', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='safety'>Safety School</SelectItem>
                    <SelectItem value='target'>Target School</SelectItem>
                    <SelectItem value='reach'>Reach School</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Notes */}
            <div className='space-y-2'>
              <Label htmlFor='notes'>Notes (Optional)</Label>
              <Input
                id='notes'
                placeholder='Any additional notes about this school...'
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
              />
            </div>

            <div className='flex gap-4'>
              <Button type='submit' disabled={loading || !selectedSchool}>
                {loading ? (
                  <>
                    <Icons.spinner className='mr-2 h-4 w-4 animate-spin' />
                    Adding School...
                  </>
                ) : (
                  <>
                    <Icons.plus className='mr-2 h-4 w-4' />
                    Add to Target List
                  </>
                )}
              </Button>
              <Button type='button' variant='outline' onClick={() => router.back()}>
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
