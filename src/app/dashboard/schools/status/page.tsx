'use client';

import { useState, useEffect } from 'react';
import { useCurrentUser } from '@/hooks/use-current-user';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { useApiGet, useApiPut } from '@/lib/api-client';
import { toast } from 'sonner';

interface ApplicationStatus {
  school_id: string;
  school_name: string;
  application_type: 'early_decision' | 'early_action' | 'regular_decision';
  status: 'not_started' | 'in_progress' | 'submitted' | 'decision_received';
  deadline: string;
  decision?: 'accepted' | 'rejected' | 'waitlisted' | 'deferred';
  decision_date?: string;
  requirements: {
    essays: { completed: number; total: number };
    transcripts: boolean;
    test_scores: boolean;
    recommendations: { completed: number; total: number };
    supplements: { completed: number; total: number };
  };
  notes?: string;
}

export default function ApplicationStatusPage() {
  const { user } = useCurrentUser();
  const [applications, setApplications] = useState<ApplicationStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API hooks
  const { data: studentProfile } = useApiGet<{ id: string }>('/api/profile/student');
  const { data: targetSchools, loading: targetLoading, error: targetError, fetch: fetchTargetSchools } = useApiGet<any[]>(
    studentProfile?.id ? `/api/students/target-schools` : ''
  );
  const { put: updateApplicationStatus } = useApiPut();

  useEffect(() => {
    if (targetSchools) {
      // Transform target schools into application status format
      const applicationStatuses: ApplicationStatus[] = targetSchools.map(school => ({
        school_id: school.school_id,
        school_name: school.schools?.name || 'Unknown School',
        application_type: school.application_type,
        status: school.status || 'not_started',
        deadline: school.deadline,
        decision: school.decision,
        decision_date: school.decision_date,
        requirements: {
          essays: { completed: 0, total: 3 }, // These would come from actual data
          transcripts: false,
          test_scores: false,
          recommendations: { completed: 0, total: 2 },
          supplements: { completed: 0, total: 1 }
        },
        notes: school.notes
      }));
      setApplications(applicationStatuses);
    }
    setLoading(targetLoading);
    if (targetError) {
      setError(targetError);
    }
  }, [targetSchools, targetLoading, targetError]);

  const handleStatusUpdate = async (schoolId: string, newStatus: string) => {
    if (!studentProfile?.id) {
      toast.error('Student profile not found');
      return;
    }

    try {
      const response = await updateApplicationStatus(`/api/students/target-schools/${schoolId}`, {
        status: newStatus
      });

      if (response.success) {
        toast.success('Application status updated successfully!');
        fetchTargetSchools(); // Refresh the data
      } else {
        toast.error('Failed to update application status');
      }
    } catch (error) {
      console.error('Error updating application status:', error);
      toast.error('Failed to update application status');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted':
        return 'bg-purple-100 text-purple-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'decision_received':
        return 'bg-green-100 text-green-800';
      case 'not_started':
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDecisionColor = (decision: string) => {
    switch (decision) {
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'waitlisted':
        return 'bg-yellow-100 text-yellow-800';
      case 'deferred':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateOverallProgress = (
    requirements: ApplicationStatus['requirements']
  ) => {
    const total =
      requirements.essays.total +
      (requirements.transcripts ? 1 : 0) +
      (requirements.test_scores ? 1 : 0) +
      requirements.recommendations.total +
      requirements.supplements.total;

    const completed =
      requirements.essays.completed +
      (requirements.transcripts ? 1 : 0) +
      (requirements.test_scores ? 1 : 0) +
      requirements.recommendations.completed +
      requirements.supplements.completed;

    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  const getDaysUntilDeadline = (deadline: string) => {
    const deadlineDate = new Date(deadline);
    const today = new Date();
    const diffTime = deadlineDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getDeadlineColor = (daysUntil: number) => {
    if (daysUntil < 0) return 'text-red-600';
    if (daysUntil <= 7) return 'text-orange-600';
    if (daysUntil <= 30) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* Header */}
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>
              Application Status
            </h2>
            <p className='text-muted-foreground'>
              Track your college application progress and deadlines
            </p>
          </div>
          <Button asChild>
            <Link href='/dashboard/schools/target'>
              <Icons.plus className='mr-2 h-4 w-4' />
              Manage Schools
            </Link>
          </Button>
        </div>

        {/* Overview Stats */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Total Applications
              </CardTitle>
              <Icons.fileText className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{applications.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>In Progress</CardTitle>
              <Icons.clock className='h-4 w-4 text-yellow-600' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {
                  applications.filter((app) => app.status === 'in_progress')
                    .length
                }
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Submitted</CardTitle>
              <Icons.check className='h-4 w-4 text-green-600' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {
                  applications.filter((app) => app.status === 'submitted')
                    .length
                }
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Decisions</CardTitle>
              <Icons.star className='h-4 w-4 text-purple-600' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {
                  applications.filter(
                    (app) => app.status === 'decision_received'
                  ).length
                }
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Applications List */}
        <Card>
          <CardHeader>
            <CardTitle>Application Progress</CardTitle>
            <CardDescription>
              Detailed status of each college application
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className='flex items-center justify-center py-8'>
                <div className='border-primary h-8 w-8 animate-spin rounded-full border-b-2'></div>
              </div>
            ) : error ? (
              <div className='py-8 text-center'>
                <p className='text-muted-foreground mb-4'>{error}</p>
                <Button onClick={fetchTargetSchools} variant='outline'>
                  Try Again
                </Button>
              </div>
            ) : applications.length === 0 ? (
              <div className='py-8 text-center'>
                <Icons.school className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
                <h3 className='mb-2 text-lg font-medium'>
                  No Applications Yet
                </h3>
                <p className='text-muted-foreground mb-4'>
                  Add schools to your target list to start tracking applications
                </p>
                <Button asChild>
                  <Link href='/dashboard/schools/target'>
                    <Icons.plus className='mr-2 h-4 w-4' />
                    Add Schools
                  </Link>
                </Button>
              </div>
            ) : (
              <div className='space-y-6'>
                {applications.map((app) => {
                  const daysUntil = getDaysUntilDeadline(app.deadline);
                  const progress = calculateOverallProgress(app.requirements);

                  return (
                    <div
                      key={app.school_id}
                      className='space-y-4 rounded-lg border p-6'
                    >
                      {/* School Header */}
                      <div className='flex items-center justify-between'>
                        <div>
                          <h3 className='text-lg font-medium'>
                            {app.school_name}
                          </h3>
                          <div className='mt-1 flex items-center space-x-2'>
                            <Badge className={getStatusColor(app.status)}>
                              {app.status.replace('_', ' ')}
                            </Badge>
                            <Badge variant='outline'>
                              {app.application_type.replace('_', ' ')}
                            </Badge>
                            {app.decision && (
                              <Badge className={getDecisionColor(app.decision)}>
                                {app.decision}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className='text-right'>
                          <p className='text-sm font-medium'>
                            Deadline:{' '}
                            {new Date(app.deadline).toLocaleDateString()}
                          </p>
                          <p
                            className={`text-sm ${getDeadlineColor(daysUntil)}`}
                          >
                            {daysUntil < 0
                              ? 'Overdue'
                              : `${daysUntil} days left`}
                          </p>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className='space-y-2'>
                        <div className='flex items-center justify-between'>
                          <span className='text-sm font-medium'>
                            Overall Progress
                          </span>
                          <span className='text-muted-foreground text-sm'>
                            {progress}%
                          </span>
                        </div>
                        <Progress value={progress} className='h-2' />
                      </div>

                      {/* Requirements Breakdown */}
                      <div className='grid gap-3 md:grid-cols-2 lg:grid-cols-5'>
                        <div className='text-center'>
                          <p className='text-muted-foreground text-xs'>
                            Essays
                          </p>
                          <p className='text-sm font-medium'>
                            {app.requirements.essays.completed}/
                            {app.requirements.essays.total}
                          </p>
                        </div>
                        <div className='text-center'>
                          <p className='text-muted-foreground text-xs'>
                            Transcripts
                          </p>
                          <p className='text-sm font-medium'>
                            {app.requirements.transcripts ? '✓' : '○'}
                          </p>
                        </div>
                        <div className='text-center'>
                          <p className='text-muted-foreground text-xs'>
                            Test Scores
                          </p>
                          <p className='text-sm font-medium'>
                            {app.requirements.test_scores ? '✓' : '○'}
                          </p>
                        </div>
                        <div className='text-center'>
                          <p className='text-muted-foreground text-xs'>
                            Recommendations
                          </p>
                          <p className='text-sm font-medium'>
                            {app.requirements.recommendations.completed}/
                            {app.requirements.recommendations.total}
                          </p>
                        </div>
                        <div className='text-center'>
                          <p className='text-muted-foreground text-xs'>
                            Supplements
                          </p>
                          <p className='text-sm font-medium'>
                            {app.requirements.supplements.completed}/
                            {app.requirements.supplements.total}
                          </p>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className='flex items-center justify-between pt-2'>
                        <div className='flex items-center space-x-2'>
                          <Button size='sm' variant='outline'>
                            <Icons.edit className='mr-2 h-4 w-4' />
                            Update Status
                          </Button>
                          <Button size='sm' variant='outline'>
                            <Icons.fileText className='mr-2 h-4 w-4' />
                            View Essays
                          </Button>
                        </div>
                        <Button size='sm'>View Details</Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </ProfileSetupCheck>
  );
}
