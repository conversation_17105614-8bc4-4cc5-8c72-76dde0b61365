'use client';

import { useState, useEffect } from 'react';
import { useCurrentUser } from '@/hooks/use-current-user';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';
import { useApiGet } from '@/lib/api-client';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Icons } from '@/components/icons';
import Link from 'next/link';

interface School {
  id: string;
  name: string;
  details: {
    location?: string;
    type?: string;
    acceptance_rate?: number;
    ranking?: number;
  };
  application_requirements: {
    deadline?: string;
    essays_required?: number;
    test_scores?: string[];
  };
}

interface TargetSchool {
  id: string;
  student_id: string;
  school_id: string;
  application_type: 'early_decision' | 'early_action' | 'regular_decision' | 'rolling_admission';
  deadline: string;
  priority: 'safety' | 'target' | 'reach';
  notes?: string;
  status: 'not_started' | 'in_progress' | 'submitted' | 'decision_received';
  schools: {
    id: string;
    name: string;
    details: any;
  };
  created_at: string;
  updated_at: string;
}

export default function TargetSchoolsPage() {
  const { user } = useCurrentUser();
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch target schools using the API
  const {
    data: targetSchools,
    loading: targetSchoolsLoading,
    error: targetSchoolsError,
    fetch: fetchTargetSchools
  } = useApiGet<TargetSchool[]>('/api/students/target-schools');

  // Fetch available schools
  const {
    data: availableSchools,
    loading: schoolsLoading,
    error: schoolsError
  } = useApiGet<School[]>('/api/schools');

  useEffect(() => {
    fetchTargetSchools();
  }, [fetchTargetSchools]);

  const loading = targetSchoolsLoading || schoolsLoading;
  const error = targetSchoolsError || schoolsError;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'safety':
        return 'bg-green-100 text-green-800';
      case 'target':
        return 'bg-blue-100 text-blue-800';
      case 'reach':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted':
        return 'bg-purple-100 text-purple-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'decision_received':
        return 'bg-green-100 text-green-800';
      case 'planning':
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredSchools = (availableSchools || []).filter((school) =>
    school.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const schoolsByPriority = {
    safety: (targetSchools || []).filter((ts) => ts.priority === 'safety'),
    target: (targetSchools || []).filter((ts) => ts.priority === 'target'),
    reach: (targetSchools || []).filter((ts) => ts.priority === 'reach')
  };

  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* Header */}
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>
              Target Schools
            </h2>
            <p className='text-muted-foreground'>
              Manage your college application target list
            </p>
          </div>
          <Button asChild>
            <Link href='/dashboard/schools/add'>
              <Icons.plus className='mr-2 h-4 w-4' />
              Add School
            </Link>
          </Button>
        </div>

        {/* Overview Stats */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Total Schools
              </CardTitle>
              <Icons.school className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{targetSchools?.length || 0}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Safety Schools
              </CardTitle>
              <Icons.shield className='h-4 w-4 text-green-600' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {schoolsByPriority.safety.length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Target Schools
              </CardTitle>
              <Icons.target className='h-4 w-4 text-blue-600' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {schoolsByPriority.target.length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Reach Schools
              </CardTitle>
              <Icons.star className='h-4 w-4 text-red-600' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {schoolsByPriority.reach.length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Target Schools List */}
        <Card>
          <CardHeader>
            <CardTitle>Your Target Schools</CardTitle>
            <CardDescription>
              Schools you&apos;re planning to apply to, organized by priority
            </CardDescription>
          </CardHeader>
          <CardContent>
            {(targetSchools?.length || 0) === 0 ? (
              <div className='py-8 text-center'>
                <Icons.school className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
                <h3 className='mb-2 text-lg font-medium'>
                  No Target Schools Yet
                </h3>
                <p className='text-muted-foreground mb-4'>
                  Start building your college list by adding schools
                </p>
                <Button asChild>
                  <Link href='/dashboard/schools/add'>
                    <Icons.plus className='mr-2 h-4 w-4' />
                    Add Your First School
                  </Link>
                </Button>
              </div>
            ) : (
              <div className='space-y-6'>
                {(['safety', 'target', 'reach'] as const).map((priority) => (
                  <div key={priority}>
                    <h3 className='mb-3 flex items-center gap-2 text-lg font-medium capitalize'>
                      {priority === 'safety' && (
                        <Icons.shield className='h-5 w-5 text-green-600' />
                      )}
                      {priority === 'target' && (
                        <Icons.target className='h-5 w-5 text-blue-600' />
                      )}
                      {priority === 'reach' && (
                        <Icons.star className='h-5 w-5 text-red-600' />
                      )}
                      {priority} Schools ({schoolsByPriority[priority].length})
                    </h3>
                    <div className='space-y-3'>
                      {schoolsByPriority[priority].map((targetSchool) => (
                        <div
                          key={targetSchool.id}
                          className='hover:bg-muted/50 flex items-center justify-between rounded-lg border p-4 transition-colors'
                        >
                          <div className='flex items-center space-x-4'>
                            <div className='min-w-0 flex-1'>
                              <h4 className='truncate text-sm font-medium'>
                                {targetSchool.schools.name}
                              </h4>
                              <div className='mt-1 flex items-center space-x-2'>
                                <Badge
                                  className={getPriorityColor(
                                    targetSchool.priority
                                  )}
                                >
                                  {targetSchool.priority}
                                </Badge>
                                <Badge
                                  className={getStatusColor(
                                    targetSchool.status
                                  )}
                                >
                                  {targetSchool.status.replace('_', ' ')}
                                </Badge>
                                {targetSchool.schools.details?.location && (
                                  <span className='text-muted-foreground text-xs'>
                                    {targetSchool.schools.details.location}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className='flex items-center space-x-2'>
                            <Button size='sm' variant='outline'>
                              <Icons.edit className='h-4 w-4' />
                            </Button>
                            <Button size='sm' asChild>
                              <Link
                                href={`/dashboard/schools/${targetSchool.schools.id}`}
                              >
                                View Details
                              </Link>
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* School Search */}
        <Card>
          <CardHeader>
            <CardTitle>Add Schools</CardTitle>
            <CardDescription>
              Search and add schools to your target list
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <Input
                placeholder='Search schools...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='max-w-sm'
              />
              {loading ? (
                <div className='flex items-center justify-center py-4'>
                  <div className='border-primary h-6 w-6 animate-spin rounded-full border-b-2'></div>
                </div>
              ) : (
                <div className='max-h-60 space-y-2 overflow-y-auto'>
                  {filteredSchools.slice(0, 10).map((school) => (
                    <div
                      key={school.id}
                      className='hover:bg-muted/50 flex items-center justify-between rounded-lg border p-3 transition-colors'
                    >
                      <div>
                        <h4 className='text-sm font-medium'>{school.name}</h4>
                        <p className='text-muted-foreground text-xs'>
                          {school.details.location} • {school.details.type}
                        </p>
                      </div>
                      <Button size='sm' variant='outline'>
                        <Icons.plus className='h-4 w-4' />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </ProfileSetupCheck>
  );
}
