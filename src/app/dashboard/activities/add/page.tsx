'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Icons } from '@/components/icons';
import { useApiPost } from '@/lib/api-client';
import { useCurrentUser } from '@/hooks/use-current-user';
import { toast } from 'sonner';

const activityCategories = [
  'Academic',
  'Arts',
  'Athletics',
  'Community Service',
  'Leadership',
  'Music',
  'Research',
  'Work Experience',
  'Other'
];

interface ActivityFormData {
  name: string;
  category: string;
  organization: string;
  position: string;
  yearsActive: string;
  hoursPerWeek: number;
  weeksPerYear: number;
  description: string;
  achievements: string;
}

export default function AddActivityPage() {
  const router = useRouter();
  const { user } = useCurrentUser();
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState<ActivityFormData>({
    name: '',
    category: '',
    organization: '',
    position: '',
    yearsActive: '',
    hoursPerWeek: 0,
    weeksPerYear: 0,
    description: '',
    achievements: ''
  });

  const { post: createActivity } = useApiPost();

  const handleInputChange = (field: keyof ActivityFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.category) {
      toast.error('Please fill in all required fields');
      return;
    }

    setLoading(true);
    
    try {
      // Convert achievements string to array
      const achievementsArray = formData.achievements
        .split('\n')
        .filter(achievement => achievement.trim() !== '')
        .map(achievement => achievement.trim());

      const activityData = {
        ...formData,
        achievements: achievementsArray,
        hoursPerWeek: Number(formData.hoursPerWeek),
        weeksPerYear: Number(formData.weeksPerYear)
      };

      const response = await createActivity('/api/activities', activityData);
      
      if (response.success) {
        toast.success('Activity added successfully!');
        router.push('/dashboard/activities');
      } else {
        toast.error('Failed to add activity');
      }
    } catch (error) {
      console.error('Error adding activity:', error);
      toast.error('Failed to add activity');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Add Activity</h2>
          <p className='text-muted-foreground'>
            Add a new extracurricular activity to your profile
          </p>
        </div>
        <Button variant='outline' onClick={() => router.back()}>
          <Icons.chevronLeft className='mr-2 h-4 w-4' />
          Back
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Activity Details</CardTitle>
          <CardDescription>
            Provide information about your extracurricular activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='name'>Activity Name *</Label>
                <Input
                  id='name'
                  placeholder='e.g., Student Government'
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='category'>Category *</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => handleInputChange('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select category' />
                  </SelectTrigger>
                  <SelectContent>
                    {activityCategories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='organization'>Organization</Label>
                <Input
                  id='organization'
                  placeholder='e.g., Lincoln High School'
                  value={formData.organization}
                  onChange={(e) => handleInputChange('organization', e.target.value)}
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='position'>Position/Role</Label>
                <Input
                  id='position'
                  placeholder='e.g., President, Member, Volunteer'
                  value={formData.position}
                  onChange={(e) => handleInputChange('position', e.target.value)}
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='yearsActive'>Years Active</Label>
                <Input
                  id='yearsActive'
                  placeholder='e.g., 2022-2024'
                  value={formData.yearsActive}
                  onChange={(e) => handleInputChange('yearsActive', e.target.value)}
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='hoursPerWeek'>Hours per Week</Label>
                <Input
                  id='hoursPerWeek'
                  type='number'
                  min='0'
                  max='168'
                  value={formData.hoursPerWeek}
                  onChange={(e) => handleInputChange('hoursPerWeek', Number(e.target.value))}
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='weeksPerYear'>Weeks per Year</Label>
                <Input
                  id='weeksPerYear'
                  type='number'
                  min='0'
                  max='52'
                  value={formData.weeksPerYear}
                  onChange={(e) => handleInputChange('weeksPerYear', Number(e.target.value))}
                />
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='description'>Description</Label>
              <Textarea
                id='description'
                placeholder='Describe your role, responsibilities, and what you accomplished...'
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={4}
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='achievements'>Achievements (one per line)</Label>
              <Textarea
                id='achievements'
                placeholder='List your key achievements, awards, or accomplishments...'
                value={formData.achievements}
                onChange={(e) => handleInputChange('achievements', e.target.value)}
                rows={4}
              />
            </div>

            <div className='flex gap-4'>
              <Button type='submit' disabled={loading}>
                {loading ? (
                  <>
                    <Icons.spinner className='mr-2 h-4 w-4 animate-spin' />
                    Adding Activity...
                  </>
                ) : (
                  <>
                    <Icons.plus className='mr-2 h-4 w-4' />
                    Add Activity
                  </>
                )}
              </Button>
              <Button type='button' variant='outline' onClick={() => router.back()}>
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
