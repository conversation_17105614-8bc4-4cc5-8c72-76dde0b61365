'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import { useApiPost } from '@/lib/api-client';
import { useCurrentUser } from '@/hooks/use-current-user';
import { toast } from 'sonner';

// Common App activity categories
const activityCategories = [
  { value: 'academic', label: 'Academic' },
  { value: 'art', label: 'Art' },
  { value: 'athletics', label: 'Athletics/Sports' },
  { value: 'community_service', label: 'Community Service (Volunteer)' },
  { value: 'cultural', label: 'Cultural' },
  { value: 'debate', label: 'Debate/Speech' },
  { value: 'journalism', label: 'Journalism/Publication' },
  { value: 'music', label: 'Music: Instrumental' },
  { value: 'religious', label: 'Religious' },
  { value: 'research', label: 'Research' },
  { value: 'robotics', label: 'Robotics' },
  { value: 'student_government', label: 'Student Government/Politics' },
  { value: 'work', label: 'Work (Paid)' },
  { value: 'honors', label: 'Honor Society/Academic Honor' },
  { value: 'other', label: 'Other Club/Activity' }
];

const gradeLevels = [
  { value: '9', label: '9th Grade' },
  { value: '10', label: '10th Grade' },
  { value: '11', label: '11th Grade' },
  { value: '12', label: '12th Grade' },
  { value: 'Post-graduate', label: 'Post-graduate' }
];

interface ActivityFormData {
  // Common App structure fields
  activity_type: string;
  organization_name: string;
  position_title: string;
  description: string;
  participation_grade_levels: string[];
  hours_per_week: number;
  weeks_per_year: number;
  leadership_role: boolean;
  awards_recognition: string;

  // Legacy fields for backward compatibility
  name: string;
  category: string;
  organization: string;
  position: string;
  yearsActive: string;
  hoursPerWeek: number;
  weeksPerYear: number;
  achievements: string;
}

export default function AddActivityPage() {
  const router = useRouter();
  const { user } = useCurrentUser();
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState<ActivityFormData>({
    // Common App structure fields
    activity_type: '',
    organization_name: '',
    position_title: '',
    description: '',
    participation_grade_levels: [],
    hours_per_week: 0,
    weeks_per_year: 0,
    leadership_role: false,
    awards_recognition: '',

    // Legacy fields for backward compatibility
    name: '',
    category: '',
    organization: '',
    position: '',
    yearsActive: '',
    hoursPerWeek: 0,
    weeksPerYear: 0,
    achievements: ''
  });

  const { post: createActivity } = useApiPost();

  const handleInputChange = (field: keyof ActivityFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.activity_type || !formData.organization_name) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Validate description word count (Common App limit: 150 words)
    const wordCount = formData.description.trim().split(/\s+/).length;
    if (wordCount > 150) {
      toast.error(`Description must be 150 words or less. Current: ${wordCount} words`);
      return;
    }

    setLoading(true);

    try {
      // Convert achievements string to array
      const achievementsArray = formData.achievements
        .split('\n')
        .filter(achievement => achievement.trim() !== '')
        .map(achievement => achievement.trim());

      const activityData = {
        // Common App structure fields
        activity_type: formData.activity_type,
        organization_name: formData.organization_name,
        position_title: formData.position_title,
        description: formData.description,
        participation_grade_levels: formData.participation_grade_levels,
        hours_per_week: Number(formData.hours_per_week),
        weeks_per_year: Number(formData.weeks_per_year),
        leadership_role: formData.leadership_role,
        awards_recognition: formData.awards_recognition,

        // Legacy fields for backward compatibility
        name: formData.name || `${formData.organization_name}${formData.position_title ? ` - ${formData.position_title}` : ''}`,
        category: formData.category || formData.activity_type,
        organization: formData.organization || formData.organization_name,
        position: formData.position || formData.position_title,
        yearsActive: formData.yearsActive,
        hoursPerWeek: Number(formData.hoursPerWeek || formData.hours_per_week),
        weeksPerYear: Number(formData.weeksPerYear || formData.weeks_per_year),
        achievements: achievementsArray
      };

      const response = await createActivity('/api/activities', activityData);

      if (response.success) {
        toast.success('Activity added successfully!');
        router.push('/dashboard/activities');
      } else {
        toast.error('Failed to add activity');
      }
    } catch (error) {
      console.error('Error adding activity:', error);
      toast.error('Failed to add activity');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Add Activity</h2>
          <p className='text-muted-foreground'>
            Add a new extracurricular activity to your profile
          </p>
        </div>
        <Button variant='outline' onClick={() => router.back()}>
          <Icons.chevronLeft className='mr-2 h-4 w-4' />
          Back
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Activity Details</CardTitle>
          <CardDescription>
            Provide information about your extracurricular activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='activity_type'>Activity Type *</Label>
                <Select
                  value={formData.activity_type}
                  onValueChange={(value) => handleInputChange('activity_type', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select activity type' />
                  </SelectTrigger>
                  <SelectContent>
                    {activityCategories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='organization_name'>Organization Name *</Label>
                <Input
                  id='organization_name'
                  placeholder='e.g., Lincoln High School, Red Cross'
                  value={formData.organization_name}
                  onChange={(e) => handleInputChange('organization_name', e.target.value)}
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='position_title'>Position/Title</Label>
                <Input
                  id='position_title'
                  placeholder='e.g., President, Member, Volunteer'
                  value={formData.position_title}
                  onChange={(e) => handleInputChange('position_title', e.target.value)}
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='hours_per_week'>Hours per Week *</Label>
                <Input
                  id='hours_per_week'
                  type='number'
                  min='0'
                  max='168'
                  value={formData.hours_per_week}
                  onChange={(e) => handleInputChange('hours_per_week', Number(e.target.value))}
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='weeks_per_year'>Weeks per Year *</Label>
                <Input
                  id='weeks_per_year'
                  type='number'
                  min='0'
                  max='52'
                  value={formData.weeks_per_year}
                  onChange={(e) => handleInputChange('weeks_per_year', Number(e.target.value))}
                  required
                />
              </div>
            </div>

            {/* Grade Levels Participation */}
            <div className='space-y-2'>
              <Label>Grade Levels of Participation</Label>
              <div className='flex flex-wrap gap-2'>
                {gradeLevels.map((grade) => (
                  <div key={grade.value} className='flex items-center space-x-2'>
                    <Checkbox
                      id={`grade-${grade.value}`}
                      checked={formData.participation_grade_levels.includes(grade.value)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          handleInputChange('participation_grade_levels', [
                            ...formData.participation_grade_levels,
                            grade.value
                          ]);
                        } else {
                          handleInputChange('participation_grade_levels',
                            formData.participation_grade_levels.filter(g => g !== grade.value)
                          );
                        }
                      }}
                    />
                    <Label htmlFor={`grade-${grade.value}`} className='text-sm'>
                      {grade.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Leadership Role */}
            <div className='flex items-center space-x-2'>
              <Checkbox
                id='leadership_role'
                checked={formData.leadership_role}
                onCheckedChange={(checked) => handleInputChange('leadership_role', checked)}
              />
              <Label htmlFor='leadership_role'>
                This was a leadership role
              </Label>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='description'>
                Description
                <span className='text-sm text-muted-foreground ml-2'>
                  (Max 150 words - {formData.description.trim().split(/\s+/).filter(word => word.length > 0).length}/150)
                </span>
              </Label>
              <Textarea
                id='description'
                placeholder='Describe your role, responsibilities, and what you accomplished...'
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={4}
                className={formData.description.trim().split(/\s+/).filter(word => word.length > 0).length > 150 ? 'border-red-500' : ''}
              />
              {formData.description.trim().split(/\s+/).filter(word => word.length > 0).length > 150 && (
                <p className='text-sm text-red-500'>
                  Description exceeds 150 word limit (Common App requirement)
                </p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='awards_recognition'>Awards & Recognition</Label>
              <Textarea
                id='awards_recognition'
                placeholder='List any awards, honors, or recognition you received...'
                value={formData.awards_recognition}
                onChange={(e) => handleInputChange('awards_recognition', e.target.value)}
                rows={3}
              />
            </div>

            <div className='flex gap-4'>
              <Button type='submit' disabled={loading}>
                {loading ? (
                  <>
                    <Icons.spinner className='mr-2 h-4 w-4 animate-spin' />
                    Adding Activity...
                  </>
                ) : (
                  <>
                    <Icons.plus className='mr-2 h-4 w-4' />
                    Add Activity
                  </>
                )}
              </Button>
              <Button type='button' variant='outline' onClick={() => router.back()}>
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
