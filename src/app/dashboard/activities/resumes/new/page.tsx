'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';
import { EnhancedResumeCreator } from '@/components/activities/enhanced-resume-creator';

export default function NewActivityResumePage() {
  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight'>Create Activity Resume</h1>
            <p className='text-muted-foreground'>
              Create a focused version of your activity resume with smart recommendations and analytics
            </p>
          </div>
          <Button variant='outline' asChild>
            <Link href='/dashboard/activities/resumes'>
              <Icons.chevronLeft className='mr-2 h-4 w-4' />
              Back to Resumes
            </Link>
          </Button>
        </div>

        {/* Enhanced Resume Creator */}
        <EnhancedResumeCreator />
      </div>
    </ProfileSetupCheck>
  );
}
