'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { useApiGet } from '@/lib/api-client';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';

interface Activity {
  id: string;
  activity_type: string;
  organization_name: string;
  position_title?: string;
  description?: string;
  hours_per_week: number;
  weeks_per_year: number;
  leadership_role: boolean;
  awards_recognition?: string;
}

interface CreateResumeForm {
  title: string;
  description: string;
  focus_area: string;
  selected_activities: string[];
  template_style: string;
  is_default: boolean;
}

const focusAreaOptions = [
  { value: 'engineering', label: 'Engineering & Technology' },
  { value: 'liberal_arts', label: 'Liberal Arts & Humanities' },
  { value: 'business', label: 'Business & Economics' },
  { value: 'sciences', label: 'Natural Sciences' },
  { value: 'social_sciences', label: 'Social Sciences' },
  { value: 'arts', label: 'Arts & Creative' },
  { value: 'pre_med', label: 'Pre-Medical' },
  { value: 'pre_law', label: 'Pre-Law' },
  { value: 'education', label: 'Education' },
  { value: 'general', label: 'General Application' }
];

const templateStyleOptions = [
  { value: 'standard', label: 'Standard Format' },
  { value: 'detailed', label: 'Detailed Format' },
  { value: 'compact', label: 'Compact Format' },
  { value: 'chronological', label: 'Chronological Format' },
  { value: 'categorical', label: 'Categorical Format' }
];

export default function NewActivityResumePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CreateResumeForm>({
    title: '',
    description: '',
    focus_area: '',
    selected_activities: [],
    template_style: 'standard',
    is_default: false
  });

  const {
    data: activities,
    loading: activitiesLoading,
    error: activitiesError
  } = useApiGet<Activity[]>('/api/activities');

  const activityList = activities || [];

  const handleInputChange = (field: keyof CreateResumeForm, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleActivityToggle = (activityId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      selected_activities: checked
        ? [...prev.selected_activities, activityId]
        : prev.selected_activities.filter(id => id !== activityId)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      alert('Please enter a title for your resume');
      return;
    }

    if (!formData.focus_area) {
      alert('Please select a focus area');
      return;
    }

    if (formData.selected_activities.length === 0) {
      alert('Please select at least one activity');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/activity-resumes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const result = await response.json();
        router.push(`/dashboard/activities/resumes/${result.data.id}`);
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || 'Failed to create resume'}`);
      }
    } catch (error) {
      console.error('Error creating resume:', error);
      alert('Failed to create resume');
    } finally {
      setLoading(false);
    }
  };

  const selectedActivitiesData = activityList.filter(activity =>
    formData.selected_activities.includes(activity.id)
  );

  const totalHours = selectedActivitiesData.reduce(
    (sum, activity) => sum + (activity.hours_per_week * activity.weeks_per_year),
    0
  );

  if (activitiesLoading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-center space-x-2'>
          <Icons.spinner className='h-4 w-4 animate-spin' />
          <span>Loading activities...</span>
        </div>
      </div>
    );
  }

  if (activitiesError) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <Card>
          <CardContent className='pt-6'>
            <div className='text-center'>
              <Icons.alertCircle className='mx-auto h-12 w-12 text-red-500' />
              <h3 className='mt-2 text-sm font-medium text-gray-900'>
                Error loading activities
              </h3>
              <p className='mt-1 text-sm text-gray-500'>{activitiesError}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (activityList.length === 0) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <Card>
          <CardContent className='pt-6'>
            <div className='text-center'>
              <Icons.activity className='mx-auto h-12 w-12 text-gray-400' />
              <h3 className='mt-2 text-sm font-medium text-gray-900'>
                No activities found
              </h3>
              <p className='mt-1 text-sm text-gray-500'>
                You need to add activities before creating a resume.
              </p>
              <div className='mt-6'>
                <Button asChild>
                  <Link href='/dashboard/activities/add'>
                    <Icons.plus className='mr-2 h-4 w-4' />
                    Add Your First Activity
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight'>Create Activity Resume</h1>
            <p className='text-muted-foreground'>
              Create a focused version of your activity resume for specific applications
            </p>
          </div>
          <Button variant='outline' asChild>
            <Link href='/dashboard/activities/resumes'>
              <Icons.arrowLeft className='mr-2 h-4 w-4' />
              Back to Resumes
            </Link>
          </Button>
        </div>

        <div className='grid gap-6 lg:grid-cols-3'>
          {/* Form */}
          <div className='lg:col-span-2'>
            <form onSubmit={handleSubmit} className='space-y-6'>
              <Card>
                <CardHeader>
                  <CardTitle>Resume Details</CardTitle>
                  <CardDescription>
                    Basic information about this resume version
                  </CardDescription>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='title'>Resume Title *</Label>
                    <Input
                      id='title'
                      placeholder='e.g., Engineering Focus Resume'
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      required
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='description'>Description</Label>
                    <Textarea
                      id='description'
                      placeholder='Brief description of this resume version...'
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      rows={3}
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='focus_area'>Focus Area *</Label>
                    <Select
                      value={formData.focus_area}
                      onValueChange={(value) => handleInputChange('focus_area', value)}
                      required
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select focus area' />
                      </SelectTrigger>
                      <SelectContent>
                        {focusAreaOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='template_style'>Template Style</Label>
                    <Select
                      value={formData.template_style}
                      onValueChange={(value) => handleInputChange('template_style', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {templateStyleOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className='flex items-center space-x-2'>
                    <Checkbox
                      id='is_default'
                      checked={formData.is_default}
                      onCheckedChange={(checked) => 
                        handleInputChange('is_default', checked === true)
                      }
                    />
                    <Label htmlFor='is_default'>Set as default resume</Label>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Select Activities</CardTitle>
                  <CardDescription>
                    Choose which activities to include in this resume version
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='space-y-3'>
                    {activityList.map((activity) => (
                      <div
                        key={activity.id}
                        className='flex items-start space-x-3 rounded-lg border p-3'
                      >
                        <Checkbox
                          id={`activity-${activity.id}`}
                          checked={formData.selected_activities.includes(activity.id)}
                          onCheckedChange={(checked) =>
                            handleActivityToggle(activity.id, checked === true)
                          }
                        />
                        <div className='flex-1 min-w-0'>
                          <div className='flex items-center justify-between'>
                            <Label
                              htmlFor={`activity-${activity.id}`}
                              className='font-medium cursor-pointer'
                            >
                              {activity.organization_name}
                            </Label>
                            {activity.leadership_role && (
                              <Icons.crown className='h-4 w-4 text-yellow-500' />
                            )}
                          </div>
                          <p className='text-sm text-muted-foreground'>
                            {activity.position_title && `${activity.position_title} • `}
                            {activity.activity_type} • {activity.hours_per_week}h/week
                          </p>
                          {activity.description && (
                            <p className='text-xs text-muted-foreground mt-1'>
                              {activity.description.length > 100
                                ? `${activity.description.substring(0, 100)}...`
                                : activity.description}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <div className='flex gap-4'>
                <Button type='submit' disabled={loading} className='flex-1'>
                  {loading ? (
                    <>
                      <Icons.spinner className='mr-2 h-4 w-4 animate-spin' />
                      Creating Resume...
                    </>
                  ) : (
                    <>
                      <Icons.plus className='mr-2 h-4 w-4' />
                      Create Resume
                    </>
                  )}
                </Button>
                <Button type='button' variant='outline' asChild>
                  <Link href='/dashboard/activities/resumes'>Cancel</Link>
                </Button>
              </div>
            </form>
          </div>

          {/* Preview */}
          <div className='lg:col-span-1'>
            <Card className='sticky top-4'>
              <CardHeader>
                <CardTitle>Resume Preview</CardTitle>
                <CardDescription>
                  Summary of your resume version
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div>
                  <Label className='text-sm font-medium'>Title</Label>
                  <p className='text-sm text-muted-foreground'>
                    {formData.title || 'Untitled Resume'}
                  </p>
                </div>

                <div>
                  <Label className='text-sm font-medium'>Focus Area</Label>
                  <p className='text-sm text-muted-foreground'>
                    {focusAreaOptions.find(opt => opt.value === formData.focus_area)?.label || 'Not selected'}
                  </p>
                </div>

                <div>
                  <Label className='text-sm font-medium'>Selected Activities</Label>
                  <p className='text-sm text-muted-foreground'>
                    {formData.selected_activities.length} activities selected
                  </p>
                </div>

                <div>
                  <Label className='text-sm font-medium'>Total Hours</Label>
                  <p className='text-sm text-muted-foreground'>
                    {totalHours.toLocaleString()} hours annually
                  </p>
                </div>

                <div>
                  <Label className='text-sm font-medium'>Template</Label>
                  <p className='text-sm text-muted-foreground'>
                    {templateStyleOptions.find(opt => opt.value === formData.template_style)?.label}
                  </p>
                </div>

                {formData.is_default && (
                  <div className='flex items-center space-x-2 text-sm'>
                    <Icons.star className='h-4 w-4 text-yellow-500' />
                    <span className='text-muted-foreground'>Default resume</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </ProfileSetupCheck>
  );
}
