'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';
import { ResumeAnalyticsDashboard } from '@/components/activities/resume-analytics-dashboard';

export default function ResumeAnalyticsPage() {
  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight'>Resume Analytics</h1>
            <p className='text-muted-foreground'>
              Analyze and optimize your activity resume performance with detailed insights
            </p>
          </div>
          <div className='flex items-center space-x-2'>
            <Button variant='outline' asChild>
              <Link href='/dashboard/activities/resumes/new'>
                <Icons.plus className='mr-2 h-4 w-4' />
                Create Resume
              </Link>
            </Button>
            <Button variant='outline' asChild>
              <Link href='/dashboard/activities/resumes'>
                <Icons.chevronLeft className='mr-2 h-4 w-4' />
                Back to Resumes
              </Link>
            </Button>
          </div>
        </div>

        {/* Analytics Dashboard */}
        <ResumeAnalyticsDashboard />
      </div>
    </ProfileSetupCheck>
  );
}
