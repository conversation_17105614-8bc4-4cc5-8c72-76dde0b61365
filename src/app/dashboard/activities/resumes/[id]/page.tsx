'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { useApiGet } from '@/lib/api-client';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';

interface Activity {
  id: string;
  activity_type: string;
  organization_name: string;
  position_title?: string;
  description?: string;
  hours_per_week: number;
  weeks_per_year: number;
  leadership_role: boolean;
  awards_recognition?: string;
  participation_grade_levels?: string[];
}

interface ActivityResume {
  id: string;
  title: string;
  description?: string;
  focus_area: string;
  selected_activities: string[];
  template_style: string;
  file_url?: string;
  google_doc_id?: string;
  google_doc_url?: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
  student_profiles: {
    first_name: string;
    last_name: string;
  };
  activities?: Activity[];
}

interface ResumePageProps {
  params: Promise<{ id: string }>;
}

export default function ActivityResumePage({ params }: ResumePageProps) {
  const router = useRouter();
  const [resumeId, setResumeId] = useState<string>('');
  const [generatingDoc, setGeneratingDoc] = useState(false);

  useEffect(() => {
    params.then(({ id }) => setResumeId(id));
  }, [params]);

  const {
    data: resume,
    loading,
    error,
    refetch
  } = useApiGet<ActivityResume>(resumeId ? `/api/activity-resumes/${resumeId}` : null);

  const handleGenerateGoogleDoc = async () => {
    if (!resume) return;

    setGeneratingDoc(true);
    try {
      // Create a document through the documents API
      const response = await fetch('/api/documents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          student_id: resume.student_profiles ?
            (resume as any).student_profiles.id :
            (resume as any).student_id,
          doc_type: 'activity_resume',
          metadata: {
            title: resume.title,
            description: resume.description,
            focus_area: resume.focus_area,
            selected_activities: resume.selected_activities,
            template_style: resume.template_style,
            enhanced_formatting: true
          }
        })
      });

      if (response.ok) {
        const result = await response.json();
        
        // Update the resume with the Google Doc info
        const updateResponse = await fetch(`/api/activity-resumes/${resumeId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            google_doc_id: result.data.google_doc_id,
            google_doc_url: result.data.google_doc_url
          })
        });

        if (updateResponse.ok) {
          refetch();
          // Open the Google Doc
          window.open(result.data.google_doc_url, '_blank');
        }
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || 'Failed to generate Google Doc'}`);
      }
    } catch (error) {
      console.error('Error generating Google Doc:', error);
      alert('Failed to generate Google Doc');
    } finally {
      setGeneratingDoc(false);
    }
  };

  const handleSetDefault = async () => {
    if (!resume) return;

    try {
      const response = await fetch(`/api/activity-resumes/${resumeId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ is_default: true })
      });

      if (response.ok) {
        refetch();
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || 'Failed to set default resume'}`);
      }
    } catch (error) {
      console.error('Error setting default resume:', error);
      alert('Failed to set default resume');
    }
  };

  const handleDelete = async () => {
    if (!resume) return;

    if (!confirm('Are you sure you want to delete this resume version?')) {
      return;
    }

    try {
      const response = await fetch(`/api/activity-resumes/${resumeId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        router.push('/dashboard/activities/resumes');
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || 'Failed to delete resume'}`);
      }
    } catch (error) {
      console.error('Error deleting resume:', error);
      alert('Failed to delete resume');
    }
  };

  if (loading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-center space-x-2'>
          <Icons.spinner className='h-4 w-4 animate-spin' />
          <span>Loading resume...</span>
        </div>
      </div>
    );
  }

  if (error || !resume) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <Card>
          <CardContent className='pt-6'>
            <div className='text-center'>
              <Icons.alertCircle className='mx-auto h-12 w-12 text-red-500' />
              <h3 className='mt-2 text-sm font-medium text-gray-900'>
                Resume not found
              </h3>
              <p className='mt-1 text-sm text-gray-500'>
                {error || 'The requested resume could not be found.'}
              </p>
              <div className='mt-6'>
                <Button asChild>
                  <Link href='/dashboard/activities/resumes'>
                    Back to Resumes
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const activities = resume.activities || [];
  const totalHours = activities.reduce(
    (sum, activity) => sum + (activity.hours_per_week * activity.weeks_per_year),
    0
  );
  const leadershipCount = activities.filter(a => a.leadership_role).length;

  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-4'>
            <div>
              <div className='flex items-center space-x-2'>
                <h1 className='text-3xl font-bold tracking-tight'>{resume.title}</h1>
                {resume.is_default && (
                  <Badge variant='default'>
                    <Icons.star className='mr-1 h-3 w-3' />
                    Default
                  </Badge>
                )}
              </div>
              <p className='text-muted-foreground'>
                {resume.description || 'Activity resume version'}
              </p>
            </div>
          </div>
          <div className='flex gap-2'>
            <Button variant='outline' asChild>
              <Link href='/dashboard/activities/resumes'>
                <Icons.chevronLeft className='mr-2 h-4 w-4' />
                Back to Resumes
              </Link>
            </Button>
            <Button variant='outline' asChild>
              <Link href={`/dashboard/activities/resumes/${resumeId}/edit`}>
                <Icons.edit className='mr-2 h-4 w-4' />
                Edit
              </Link>
            </Button>
          </div>
        </div>

        {/* Resume Stats */}
        <div className='grid gap-4 md:grid-cols-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Activities</CardTitle>
              <Icons.fileText className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{activities.length}</div>
              <p className='text-xs text-muted-foreground'>
                Selected activities
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Total Hours</CardTitle>
              <Icons.clock className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{totalHours.toLocaleString()}</div>
              <p className='text-xs text-muted-foreground'>
                Hours annually
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Leadership</CardTitle>
              <Icons.crown className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{leadershipCount}</div>
              <p className='text-xs text-muted-foreground'>
                Leadership roles
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Focus Area</CardTitle>
              <Icons.target className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-lg font-bold capitalize'>
                {resume.focus_area.replace('_', ' ')}
              </div>
              <p className='text-xs text-muted-foreground'>
                Application focus
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Resume Actions</CardTitle>
            <CardDescription>
              Generate documents and manage this resume version
            </CardDescription>
          </CardHeader>
          <CardContent className='flex flex-wrap gap-2'>
            {resume.google_doc_url ? (
              <Button variant='outline' asChild>
                <a
                  href={resume.google_doc_url}
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  <Icons.externalLink className='mr-2 h-4 w-4' />
                  Open Google Doc
                </a>
              </Button>
            ) : (
              <Button
                variant='outline'
                onClick={handleGenerateGoogleDoc}
                disabled={generatingDoc}
              >
                {generatingDoc ? (
                  <>
                    <Icons.spinner className='mr-2 h-4 w-4 animate-spin' />
                    Generating...
                  </>
                ) : (
                  <>
                    <Icons.fileText className='mr-2 h-4 w-4' />
                    Generate Google Doc
                  </>
                )}
              </Button>
            )}
            
            {!resume.is_default && (
              <Button variant='outline' onClick={handleSetDefault}>
                <Icons.star className='mr-2 h-4 w-4' />
                Set as Default
              </Button>
            )}
            
            <Button variant='outline' onClick={handleDelete}>
              <Icons.trash className='mr-2 h-4 w-4' />
              Delete Resume
            </Button>
          </CardContent>
        </Card>

        {/* Activities List */}
        <Card>
          <CardHeader>
            <CardTitle>Selected Activities</CardTitle>
            <CardDescription>
              Activities included in this resume version
            </CardDescription>
          </CardHeader>
          <CardContent>
            {activities.length === 0 ? (
              <div className='text-center py-8'>
                <Icons.fileText className='mx-auto h-12 w-12 text-gray-400' />
                <h3 className='mt-2 text-sm font-medium text-gray-900'>
                  No activities selected
                </h3>
                <p className='mt-1 text-sm text-gray-500'>
                  Edit this resume to add activities.
                </p>
              </div>
            ) : (
              <div className='space-y-4'>
                {activities.map((activity) => (
                  <div
                    key={activity.id}
                    className='flex items-start justify-between rounded-lg border p-4'
                  >
                    <div className='flex-1'>
                      <div className='flex items-center space-x-2'>
                        <h4 className='font-medium'>{activity.organization_name}</h4>
                        {activity.leadership_role && (
                          <Icons.crown className='h-4 w-4 text-yellow-500' />
                        )}
                        <Badge variant='secondary' className='text-xs'>
                          {activity.activity_type}
                        </Badge>
                      </div>
                      {activity.position_title && (
                        <p className='text-sm text-muted-foreground mt-1'>
                          {activity.position_title}
                        </p>
                      )}
                      {activity.description && (
                        <p className='text-sm text-muted-foreground mt-2'>
                          {activity.description}
                        </p>
                      )}
                      {activity.awards_recognition && (
                        <p className='text-sm text-blue-600 mt-2'>
                          <Icons.award className='inline h-3 w-3 mr-1' />
                          {activity.awards_recognition}
                        </p>
                      )}
                    </div>
                    <div className='text-right text-sm text-muted-foreground'>
                      <p>{activity.hours_per_week}h/week</p>
                      <p>{activity.weeks_per_year} weeks/year</p>
                      <p className='font-medium'>
                        {(activity.hours_per_week * activity.weeks_per_year).toLocaleString()}h total
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </ProfileSetupCheck>
  );
}
