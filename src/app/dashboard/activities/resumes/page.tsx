'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { useApiGet } from '@/lib/api-client';
import { useCurrentUser } from '@/hooks/use-current-user';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';

interface ActivityResume {
  id: string;
  title: string;
  description?: string;
  focus_area: string;
  selected_activities: string[];
  template_style: string;
  file_url?: string;
  google_doc_id?: string;
  google_doc_url?: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
  student_profiles: {
    first_name: string;
    last_name: string;
  };
  activities?: Array<{
    id: string;
    activity_type: string;
    organization_name: string;
    position_title?: string;
    hours_per_week: number;
    weeks_per_year: number;
    leadership_role: boolean;
  }>;
}

export default function ActivityResumesPage() {
  const { user } = useCurrentUser();
  const [selectedResume, setSelectedResume] = useState<string | null>(null);

  const {
    data: resumes,
    loading,
    error,
    refetch
  } = useApiGet<ActivityResume[]>('/api/activity-resumes');

  const resumeList = resumes || [];

  // Focus area options for filtering
  const focusAreas = Array.from(new Set(resumeList.map(r => r.focus_area)));

  const handleDeleteResume = async (resumeId: string) => {
    if (!confirm('Are you sure you want to delete this resume version?')) {
      return;
    }

    try {
      const response = await fetch(`/api/activity-resumes/${resumeId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        refetch();
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || 'Failed to delete resume'}`);
      }
    } catch (error) {
      console.error('Error deleting resume:', error);
      alert('Failed to delete resume');
    }
  };

  const handleSetDefault = async (resumeId: string) => {
    try {
      const response = await fetch(`/api/activity-resumes/${resumeId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ is_default: true })
      });

      if (response.ok) {
        refetch();
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || 'Failed to set default resume'}`);
      }
    } catch (error) {
      console.error('Error setting default resume:', error);
      alert('Failed to set default resume');
    }
  };

  if (loading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-center space-x-2'>
          <Icons.spinner className='h-4 w-4 animate-spin' />
          <span>Loading activity resumes...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <Card>
          <CardContent className='pt-6'>
            <div className='text-center'>
              <Icons.alertCircle className='mx-auto h-12 w-12 text-red-500' />
              <h3 className='mt-2 text-sm font-medium text-gray-900'>
                Error loading resumes
              </h3>
              <p className='mt-1 text-sm text-gray-500'>{error}</p>
              <div className='mt-6'>
                <Button onClick={() => refetch()}>Try Again</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight'>Activity Resumes</h1>
            <p className='text-muted-foreground'>
              Create multiple versions of your activity resume for different applications
            </p>
          </div>
          <div className='flex gap-2'>
            <Button asChild variant='outline'>
              <Link href='/dashboard/activities'>
                <Icons.chevronLeft className='mr-2 h-4 w-4' />
                Back to Activities
              </Link>
            </Button>
            <Button asChild>
              <Link href='/dashboard/activities/resumes/new'>
                <Icons.plus className='mr-2 h-4 w-4' />
                Create Resume Version
              </Link>
            </Button>
          </div>
        </div>

        {/* Resume Stats */}
        <div className='grid gap-4 md:grid-cols-3'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Total Versions</CardTitle>
              <Icons.fileText className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{resumeList.length}</div>
              <p className='text-xs text-muted-foreground'>
                Resume versions created
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Focus Areas</CardTitle>
              <Icons.target className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{focusAreas.length}</div>
              <p className='text-xs text-muted-foreground'>
                Different focus areas
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Default Resume</CardTitle>
              <Icons.star className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {resumeList.find(r => r.is_default) ? '1' : '0'}
              </div>
              <p className='text-xs text-muted-foreground'>
                Default version set
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Resume List */}
        {resumeList.length === 0 ? (
          <Card>
            <CardContent className='pt-6'>
              <div className='text-center'>
                <Icons.fileText className='mx-auto h-12 w-12 text-gray-400' />
                <h3 className='mt-2 text-sm font-medium text-gray-900'>
                  No resume versions yet
                </h3>
                <p className='mt-1 text-sm text-gray-500'>
                  Create your first activity resume version to get started.
                </p>
                <div className='mt-6'>
                  <Button asChild>
                    <Link href='/dashboard/activities/resumes/new'>
                      <Icons.plus className='mr-2 h-4 w-4' />
                      Create First Resume
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
            {resumeList.map((resume) => (
              <Card key={resume.id} className='relative'>
                <CardHeader>
                  <div className='flex items-start justify-between'>
                    <div className='flex-1'>
                      <CardTitle className='text-lg'>{resume.title}</CardTitle>
                      <CardDescription className='mt-1'>
                        {resume.description || 'No description'}
                      </CardDescription>
                    </div>
                    {resume.is_default && (
                      <Badge variant='default' className='ml-2'>
                        <Icons.star className='mr-1 h-3 w-3' />
                        Default
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='space-y-3'>
                    <div className='flex items-center justify-between text-sm'>
                      <span className='text-muted-foreground'>Focus Area:</span>
                      <Badge variant='secondary'>{resume.focus_area}</Badge>
                    </div>
                    <div className='flex items-center justify-between text-sm'>
                      <span className='text-muted-foreground'>Activities:</span>
                      <span className='font-medium'>
                        {resume.selected_activities.length}
                      </span>
                    </div>
                    <div className='flex items-center justify-between text-sm'>
                      <span className='text-muted-foreground'>Template:</span>
                      <span className='font-medium capitalize'>
                        {resume.template_style}
                      </span>
                    </div>
                    {resume.google_doc_url && (
                      <div className='flex items-center justify-between text-sm'>
                        <span className='text-muted-foreground'>Google Doc:</span>
                        <Button
                          variant='link'
                          size='sm'
                          className='h-auto p-0'
                          asChild
                        >
                          <a
                            href={resume.google_doc_url}
                            target='_blank'
                            rel='noopener noreferrer'
                          >
                            <Icons.externalLink className='h-3 w-3' />
                          </a>
                        </Button>
                      </div>
                    )}
                  </div>

                  <div className='mt-4 flex gap-2'>
                    <Button
                      variant='outline'
                      size='sm'
                      className='flex-1'
                      asChild
                    >
                      <Link href={`/dashboard/activities/resumes/${resume.id}`}>
                        <Icons.edit className='mr-1 h-3 w-3' />
                        Edit
                      </Link>
                    </Button>
                    {!resume.is_default && (
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handleSetDefault(resume.id)}
                      >
                        <Icons.star className='h-3 w-3' />
                      </Button>
                    )}
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => handleDeleteResume(resume.id)}
                    >
                      <Icons.trash className='h-3 w-3' />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </ProfileSetupCheck>
  );
}
