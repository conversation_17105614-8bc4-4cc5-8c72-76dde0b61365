'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { useApiGet } from '@/lib/api-client';
import { useCurrentUser } from '@/hooks/use-current-user';

interface Activity {
  id: string;
  name: string;
  category: string;
  organization?: string;
  position?: string;
  yearsActive?: string;
  hoursPerWeek: number;
  weeksPerYear: number;
  description?: string;
  achievements?: string[];
}

interface StudentActivityStats {
  personal: {
    totalActivities: number;
    totalHours: number;
    leadershipRoles: number;
    categories: string[];
  };
  breakdown: Array<{
    id: string;
    name: string;
    category: string;
    position: string;
    hoursPerWeek: number;
    weeksPerYear: number;
    totalHours: number;
    achievements: string[];
  }>;
  comparison: {
    averageActivitiesPerStudent: number;
    averageHoursPerStudent: number;
    percentileRank: number;
  };
}

export default function ActivitiesPage() {
  const [studentId, setStudentId] = useState<string | null>(null);

  // Get student profile ID
  const { data: studentProfile, loading: profileLoading } = useApiGet<{
    id: string;
  }>('/api/profile/student');

  const {
    data: activityStats,
    loading: statsLoading,
    fetch: fetchStats
  } = useApiGet<StudentActivityStats>(
    studentId ? `/api/stats/activities?student_id=${studentId}` : ''
  );

  useEffect(() => {
    if (studentProfile?.id) {
      setStudentId(studentProfile.id);
    }
  }, [studentProfile]);

  useEffect(() => {
    if (studentId) {
      fetchStats();
    }
  }, [studentId, fetchStats]);

  const loading = profileLoading || statsLoading;
  const activities: Activity[] =
    activityStats?.breakdown.map((activity) => ({
      id: activity.id,
      name: activity.name,
      category: activity.category,
      position: activity.position,
      hoursPerWeek: activity.hoursPerWeek,
      weeksPerYear: activity.weeksPerYear,
      description: '', // Would need to be added to API
      achievements: activity.achievements
    })) || [];

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'leadership':
        return 'bg-blue-100 text-blue-800';
      case 'athletics':
        return 'bg-green-100 text-green-800';
      case 'community service':
        return 'bg-purple-100 text-purple-800';
      case 'academic':
        return 'bg-yellow-100 text-yellow-800';
      case 'environmental':
        return 'bg-emerald-100 text-emerald-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const totalHours = activityStats?.personal.totalHours || 0;
  const totalActivities = activityStats?.personal.totalActivities || 0;
  const leadershipRoles = activityStats?.personal.leadershipRoles || 0;
  const categories = activityStats?.personal.categories || [];

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>
            Activities & Achievements
          </h2>
          <p className='text-muted-foreground'>
            Showcase your extracurricular activities and accomplishments
          </p>
        </div>
        <Button asChild>
          <Link href='/dashboard/activities/add'>
            <Icons.add className='mr-2 h-4 w-4' />
            Add Activity
          </Link>
        </Button>
      </div>

      {/* Overview Stats */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Activities
            </CardTitle>
            <Icons.trophy className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className='flex items-center justify-center py-2'>
                <Icons.spinner className='h-4 w-4 animate-spin' />
              </div>
            ) : (
              <>
                <div className='text-2xl font-bold'>{totalActivities}</div>
                <p className='text-muted-foreground text-xs'>
                  Across {categories.length} categories
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Leadership Roles
            </CardTitle>
            <Icons.users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className='flex items-center justify-center py-2'>
                <Icons.spinner className='h-4 w-4 animate-spin' />
              </div>
            ) : (
              <>
                <div className='text-2xl font-bold'>{leadershipRoles}</div>
                <p className='text-muted-foreground text-xs'>
                  Leadership positions held
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Hours</CardTitle>
            <Icons.calendar className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className='flex items-center justify-center py-2'>
                <Icons.spinner className='h-4 w-4 animate-spin' />
              </div>
            ) : (
              <>
                <div className='text-2xl font-bold'>
                  {totalHours.toLocaleString()}
                </div>
                <p className='text-muted-foreground text-xs'>
                  Hours of involvement
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Percentile Rank
            </CardTitle>
            <Icons.trendingUp className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className='flex items-center justify-center py-2'>
                <Icons.spinner className='h-4 w-4 animate-spin' />
              </div>
            ) : (
              <>
                <div className='text-2xl font-bold'>
                  {activityStats?.comparison.percentileRank || 0}%
                </div>
                <p className='text-muted-foreground text-xs'>
                  Compared to peers
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Activities List */}
      <div className='space-y-4'>
        {loading ? (
          <Card>
            <CardContent className='flex items-center justify-center py-8'>
              <div className='flex items-center gap-2'>
                <Icons.spinner className='h-6 w-6 animate-spin' />
                <span>Loading activities...</span>
              </div>
            </CardContent>
          </Card>
        ) : activities.length === 0 ? (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-8'>
              <Icons.trophy className='text-muted-foreground mb-4 h-12 w-12' />
              <h3 className='mb-2 text-lg font-medium'>No activities yet</h3>
              <p className='text-muted-foreground mb-4 text-center'>
                Start building your profile by adding your extracurricular
                activities and achievements.
              </p>
              <Button asChild>
                <Link href='/dashboard/activities/add'>
                  <Icons.add className='mr-2 h-4 w-4' />
                  Add Your First Activity
                </Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          activities.map((activity) => (
            <Card key={activity.id}>
              <CardHeader>
                <div className='flex items-start justify-between'>
                  <div className='space-y-2'>
                    <div className='flex items-center space-x-2'>
                      <CardTitle className='text-lg'>{activity.name}</CardTitle>
                      <Badge className={getCategoryColor(activity.category)}>
                        {activity.category}
                      </Badge>
                    </div>
                    <CardDescription>
                      {activity.position} at {activity.organization} •{' '}
                      {activity.yearsActive}
                    </CardDescription>
                  </div>
                  <Button asChild size='sm' variant='outline'>
                    <Link href={`/dashboard/activities/${activity.id}/edit`}>
                      <Icons.userPen className='mr-2 h-4 w-4' />
                      Edit
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent className='space-y-4'>
                <p className='text-sm'>{activity.description}</p>

                {activity.achievements && activity.achievements.length > 0 && (
                  <div>
                    <h4 className='mb-2 text-sm font-medium'>
                      Key Achievements:
                    </h4>
                    <ul className='text-muted-foreground space-y-1 text-sm'>
                      {activity.achievements.map((achievement, index) => (
                        <li key={index} className='flex items-start space-x-2'>
                          <Icons.checkCircle className='mt-0.5 h-4 w-4 flex-shrink-0 text-green-500' />
                          <span>{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className='text-muted-foreground flex items-center justify-between border-t pt-4 text-sm'>
                  <div className='flex items-center space-x-4'>
                    <div className='flex items-center space-x-1'>
                      <Icons.calendar className='h-4 w-4' />
                      <span>{activity.hoursPerWeek} hrs/week</span>
                    </div>
                    <div className='flex items-center space-x-1'>
                      <Icons.calendar className='h-4 w-4' />
                      <span>{activity.weeksPerYear} weeks/year</span>
                    </div>
                  </div>
                  <div className='font-medium'>
                    Total:{' '}
                    {(
                      activity.hoursPerWeek * activity.weeksPerYear
                    ).toLocaleString()}{' '}
                    hours
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Activity Categories Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Activity Categories</CardTitle>
          <CardDescription>
            Distribution of your extracurricular involvement
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
            {Array.from(new Set(activities.map((a) => a.category))).map(
              (category) => {
                const categoryActivities = activities.filter(
                  (a) => a.category === category
                );
                const categoryHours = categoryActivities.reduce(
                  (sum, a) => sum + a.hoursPerWeek * a.weeksPerYear,
                  0
                );

                return (
                  <div
                    key={category}
                    className='flex items-center justify-between rounded-lg border p-3'
                  >
                    <div className='flex items-center space-x-3'>
                      <Icons.trophy className='h-5 w-5 text-yellow-500' />
                      <div>
                        <p className='font-medium'>{category}</p>
                        <p className='text-muted-foreground text-sm'>
                          {categoryActivities.length} activit
                          {categoryActivities.length !== 1 ? 'ies' : 'y'}
                        </p>
                      </div>
                    </div>
                    <div className='text-right'>
                      <p className='font-medium'>
                        {categoryHours.toLocaleString()}
                      </p>
                      <p className='text-muted-foreground text-xs'>hours</p>
                    </div>
                  </div>
                );
              }
            )}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Manage your activity portfolio</CardDescription>
        </CardHeader>
        <CardContent className='flex flex-wrap gap-2'>
          <Button asChild variant='outline'>
            <Link href='/dashboard/activities/resume'>
              <Icons.fileText className='mr-2 h-4 w-4' />
              Generate Activity Resume
            </Link>
          </Button>
          <Button asChild variant='outline'>
            <Link href='/dashboard/activities/import'>
              <Icons.folder className='mr-2 h-4 w-4' />
              Import from Common App
            </Link>
          </Button>
          <Button asChild variant='outline'>
            <Link href='/dashboard/activities/tips'>
              <Icons.help className='mr-2 h-4 w-4' />
              Activity Tips
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
