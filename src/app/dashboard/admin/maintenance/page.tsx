'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useCurrentUser } from '@/hooks/use-current-user';

interface MaintenanceTask {
  id: string;
  name: string;
  description: string;
  category: 'database' | 'files' | 'cache' | 'logs' | 'security';
  status: 'idle' | 'running' | 'completed' | 'failed';
  lastRun?: Date;
  duration?: string;
  impact: 'low' | 'medium' | 'high';
}

export default function AdminMaintenancePage() {
  const { user: currentUser, loading: userLoading, isAdmin } = useCurrentUser();
  const [runningTasks, setRunningTasks] = useState<Set<string>>(new Set());
  const [taskProgress, setTaskProgress] = useState<Record<string, number>>({});

  // Show loading state while checking user role
  if (userLoading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='flex items-center gap-2'>
              <Icons.spinner className='h-4 w-4 animate-spin' />
              <span>Loading...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show access denied for non-admin users
  if (!isAdmin) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <Icons.settings className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
            <h3 className='text-lg font-semibold'>Access Restricted</h3>
            <p className='text-muted-foreground mt-2 text-sm'>
              This page is only available to administrators.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const maintenanceTasks: MaintenanceTask[] = [
    {
      id: 'db-optimize',
      name: 'Database Optimization',
      description: 'Optimize database tables and rebuild indexes for better performance',
      category: 'database',
      status: 'idle',
      lastRun: new Date('2025-07-04T02:00:00Z'),
      duration: '15 minutes',
      impact: 'medium'
    },
    {
      id: 'cache-clear',
      name: 'Clear System Cache',
      description: 'Clear application cache and temporary files',
      category: 'cache',
      status: 'idle',
      lastRun: new Date('2025-07-05T01:00:00Z'),
      duration: '2 minutes',
      impact: 'low'
    },
    {
      id: 'log-cleanup',
      name: 'Log File Cleanup',
      description: 'Archive old log files and clean up disk space',
      category: 'logs',
      status: 'idle',
      lastRun: new Date('2025-07-03T03:00:00Z'),
      duration: '5 minutes',
      impact: 'low'
    },
    {
      id: 'file-cleanup',
      name: 'Orphaned Files Cleanup',
      description: 'Remove orphaned files and unused uploads',
      category: 'files',
      status: 'idle',
      lastRun: new Date('2025-07-02T02:30:00Z'),
      duration: '10 minutes',
      impact: 'low'
    },
    {
      id: 'security-scan',
      name: 'Security Vulnerability Scan',
      description: 'Scan system for security vulnerabilities and outdated dependencies',
      category: 'security',
      status: 'idle',
      lastRun: new Date('2025-07-01T04:00:00Z'),
      duration: '20 minutes',
      impact: 'high'
    },
    {
      id: 'db-backup-verify',
      name: 'Backup Integrity Check',
      description: 'Verify the integrity of recent database backups',
      category: 'database',
      status: 'idle',
      lastRun: new Date('2025-07-04T05:00:00Z'),
      duration: '8 minutes',
      impact: 'low'
    }
  ];

  const runMaintenanceTask = async (taskId: string) => {
    setRunningTasks(prev => new Set(prev).add(taskId));
    setTaskProgress(prev => ({ ...prev, [taskId]: 0 }));

    const task = maintenanceTasks.find(t => t.id === taskId);
    toast.info(`Starting ${task?.name}...`);

    // Simulate task progress
    const interval = setInterval(() => {
      setTaskProgress(prev => {
        const currentProgress = prev[taskId] || 0;
        if (currentProgress >= 100) {
          clearInterval(interval);
          setRunningTasks(prevRunning => {
            const newRunning = new Set(prevRunning);
            newRunning.delete(taskId);
            return newRunning;
          });
          toast.success(`${task?.name} completed successfully`);
          return prev;
        }
        return { ...prev, [taskId]: currentProgress + 10 };
      });
    }, 300);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'database':
        return <Icons.database className='h-4 w-4' />;
      case 'files':
        return <Icons.folder className='h-4 w-4' />;
      case 'cache':
        return <Icons.settings className='h-4 w-4' />;
      case 'logs':
        return <Icons.fileText className='h-4 w-4' />;
      case 'security':
        return <Icons.shield className='h-4 w-4' />;
      default:
        return <Icons.settings className='h-4 w-4' />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'database':
        return 'bg-blue-100 text-blue-800';
      case 'files':
        return 'bg-green-100 text-green-800';
      case 'cache':
        return 'bg-yellow-100 text-yellow-800';
      case 'logs':
        return 'bg-purple-100 text-purple-800';
      case 'security':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'low':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'high':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const runAllTasks = () => {
    maintenanceTasks.forEach(task => {
      if (!runningTasks.has(task.id)) {
        setTimeout(() => runMaintenanceTask(task.id), Math.random() * 2000);
      }
    });
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>System Maintenance</h2>
          <p className='text-muted-foreground'>
            Perform system maintenance tasks and monitor system health
          </p>
        </div>
        <div className='flex gap-2'>
          <Button variant='outline'>
            <Icons.calendar className='mr-2 h-4 w-4' />
            Schedule Tasks
          </Button>
          <Button onClick={runAllTasks} disabled={runningTasks.size > 0}>
            <Icons.arrowRight className='mr-2 h-4 w-4' />
            Run All Tasks
          </Button>
        </div>
      </div>

      {/* System Status */}
      <div className='grid gap-4 md:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>System Health</CardTitle>
            <Icons.checkCircle className='h-4 w-4 text-green-600' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-green-600'>Healthy</div>
            <p className='text-xs text-muted-foreground'>
              All systems operational
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Disk Usage</CardTitle>
            <Icons.folder className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>68%</div>
            <p className='text-xs text-muted-foreground'>
              340 GB of 500 GB used
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Memory Usage</CardTitle>
            <Icons.barChart className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>45%</div>
            <p className='text-xs text-muted-foreground'>
              7.2 GB of 16 GB used
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Active Tasks</CardTitle>
            <Icons.barChart className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{runningTasks.size}</div>
            <p className='text-xs text-muted-foreground'>
              Currently running
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Maintenance Alert */}
      {runningTasks.size > 0 && (
        <Alert>
          <Icons.alertCircle className='h-4 w-4' />
          <AlertDescription>
            {runningTasks.size} maintenance task{runningTasks.size > 1 ? 's' : ''} currently running. 
            Some system features may be temporarily affected.
          </AlertDescription>
        </Alert>
      )}

      {/* Maintenance Tasks */}
      <Card>
        <CardHeader>
          <CardTitle>Maintenance Tasks</CardTitle>
          <CardDescription>
            Available system maintenance and optimization tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {maintenanceTasks.map((task) => {
              const isRunning = runningTasks.has(task.id);
              const progress = taskProgress[task.id] || 0;

              return (
                <div
                  key={task.id}
                  className='flex items-center justify-between rounded-lg border p-4'
                >
                  <div className='flex items-center gap-4'>
                    <div className='flex h-10 w-10 items-center justify-center rounded-lg bg-muted'>
                      {getCategoryIcon(task.category)}
                    </div>
                    <div className='space-y-1'>
                      <div className='flex items-center gap-2'>
                        <h4 className='font-medium'>{task.name}</h4>
                        <Badge className={getCategoryColor(task.category)}>
                          {task.category}
                        </Badge>
                        <Badge className={getImpactColor(task.impact)}>
                          {task.impact} impact
                        </Badge>
                      </div>
                      <p className='text-sm text-muted-foreground'>
                        {task.description}
                      </p>
                      <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                        {task.lastRun && (
                          <span>
                            Last run: {task.lastRun.toLocaleDateString()}
                          </span>
                        )}
                        {task.duration && (
                          <span>Duration: ~{task.duration}</span>
                        )}
                      </div>
                      {isRunning && (
                        <div className='mt-2 space-y-1'>
                          <div className='flex justify-between text-xs'>
                            <span>Progress</span>
                            <span>{progress}%</span>
                          </div>
                          <Progress value={progress} className='w-64' />
                        </div>
                      )}
                    </div>
                  </div>
                  <div className='flex items-center gap-2'>
                    <Button
                      size='sm'
                      onClick={() => runMaintenanceTask(task.id)}
                      disabled={isRunning}
                    >
                      {isRunning ? (
                        <>
                          <Icons.spinner className='mr-2 h-4 w-4 animate-spin' />
                          Running
                        </>
                      ) : (
                        <>
                          <Icons.arrowRight className='mr-2 h-4 w-4' />
                          Run Task
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className='grid gap-4 md:grid-cols-3'>
        <Card>
          <CardHeader>
            <CardTitle className='text-base'>Emergency Actions</CardTitle>
            <CardDescription>
              Critical system maintenance actions
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-2'>
            <Button variant='destructive' className='w-full'>
              <Icons.alertTriangle className='mr-2 h-4 w-4' />
              Emergency Shutdown
            </Button>
            <Button variant='outline' className='w-full'>
              <Icons.arrowRight className='mr-2 h-4 w-4' />
              Restart Services
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='text-base'>System Monitoring</CardTitle>
            <CardDescription>
              Monitor system performance
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-2'>
            <Button variant='outline' className='w-full'>
              <Icons.barChart className='mr-2 h-4 w-4' />
              View System Logs
            </Button>
            <Button variant='outline' className='w-full'>
              <Icons.barChart className='mr-2 h-4 w-4' />
              Performance Metrics
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='text-base'>Maintenance Schedule</CardTitle>
            <CardDescription>
              Automated maintenance settings
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-2'>
            <Button variant='outline' className='w-full'>
              <Icons.calendar className='mr-2 h-4 w-4' />
              Schedule Tasks
            </Button>
            <Button variant='outline' className='w-full'>
              <Icons.settings className='mr-2 h-4 w-4' />
              Configure Alerts
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
