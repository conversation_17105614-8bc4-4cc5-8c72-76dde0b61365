'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { useCurrentUser } from '@/hooks/use-current-user';

interface BackupItem {
  id: string;
  name: string;
  type: 'full' | 'incremental' | 'database' | 'files';
  size: string;
  createdAt: Date;
  status: 'completed' | 'in_progress' | 'failed';
  description: string;
}

export default function AdminBackupPage() {
  const { user: currentUser, loading: userLoading, isAdmin } = useCurrentUser();
  const [isCreatingBackup, setIsCreatingBackup] = useState(false);
  const [backupProgress, setBackupProgress] = useState(0);

  // Show loading state while checking user role
  if (userLoading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='flex items-center gap-2'>
              <Icons.spinner className='h-4 w-4 animate-spin' />
              <span>Loading...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show access denied for non-admin users
  if (!isAdmin) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <Icons.database className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
            <h3 className='text-lg font-semibold'>Access Restricted</h3>
            <p className='text-muted-foreground mt-2 text-sm'>
              This page is only available to administrators.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Mock backup data
  const backups: BackupItem[] = [
    {
      id: '1',
      name: 'Full System Backup - July 5, 2025',
      type: 'full',
      size: '2.4 GB',
      createdAt: new Date('2025-07-05T02:00:00Z'),
      status: 'completed',
      description: 'Complete system backup including database, files, and configurations'
    },
    {
      id: '2',
      name: 'Database Backup - July 4, 2025',
      type: 'database',
      size: '156 MB',
      createdAt: new Date('2025-07-04T02:00:00Z'),
      status: 'completed',
      description: 'Database backup with all user data and application state'
    },
    {
      id: '3',
      name: 'Incremental Backup - July 3, 2025',
      type: 'incremental',
      size: '89 MB',
      createdAt: new Date('2025-07-03T02:00:00Z'),
      status: 'completed',
      description: 'Incremental backup of changed files and data'
    },
    {
      id: '4',
      name: 'Files Backup - July 2, 2025',
      type: 'files',
      size: '1.8 GB',
      createdAt: new Date('2025-07-02T02:00:00Z'),
      status: 'completed',
      description: 'Backup of uploaded files and documents'
    }
  ];

  const handleCreateBackup = async (type: string) => {
    setIsCreatingBackup(true);
    setBackupProgress(0);

    // Simulate backup progress
    const interval = setInterval(() => {
      setBackupProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsCreatingBackup(false);
          toast.success(`${type} backup created successfully`);
          return 100;
        }
        return prev + 10;
      });
    }, 500);
  };

  const handleDownloadBackup = (backup: BackupItem) => {
    toast.success(`Downloading ${backup.name}...`);
    // In a real app, this would trigger a download
  };

  const handleDeleteBackup = (backup: BackupItem) => {
    toast.success(`Backup ${backup.name} deleted`);
    // In a real app, this would delete the backup
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'full':
        return 'bg-blue-100 text-blue-800';
      case 'database':
        return 'bg-green-100 text-green-800';
      case 'incremental':
        return 'bg-yellow-100 text-yellow-800';
      case 'files':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>System Backup</h2>
          <p className='text-muted-foreground'>
            Manage system backups and data recovery
          </p>
        </div>
        <div className='flex gap-2'>
          <Button variant='outline'>
            <Icons.settings className='mr-2 h-4 w-4' />
            Backup Settings
          </Button>
          <Button>
            <Icons.shield className='mr-2 h-4 w-4' />
            Restore Data
          </Button>
        </div>
      </div>

      {/* Backup Creation */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='text-base'>Full Backup</CardTitle>
            <CardDescription className='text-sm'>
              Complete system backup
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              className='w-full'
              onClick={() => handleCreateBackup('Full')}
              disabled={isCreatingBackup}
            >
              <Icons.database className='mr-2 h-4 w-4' />
              Create Full Backup
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='text-base'>Database Backup</CardTitle>
            <CardDescription className='text-sm'>
              Database only backup
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              className='w-full'
              variant='outline'
              onClick={() => handleCreateBackup('Database')}
              disabled={isCreatingBackup}
            >
              <Icons.database className='mr-2 h-4 w-4' />
              Backup Database
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='text-base'>Incremental Backup</CardTitle>
            <CardDescription className='text-sm'>
              Changes since last backup
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              className='w-full'
              variant='outline'
              onClick={() => handleCreateBackup('Incremental')}
              disabled={isCreatingBackup}
            >
              <Icons.plus className='mr-2 h-4 w-4' />
              Incremental Backup
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='text-base'>Files Backup</CardTitle>
            <CardDescription className='text-sm'>
              User files and documents
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              className='w-full'
              variant='outline'
              onClick={() => handleCreateBackup('Files')}
              disabled={isCreatingBackup}
            >
              <Icons.folder className='mr-2 h-4 w-4' />
              Backup Files
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Backup Progress */}
      {isCreatingBackup && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.spinner className='h-5 w-5 animate-spin' />
              Creating Backup...
            </CardTitle>
            <CardDescription>
              Please wait while the backup is being created
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              <div className='flex justify-between text-sm'>
                <span>Progress</span>
                <span>{backupProgress}%</span>
              </div>
              <Progress value={backupProgress} className='w-full' />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Backup History */}
      <Card>
        <CardHeader>
          <CardTitle>Backup History</CardTitle>
          <CardDescription>
            Recent system backups and their status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {backups.map((backup) => (
              <div
                key={backup.id}
                className='flex items-center justify-between rounded-lg border p-4'
              >
                <div className='flex items-center gap-4'>
                  <div className='flex h-10 w-10 items-center justify-center rounded-lg bg-muted'>
                    {backup.type === 'full' && <Icons.database className='h-5 w-5' />}
                    {backup.type === 'database' && <Icons.database className='h-5 w-5' />}
                    {backup.type === 'incremental' && <Icons.plus className='h-5 w-5' />}
                    {backup.type === 'files' && <Icons.folder className='h-5 w-5' />}
                  </div>
                  <div className='space-y-1'>
                    <div className='flex items-center gap-2'>
                      <h4 className='font-medium'>{backup.name}</h4>
                      <Badge className={getTypeColor(backup.type)}>
                        {backup.type}
                      </Badge>
                      <Badge className={getStatusColor(backup.status)}>
                        {backup.status}
                      </Badge>
                    </div>
                    <p className='text-sm text-muted-foreground'>
                      {backup.description}
                    </p>
                    <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                      <span>Size: {backup.size}</span>
                      <span>
                        Created {formatDistanceToNow(backup.createdAt)} ago
                      </span>
                    </div>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <Button
                    size='sm'
                    variant='outline'
                    onClick={() => handleDownloadBackup(backup)}
                  >
                    <Icons.download className='h-4 w-4' />
                  </Button>
                  <Button
                    size='sm'
                    variant='outline'
                    onClick={() => handleDeleteBackup(backup)}
                  >
                    <Icons.trash className='h-4 w-4' />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Backup Statistics */}
      <div className='grid gap-4 md:grid-cols-3'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Backups</CardTitle>
            <Icons.database className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{backups.length}</div>
            <p className='text-xs text-muted-foreground'>
              All backup files
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Size</CardTitle>
            <Icons.folder className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>4.5 GB</div>
            <p className='text-xs text-muted-foreground'>
              Storage used
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Last Backup</CardTitle>
            <Icons.clock className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatDistanceToNow(backups[0]?.createdAt || new Date())}
            </div>
            <p className='text-xs text-muted-foreground'>
              ago
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
