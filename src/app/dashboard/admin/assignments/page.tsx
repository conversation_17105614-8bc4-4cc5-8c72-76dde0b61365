'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Icons } from '@/components/icons';
import { useCurrentUser } from '@/hooks/use-current-user';
import { AssignConsultantDialog } from '@/components/admin/assign-consultant-dialog';
import { toast } from 'sonner';

interface StudentWithAssignment {
  id: string;
  users: {
    id: string;
    email: string;
    profile_data: {
      first_name?: string;
      last_name?: string;
    };
  };
  current_consultant?: {
    id: string;
    users: {
      profile_data: {
        first_name?: string;
        last_name?: string;
      };
    };
  };
  created_at: string;
}

interface ConsultantWithStudents {
  id: string;
  users: {
    id: string;
    email: string;
    profile_data: {
      first_name?: string;
      last_name?: string;
    };
  };
  specialties: string[];
  student_count: number;
  students: StudentWithAssignment[];
}

export default function AdminAssignmentsPage() {
  const { user, loading } = useCurrentUser();
  const [students, setStudents] = useState<StudentWithAssignment[]>([]);
  const [consultants, setConsultants] = useState<ConsultantWithStudents[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedStudent, setSelectedStudent] = useState<StudentWithAssignment | null>(null);
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [assignmentFilter, setAssignmentFilter] = useState<string>('all');

  const fetchData = async () => {
    setIsLoading(true);
    try {
      // Fetch students with their assignments
      const studentsResponse = await fetch('/api/students');
      const studentsResult = await studentsResponse.json();

      // Fetch consultants with their student counts
      const consultantsResponse = await fetch('/api/consultants');
      const consultantsResult = await consultantsResponse.json();

      if (studentsResult.success) {
        setStudents(studentsResult.data);
      }

      if (consultantsResult.success) {
        setConsultants(consultantsResult.data);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load assignment data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAssignConsultant = (student: StudentWithAssignment) => {
    setSelectedStudent(student);
    setIsAssignDialogOpen(true);
  };

  const handleAssignmentComplete = () => {
    fetchData(); // Refresh data after assignment
  };

  const unassignedStudents = students.filter(s => !s.current_consultant);
  const assignedStudents = students.filter(s => s.current_consultant);

  const filteredStudents = students.filter((student) => {
    const studentName = `${student.users.profile_data.first_name || ''} ${student.users.profile_data.last_name || ''}`.trim();
    const matchesSearch = 
      studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.users.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = 
      assignmentFilter === 'all' ||
      (assignmentFilter === 'assigned' && student.current_consultant) ||
      (assignmentFilter === 'unassigned' && !student.current_consultant);

    return matchesSearch && matchesFilter;
  });

  useEffect(() => {
    if (user && user.role === 'admin') {
      fetchData();
    }
  }, [user]);

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading...</div>;
  }

  if (!user || user.role !== 'admin') {
    return <div className="flex items-center justify-center h-64">Access denied</div>;
  }

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Advisor Assignments</h1>
          <p className="text-muted-foreground">
            Manage student-consultant assignments and workload distribution
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Icons.users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{students.length}</div>
            <p className="text-xs text-muted-foreground">All registered students</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assigned Students</CardTitle>
            <Icons.userCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assignedStudents.length}</div>
            <p className="text-xs text-muted-foreground">
              {students.length > 0 ? Math.round((assignedStudents.length / students.length) * 100) : 0}% of total
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unassigned Students</CardTitle>
            <Icons.userX className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{unassignedStudents.length}</div>
            <p className="text-xs text-muted-foreground">Need advisor assignment</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Consultants</CardTitle>
            <Icons.briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{consultants.length}</div>
            <p className="text-xs text-muted-foreground">Available advisors</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <Input
          placeholder="Search students by name or email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
        <Select value={assignmentFilter} onValueChange={setAssignmentFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by assignment" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Students</SelectItem>
            <SelectItem value="assigned">Assigned Students</SelectItem>
            <SelectItem value="unassigned">Unassigned Students</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Students List */}
      <div className="grid gap-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <Icons.spinner className="h-6 w-6 animate-spin" />
          </div>
        ) : filteredStudents.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Icons.users className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="mb-2 text-lg font-semibold">No students found</h3>
              <p className="text-muted-foreground mb-4 text-center">
                {searchTerm || assignmentFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'No students registered yet'}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredStudents.map((student) => {
            const studentName = `${student.users.profile_data.first_name || ''} ${student.users.profile_data.last_name || ''}`.trim();
            const consultantName = student.current_consultant 
              ? `${student.current_consultant.users.profile_data.first_name || ''} ${student.current_consultant.users.profile_data.last_name || ''}`.trim()
              : null;

            return (
              <Card key={student.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{studentName}</CardTitle>
                      <p className="text-muted-foreground">{student.users.email}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      {student.current_consultant ? (
                        <Badge variant="outline" className="text-green-600">
                          <Icons.userCheck className="mr-1 h-3 w-3" />
                          Assigned
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-orange-600">
                          <Icons.userX className="mr-1 h-3 w-3" />
                          Unassigned
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      {consultantName ? (
                        <p className="text-sm">
                          <span className="text-muted-foreground">Advisor: </span>
                          <span className="font-medium">{consultantName}</span>
                        </p>
                      ) : (
                        <p className="text-sm text-muted-foreground">No advisor assigned</p>
                      )}
                      <p className="text-xs text-muted-foreground">
                        Joined: {new Date(student.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => handleAssignConsultant(student)}
                    >
                      <Icons.userCheck className="mr-2 h-4 w-4" />
                      {student.current_consultant ? 'Reassign' : 'Assign'} Advisor
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* Assignment Dialog */}
      <AssignConsultantDialog
        student={selectedStudent}
        isOpen={isAssignDialogOpen}
        onClose={() => setIsAssignDialogOpen(false)}
        onAssignmentComplete={handleAssignmentComplete}
      />
    </div>
  );
}
