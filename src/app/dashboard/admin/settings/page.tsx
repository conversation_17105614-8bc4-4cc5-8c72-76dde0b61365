'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import { toast } from 'sonner';
import { useCurrentUser } from '@/hooks/use-current-user';

export default function AdminSettingsPage() {
  const { user: currentUser, loading: userLoading, isAdmin } = useCurrentUser();

  // Initialize all state hooks at the top level
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('system');
  const [settings, setSettings] = useState({
    system: {
      siteName: 'Lighten Counsel',
      siteDescription: 'College Application Management System',
      maintenanceMode: false,
      registrationEnabled: true,
      emailNotifications: true,
      backupEnabled: true,
      backupFrequency: 'daily'
    },
    security: {
      twoFactorRequired: false,
      sessionTimeout: 30,
      passwordMinLength: 8,
      maxLoginAttempts: 5,
      ipWhitelist: [],
      sslRequired: true,
      lockoutDuration: 15,
      allowGoogleAuth: true
    },
    email: {
      smtpHost: '',
      smtpPort: 587,
      smtpUser: '',
      smtpPassword: '',
      fromEmail: '<EMAIL>',
      fromName: 'Lighten Counsel'
    },
    integrations: {
      googleDocsEnabled: true,
      googleDriveEnabled: true,
      emailServiceEnabled: true,
      analyticsEnabled: true,
      googleWorkspace: {
        enabled: true,
        domain: '',
        adminEmail: ''
      },
      supabase: {
        enabled: true,
        projectUrl: '',
        anonKey: ''
      }
    },
    limits: {
      maxStudentsPerConsultant: 15,
      maxDocumentsPerStudent: 50,
      maxFileSize: 10,
      storageQuota: 1000
    }
  });

  // Show loading state while checking user role
  if (userLoading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='flex items-center gap-2'>
              <Icons.spinner className='h-4 w-4 animate-spin' />
              <span>Loading...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show access denied for non-admin users
  if (!isAdmin) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <Icons.settings className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
            <h3 className='text-lg font-semibold'>Access Restricted</h3>
            <p className='text-muted-foreground mt-2 text-sm'>
              This page is only available to administrators.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const handleSave = (section: string) => {
    // In a real app, this would make an API call
    toast.success(`${section} settings saved successfully`);
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>System Settings</h2>
          <p className='text-muted-foreground'>
            Configure system-wide settings and preferences
          </p>
        </div>
        <div className='flex gap-2'>
          <Button variant='outline'>
            <Icons.download className='mr-2 h-4 w-4' />
            Export Config
          </Button>
          <Button>
            <Icons.plus className='mr-2 h-4 w-4' />
            Import Config
          </Button>
        </div>
      </div>

      <Tabs defaultValue='system' className='space-y-4'>
        <TabsList className='grid w-full grid-cols-4'>
          <TabsTrigger value='system'>System</TabsTrigger>
          <TabsTrigger value='security'>Security</TabsTrigger>
          <TabsTrigger value='integrations'>Integrations</TabsTrigger>
          <TabsTrigger value='limits'>Limits</TabsTrigger>
        </TabsList>

        <TabsContent value='system' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>General System Settings</CardTitle>
              <CardDescription>
                Configure basic system information and behavior
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='siteName'>Site Name</Label>
                  <Input
                    id='siteName'
                    value={settings.system.siteName}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        system: { ...settings.system, siteName: e.target.value }
                      })
                    }
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='backupFrequency'>Backup Frequency</Label>
                  <select
                    id='backupFrequency'
                    className='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background'
                    value={settings.system.backupFrequency}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        system: { ...settings.system, backupFrequency: e.target.value }
                      })
                    }
                  >
                    <option value='hourly'>Hourly</option>
                    <option value='daily'>Daily</option>
                    <option value='weekly'>Weekly</option>
                  </select>
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='siteDescription'>Site Description</Label>
                <Textarea
                  id='siteDescription'
                  value={settings.system.siteDescription}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      system: { ...settings.system, siteDescription: e.target.value }
                    })
                  }
                />
              </div>

              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Maintenance Mode</Label>
                    <p className='text-sm text-muted-foreground'>
                      Temporarily disable access for maintenance
                    </p>
                  </div>
                  <Switch
                    checked={settings.system.maintenanceMode}
                    onCheckedChange={(checked) =>
                      setSettings({
                        ...settings,
                        system: { ...settings.system, maintenanceMode: checked }
                      })
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Registration Enabled</Label>
                    <p className='text-sm text-muted-foreground'>
                      Allow new users to register
                    </p>
                  </div>
                  <Switch
                    checked={settings.system.registrationEnabled}
                    onCheckedChange={(checked) =>
                      setSettings({
                        ...settings,
                        system: { ...settings.system, registrationEnabled: checked }
                      })
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Email Notifications</Label>
                    <p className='text-sm text-muted-foreground'>
                      Send system notifications via email
                    </p>
                  </div>
                  <Switch
                    checked={settings.system.emailNotifications}
                    onCheckedChange={(checked) =>
                      setSettings({
                        ...settings,
                        system: { ...settings.system, emailNotifications: checked }
                      })
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Automatic Backups</Label>
                    <p className='text-sm text-muted-foreground'>
                      Enable automatic system backups
                    </p>
                  </div>
                  <Switch
                    checked={settings.system.backupEnabled}
                    onCheckedChange={(checked) =>
                      setSettings({
                        ...settings,
                        system: { ...settings.system, backupEnabled: checked }
                      })
                    }
                  />
                </div>
              </div>

              <div className='flex justify-end'>
                <Button onClick={() => handleSave('System')}>
                  <Icons.save className='mr-2 h-4 w-4' />
                  Save System Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='security' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Configure authentication and security policies
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='sessionTimeout'>Session Timeout (minutes)</Label>
                  <Input
                    id='sessionTimeout'
                    type='number'
                    value={settings.security.sessionTimeout}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        security: { ...settings.security, sessionTimeout: parseInt(e.target.value) }
                      })
                    }
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='passwordMinLength'>Minimum Password Length</Label>
                  <Input
                    id='passwordMinLength'
                    type='number'
                    value={settings.security.passwordMinLength}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        security: { ...settings.security, passwordMinLength: parseInt(e.target.value) }
                      })
                    }
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='maxLoginAttempts'>Max Login Attempts</Label>
                  <Input
                    id='maxLoginAttempts'
                    type='number'
                    value={settings.security.maxLoginAttempts}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        security: { ...settings.security, maxLoginAttempts: parseInt(e.target.value) }
                      })
                    }
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='lockoutDuration'>Lockout Duration (minutes)</Label>
                  <Input
                    id='lockoutDuration'
                    type='number'
                    value={settings.security.lockoutDuration}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        security: { ...settings.security, lockoutDuration: parseInt(e.target.value) }
                      })
                    }
                  />
                </div>
              </div>

              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Require Two-Factor Authentication</Label>
                    <p className='text-sm text-muted-foreground'>
                      Require 2FA for all user accounts
                    </p>
                  </div>
                  <Switch
                    checked={settings.security.twoFactorRequired}
                    onCheckedChange={(checked) =>
                      setSettings({
                        ...settings,
                        security: { ...settings.security, twoFactorRequired: checked }
                      })
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Allow Google Authentication</Label>
                    <p className='text-sm text-muted-foreground'>
                      Enable Google OAuth login
                    </p>
                  </div>
                  <Switch
                    checked={settings.security.allowGoogleAuth}
                    onCheckedChange={(checked) =>
                      setSettings({
                        ...settings,
                        security: { ...settings.security, allowGoogleAuth: checked }
                      })
                    }
                  />
                </div>
              </div>

              <div className='flex justify-end'>
                <Button onClick={() => handleSave('Security')}>
                  <Icons.save className='mr-2 h-4 w-4' />
                  Save Security Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='integrations' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>Integration Settings</CardTitle>
              <CardDescription>
                Configure third-party service integrations
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <div className='flex items-center gap-2'>
                      <Label>Google Docs Integration</Label>
                      <Badge variant='outline' className='text-green-600'>
                        Active
                      </Badge>
                    </div>
                    <p className='text-sm text-muted-foreground'>
                      Enable Google Docs document creation and editing
                    </p>
                  </div>
                  <Switch
                    checked={settings.integrations.googleDocsEnabled}
                    onCheckedChange={(checked) =>
                      setSettings({
                        ...settings,
                        integrations: { ...settings.integrations, googleDocsEnabled: checked }
                      })
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <div className='flex items-center gap-2'>
                      <Label>Google Drive Integration</Label>
                      <Badge variant='outline' className='text-green-600'>
                        Active
                      </Badge>
                    </div>
                    <p className='text-sm text-muted-foreground'>
                      Enable Google Drive file storage and sharing
                    </p>
                  </div>
                  <Switch
                    checked={settings.integrations.googleDriveEnabled}
                    onCheckedChange={(checked) =>
                      setSettings({
                        ...settings,
                        integrations: { ...settings.integrations, googleDriveEnabled: checked }
                      })
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Email Service</Label>
                    <p className='text-sm text-muted-foreground'>
                      Enable email notifications and communications
                    </p>
                  </div>
                  <Switch
                    checked={settings.integrations.emailServiceEnabled}
                    onCheckedChange={(checked) =>
                      setSettings({
                        ...settings,
                        integrations: { ...settings.integrations, emailServiceEnabled: checked }
                      })
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Analytics Tracking</Label>
                    <p className='text-sm text-muted-foreground'>
                      Enable usage analytics and reporting
                    </p>
                  </div>
                  <Switch
                    checked={settings.integrations.analyticsEnabled}
                    onCheckedChange={(checked) =>
                      setSettings({
                        ...settings,
                        integrations: { ...settings.integrations, analyticsEnabled: checked }
                      })
                    }
                  />
                </div>
              </div>

              <div className='flex justify-end'>
                <Button onClick={() => handleSave('Integrations')}>
                  <Icons.save className='mr-2 h-4 w-4' />
                  Save Integration Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='limits' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>System Limits</CardTitle>
              <CardDescription>
                Configure system resource limits and quotas
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='maxStudentsPerConsultant'>Max Students per Consultant</Label>
                  <Input
                    id='maxStudentsPerConsultant'
                    type='number'
                    value={settings.limits.maxStudentsPerConsultant}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        limits: { ...settings.limits, maxStudentsPerConsultant: parseInt(e.target.value) }
                      })
                    }
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='maxDocumentsPerStudent'>Max Documents per Student</Label>
                  <Input
                    id='maxDocumentsPerStudent'
                    type='number'
                    value={settings.limits.maxDocumentsPerStudent}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        limits: { ...settings.limits, maxDocumentsPerStudent: parseInt(e.target.value) }
                      })
                    }
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='maxFileSize'>Max File Size (MB)</Label>
                  <Input
                    id='maxFileSize'
                    type='number'
                    value={settings.limits.maxFileSize}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        limits: { ...settings.limits, maxFileSize: parseInt(e.target.value) }
                      })
                    }
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='storageQuota'>Storage Quota (GB)</Label>
                  <Input
                    id='storageQuota'
                    type='number'
                    value={settings.limits.storageQuota}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        limits: { ...settings.limits, storageQuota: parseInt(e.target.value) }
                      })
                    }
                  />
                </div>
              </div>

              <div className='flex justify-end'>
                <Button onClick={() => handleSave('Limits')}>
                  <Icons.save className='mr-2 h-4 w-4' />
                  Save Limit Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
