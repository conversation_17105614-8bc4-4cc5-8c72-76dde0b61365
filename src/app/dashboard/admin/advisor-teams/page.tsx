'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Icons } from '@/components/icons';
import { useCurrentUser } from '@/hooks/use-current-user';
import { AssignConsultantDialog } from '@/components/admin/assign-consultant-dialog';
import { toast } from 'sonner';

interface AdvisorAssignment {
  id: string;
  role_type: 'main_advisor' | 'essay_teacher' | 'planning_assistant' | 'subject_specialist';
  start_date: string;
  end_date?: string;
  status: 'active' | 'inactive';
  notes?: string;
  consultant: {
    id: string;
    users: {
      profile_data: {
        first_name?: string;
        last_name?: string;
      };
    };
    specialties: string[];
  };
  assigned_by_user?: {
    profile_data: {
      first_name?: string;
      last_name?: string;
    };
  };
}

interface StudentWithTeam {
  id: string;
  users: {
    id: string;
    email: string;
    profile_data: {
      first_name?: string;
      last_name?: string;
    };
  };
  advisor_assignments: AdvisorAssignment[];
  created_at: string;
}

export default function AdvisorTeamsPage() {
  const { user, loading } = useCurrentUser();
  const [students, setStudents] = useState<StudentWithTeam[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedStudent, setSelectedStudent] = useState<StudentWithTeam | null>(null);
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');

  const fetchStudentsWithTeams = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/students?include_assignments=true');
      const result = await response.json();

      if (result.success) {
        setStudents(result.data);
      } else {
        toast.error('Failed to load student advisor teams');
      }
    } catch (error) {
      console.error('Error fetching student teams:', error);
      toast.error('Failed to load student advisor teams');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAssignAdvisor = (student: StudentWithTeam) => {
    setSelectedStudent(student);
    setIsAssignDialogOpen(true);
  };

  const handleRemoveAssignment = async (assignmentId: string) => {
    try {
      const response = await fetch(`/api/assignments/${assignmentId}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Advisor assignment removed successfully');
        fetchStudentsWithTeams();
      } else {
        toast.error(result.message || 'Failed to remove assignment');
      }
    } catch (error) {
      console.error('Error removing assignment:', error);
      toast.error('Failed to remove assignment');
    }
  };

  const handleAssignmentComplete = () => {
    fetchStudentsWithTeams();
  };

  const getRoleBadge = (roleType: string) => {
    const roleConfig = {
      main_advisor: { label: 'Main Advisor', variant: 'default' as const, icon: Icons.crown },
      essay_teacher: { label: 'Essay Teacher', variant: 'secondary' as const, icon: Icons.fileText },
      planning_assistant: { label: 'Planning Assistant', variant: 'outline' as const, icon: Icons.calendar },
      subject_specialist: { label: 'Subject Specialist', variant: 'outline' as const, icon: Icons.bookOpen }
    };

    const config = roleConfig[roleType as keyof typeof roleConfig] || roleConfig.main_advisor;
    const IconComponent = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <IconComponent className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const filteredStudents = students.filter((student) => {
    const studentName = `${student.users.profile_data.first_name || ''} ${student.users.profile_data.last_name || ''}`.trim();
    const matchesSearch = 
      studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.users.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = 
      roleFilter === 'all' ||
      student.advisor_assignments.some(assignment => assignment.role_type === roleFilter && assignment.status === 'active');

    return matchesSearch && matchesRole;
  });

  useEffect(() => {
    if (user && user.role === 'admin') {
      fetchStudentsWithTeams();
    }
  }, [user]);

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading...</div>;
  }

  if (!user || user.role !== 'admin') {
    return <div className="flex items-center justify-center h-64">Access denied</div>;
  }

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Advisor Teams</h1>
          <p className="text-muted-foreground">
            Manage student advisor teams with main advisors and specialized support staff
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Icons.users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{students.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">With Main Advisor</CardTitle>
            <Icons.crown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {students.filter(s => s.advisor_assignments.some(a => a.role_type === 'main_advisor' && a.status === 'active')).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">With Team Support</CardTitle>
            <Icons.users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {students.filter(s => s.advisor_assignments.filter(a => a.status === 'active').length > 1).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Assignments</CardTitle>
            <Icons.userCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {students.reduce((total, s) => total + s.advisor_assignments.filter(a => a.status === 'active').length, 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <Input
          placeholder="Search students by name or email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
        <Select value={roleFilter} onValueChange={setRoleFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by advisor role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Students</SelectItem>
            <SelectItem value="main_advisor">With Main Advisor</SelectItem>
            <SelectItem value="essay_teacher">With Essay Teacher</SelectItem>
            <SelectItem value="planning_assistant">With Planning Assistant</SelectItem>
            <SelectItem value="subject_specialist">With Subject Specialist</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Students List */}
      <div className="grid gap-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <Icons.spinner className="h-6 w-6 animate-spin" />
          </div>
        ) : filteredStudents.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Icons.users className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="mb-2 text-lg font-semibold">No students found</h3>
              <p className="text-muted-foreground mb-4 text-center">
                {searchTerm || roleFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'No students registered yet'}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredStudents.map((student) => {
            const studentName = `${student.users.profile_data.first_name || ''} ${student.users.profile_data.last_name || ''}`.trim();
            const activeAssignments = student.advisor_assignments.filter(a => a.status === 'active');
            const mainAdvisor = activeAssignments.find(a => a.role_type === 'main_advisor');

            return (
              <Card key={student.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{studentName}</CardTitle>
                      <p className="text-muted-foreground">{student.users.email}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      {mainAdvisor ? (
                        <Badge variant="outline" className="text-green-600">
                          <Icons.crown className="mr-1 h-3 w-3" />
                          Has Main Advisor
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-orange-600">
                          <Icons.alertTriangle className="mr-1 h-3 w-3" />
                          No Main Advisor
                        </Badge>
                      )}
                      <Badge variant="outline">
                        {activeAssignments.length} advisor{activeAssignments.length !== 1 ? 's' : ''}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Current Advisor Team */}
                    {activeAssignments.length > 0 ? (
                      <div>
                        <h4 className="text-sm font-medium mb-2">Current Advisor Team</h4>
                        <div className="space-y-2">
                          {activeAssignments.map((assignment) => {
                            const advisorName = `${assignment.consultant.users.profile_data.first_name || ''} ${assignment.consultant.users.profile_data.last_name || ''}`.trim();
                            return (
                              <div key={assignment.id} className="flex items-center justify-between p-2 border rounded">
                                <div className="flex items-center gap-3">
                                  {getRoleBadge(assignment.role_type)}
                                  <div>
                                    <p className="font-medium">{advisorName}</p>
                                    {assignment.consultant.specialties.length > 0 && (
                                      <p className="text-xs text-muted-foreground">
                                        {assignment.consultant.specialties.slice(0, 2).join(', ')}
                                      </p>
                                    )}
                                  </div>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRemoveAssignment(assignment.id)}
                                >
                                  <Icons.x className="h-4 w-4" />
                                </Button>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">No advisors assigned</p>
                    )}

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleAssignAdvisor(student)}
                      >
                        <Icons.userPlus className="mr-2 h-4 w-4" />
                        Add Advisor
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* Assignment Dialog */}
      <AssignConsultantDialog
        student={selectedStudent}
        isOpen={isAssignDialogOpen}
        onClose={() => setIsAssignDialogOpen(false)}
        onAssignmentComplete={handleAssignmentComplete}
      />
    </div>
  );
}
