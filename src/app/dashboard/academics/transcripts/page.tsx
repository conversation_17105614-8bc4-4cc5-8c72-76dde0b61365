'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';
import { useCurrentUser } from '@/hooks/use-current-user';

interface TranscriptRecord {
  id: string;
  academic_year: string;
  quarter: string;
  gpa: number;
  file_url?: string;
  upload_date: string;
  status: 'uploaded' | 'pending' | 'verified';
  courses?: Course[];
}

interface Course {
  name: string;
  grade: string;
  credits: number;
}

export default function TranscriptsPage() {
  const { user } = useCurrentUser();
  const [transcripts, setTranscripts] = useState<TranscriptRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTranscripts();
  }, []);

  const fetchTranscripts = async () => {
    try {
      // Mock data - in real app, this would fetch from API
      const mockData: TranscriptRecord[] = [
        {
          id: '1',
          academic_year: '2024-2025',
          quarter: 'Fall',
          gpa: 3.85,
          file_url: '/transcripts/fall-2024.pdf',
          upload_date: '2024-10-15',
          status: 'verified',
          courses: [
            { name: 'AP Calculus BC', grade: 'A', credits: 1.0 },
            { name: 'AP Physics C', grade: 'A-', credits: 1.0 },
            { name: 'AP English Literature', grade: 'A', credits: 1.0 },
            { name: 'AP US History', grade: 'A+', credits: 1.0 },
            { name: 'Spanish IV', grade: 'A', credits: 1.0 }
          ]
        },
        {
          id: '2',
          academic_year: '2023-2024',
          quarter: 'Spring',
          gpa: 3.92,
          file_url: '/transcripts/spring-2024.pdf',
          upload_date: '2024-06-10',
          status: 'verified'
        },
        {
          id: '3',
          academic_year: '2023-2024',
          quarter: 'Fall',
          gpa: 3.78,
          file_url: '/transcripts/fall-2023.pdf',
          upload_date: '2024-01-15',
          status: 'verified'
        }
      ];
      setTranscripts(mockData);
    } catch (err) {
      setError('Failed to fetch transcripts');
      console.error('Error fetching transcripts:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'bg-green-100 text-green-800';
      case 'uploaded':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateCumulativeGPA = () => {
    if (transcripts.length === 0) return 0;
    const totalGPA = transcripts.reduce(
      (sum, transcript) => sum + transcript.gpa,
      0
    );
    return (totalGPA / transcripts.length).toFixed(2);
  };

  if (loading) {
    return (
      <ProfileSetupCheck requireStudentProfile={true}>
        <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
          <div className='flex h-64 items-center justify-center'>
            <Icons.spinner className='h-8 w-8 animate-spin' />
          </div>
        </div>
      </ProfileSetupCheck>
    );
  }

  if (error) {
    return (
      <ProfileSetupCheck requireStudentProfile={true}>
        <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
          <div className='flex h-64 items-center justify-center'>
            <div className='text-center'>
              <Icons.alertCircle className='mx-auto mb-4 h-12 w-12 text-red-500' />
              <h3 className='text-lg font-semibold'>
                Error Loading Transcripts
              </h3>
              <p className='text-muted-foreground mt-2'>{error}</p>
              <Button onClick={fetchTranscripts} className='mt-4'>
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </ProfileSetupCheck>
    );
  }

  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* Header */}
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>
              GPA & Transcripts
            </h2>
            <p className='text-muted-foreground'>
              Manage your academic transcripts and GPA records
            </p>
          </div>
          <Button>
            <Icons.plus className='mr-2 h-4 w-4' />
            Upload Transcript
          </Button>
        </div>

        {/* GPA Overview */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Cumulative GPA
              </CardTitle>
              <Icons.trendingUp className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {calculateCumulativeGPA()}
              </div>
              <p className='text-muted-foreground text-xs'>
                Based on {transcripts.length} transcript
                {transcripts.length !== 1 ? 's' : ''}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Latest GPA</CardTitle>
              <Icons.calendar className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {transcripts.length > 0 ? transcripts[0].gpa.toFixed(2) : 'N/A'}
              </div>
              <p className='text-muted-foreground text-xs'>
                {transcripts.length > 0
                  ? `${transcripts[0].quarter} ${transcripts[0].academic_year}`
                  : 'No data'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Transcripts</CardTitle>
              <Icons.fileText className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{transcripts.length}</div>
              <p className='text-muted-foreground text-xs'>
                {transcripts.filter((t) => t.status === 'verified').length}{' '}
                verified
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Status</CardTitle>
              <Icons.checkCircle className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold text-green-600'>Complete</div>
              <p className='text-muted-foreground text-xs'>
                All transcripts verified
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Transcripts List */}
        <Card>
          <CardHeader>
            <CardTitle>Academic Transcripts</CardTitle>
            <CardDescription>
              Your official academic records by semester
            </CardDescription>
          </CardHeader>
          <CardContent>
            {transcripts.length === 0 ? (
              <div className='py-8 text-center'>
                <Icons.fileText className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
                <h3 className='mb-2 text-lg font-medium'>No Transcripts Yet</h3>
                <p className='text-muted-foreground mb-4'>
                  Upload your academic transcripts to track your GPA progress
                </p>
                <Button>
                  <Icons.plus className='mr-2 h-4 w-4' />
                  Upload Your First Transcript
                </Button>
              </div>
            ) : (
              <div className='space-y-4'>
                {transcripts.map((transcript) => (
                  <div
                    key={transcript.id}
                    className='flex items-center justify-between rounded-lg border p-4'
                  >
                    <div className='flex items-center space-x-4'>
                      <Icons.fileText className='h-8 w-8 text-blue-500' />
                      <div>
                        <h4 className='font-medium'>
                          {transcript.quarter} {transcript.academic_year}
                        </h4>
                        <p className='text-muted-foreground text-sm'>
                          GPA: {transcript.gpa.toFixed(2)} • Uploaded{' '}
                          {new Date(
                            transcript.upload_date
                          ).toLocaleDateString()}
                        </p>
                        {transcript.courses && (
                          <p className='text-muted-foreground text-xs'>
                            {transcript.courses.length} courses
                          </p>
                        )}
                      </div>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <Badge className={getStatusColor(transcript.status)}>
                        {transcript.status.charAt(0).toUpperCase() +
                          transcript.status.slice(1)}
                      </Badge>
                      <Button size='sm' variant='outline'>
                        <Icons.eye className='mr-2 h-4 w-4' />
                        View
                      </Button>
                      <Button size='sm' variant='outline'>
                        <Icons.download className='mr-2 h-4 w-4' />
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks for managing your academic records
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-2'>
            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/academics'>
                <Icons.chevronLeft className='mr-2 h-4 w-4' />
                Back to Academic Records
              </Link>
            </Button>
            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/academics/test-scores'>
                <Icons.calculator className='mr-2 h-4 w-4' />
                Manage Standardized Tests
              </Link>
            </Button>
            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/academics/ap-courses'>
                <Icons.award className='mr-2 h-4 w-4' />
                Manage AP Performance
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </ProfileSetupCheck>
  );
}
