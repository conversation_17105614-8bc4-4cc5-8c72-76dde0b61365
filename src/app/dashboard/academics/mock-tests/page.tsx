'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Icons } from '@/components/icons';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useApiGet, useApiPost, useApiPut, useApiDelete } from '@/lib/api-client';
import { toast } from 'sonner';

interface MockTestScore {
  id: string;
  exam_type: 'SAT' | 'ACT' | 'SAT_SUBJECT' | 'TOEFL' | 'IELTS' | 'AP' | 'Other';
  test_date: string;
  score: number;
  max_score: number;
  source: 'practice_test' | 'tutor_feedback' | 'official_practice' | 'mock_exam' | 'prep_course';
  notes?: string;
  document_url?: string;
  created_by: string;
  tutor_feedback?: string;
  improvement_areas?: string[];
  created_at: string;
  updated_at: string;
  created_by_user?: {
    id: string;
    email: string;
    profile_data?: any;
  };
}

export default function MockTestsPage() {
  const { user } = useCurrentUser();
  const [isAddingTest, setIsAddingTest] = useState(false);
  const [editingTest, setEditingTest] = useState<MockTestScore | null>(null);
  const [newTest, setNewTest] = useState({
    exam_type: '',
    test_date: '',
    score: '',
    max_score: '',
    source: '',
    notes: '',
    document_url: '',
    tutor_feedback: '',
    improvement_areas: ''
  });

  // Get student profile
  const {
    data: studentProfile,
    loading: profileLoading,
    error: profileError
  } = useApiGet<any>('/api/profile/student');

  // Get mock test scores
  const {
    data: mockTests,
    loading: testsLoading,
    error: testsError,
    fetch: fetchMockTests
  } = useApiGet<MockTestScore[]>(
    studentProfile?.id ? `/api/students/${studentProfile.id}/mock-tests` : ''
  );

  // API hooks for CRUD operations
  const createMockTestApi = useApiPost();
  const updateMockTestApi = useApiPut();
  const deleteMockTestApi = useApiDelete();

  useEffect(() => {
    if (studentProfile?.id) {
      fetchMockTests();
    }
  }, [studentProfile?.id, fetchMockTests]);

  const handleAddTest = async () => {
    if (!studentProfile?.id) return;

    try {
      const improvementAreasArray = newTest.improvement_areas
        ? newTest.improvement_areas.split(',').map(area => area.trim()).filter(Boolean)
        : [];

      const testData = {
        exam_type: newTest.exam_type as MockTestScore['exam_type'],
        test_date: newTest.test_date,
        score: parseInt(newTest.score),
        max_score: parseInt(newTest.max_score),
        source: newTest.source as MockTestScore['source'],
        notes: newTest.notes || undefined,
        document_url: newTest.document_url || undefined,
        tutor_feedback: newTest.tutor_feedback || undefined,
        improvement_areas: improvementAreasArray.length > 0 ? improvementAreasArray : undefined
      };

      const response = await createMockTestApi.execute(() =>
        fetch(`/api/students/${studentProfile.id}/mock-tests`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(testData)
        }).then(res => res.json())
      );

      if (response.success) {
        toast.success('Mock test score added successfully!');
        fetchMockTests();
        setNewTest({
          exam_type: '',
          test_date: '',
          score: '',
          max_score: '',
          source: '',
          notes: '',
          document_url: '',
          tutor_feedback: '',
          improvement_areas: ''
        });
        setIsAddingTest(false);
      } else {
        toast.error('Failed to add mock test score');
      }
    } catch (error) {
      console.error('Error adding mock test:', error);
      toast.error('Failed to add mock test score');
    }
  };

  const handleDeleteTest = async (testId: string) => {
    if (!studentProfile?.id) return;

    try {
      const response = await deleteMockTestApi.execute(() =>
        fetch(`/api/students/${studentProfile.id}/mock-tests/${testId}`, {
          method: 'DELETE'
        }).then(res => res.json())
      );

      if (response.success) {
        toast.success('Mock test score deleted successfully!');
        fetchMockTests();
      } else {
        toast.error('Failed to delete mock test score');
      }
    } catch (error) {
      console.error('Error deleting mock test:', error);
      toast.error('Failed to delete mock test score');
    }
  };

  const getExamTypeColor = (examType: string) => {
    switch (examType) {
      case 'SAT':
        return 'bg-blue-100 text-blue-800';
      case 'ACT':
        return 'bg-green-100 text-green-800';
      case 'TOEFL':
        return 'bg-purple-100 text-purple-800';
      case 'IELTS':
        return 'bg-orange-100 text-orange-800';
      case 'AP':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'practice_test':
        return 'bg-blue-100 text-blue-800';
      case 'tutor_feedback':
        return 'bg-green-100 text-green-800';
      case 'official_practice':
        return 'bg-purple-100 text-purple-800';
      case 'mock_exam':
        return 'bg-orange-100 text-orange-800';
      case 'prep_course':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 80) return 'text-blue-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatSource = (source: string) => {
    return source.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (profileLoading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>Mock Test Scores</h2>
            <p className='text-muted-foreground'>Loading your profile...</p>
          </div>
        </div>
      </div>
    );
  }

  if (profileError) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>Mock Test Scores</h2>
            <p className='text-muted-foreground text-red-600'>Error loading profile: {profileError}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Mock Test Scores</h2>
          <p className='text-muted-foreground'>
            Track your practice test performance and improvement over time
          </p>
        </div>
        <Dialog open={isAddingTest} onOpenChange={setIsAddingTest}>
          <DialogTrigger asChild>
            <Button>
              <Icons.plus className='mr-2 h-4 w-4' />
              Add Mock Test
            </Button>
          </DialogTrigger>
          <DialogContent className='max-w-2xl'>
            <DialogHeader>
              <DialogTitle>Add Mock Test Score</DialogTitle>
              <DialogDescription>
                Record a new practice test score to track your progress
              </DialogDescription>
            </DialogHeader>
            <div className='grid gap-4 py-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='exam_type'>Exam Type</Label>
                  <Select
                    value={newTest.exam_type}
                    onValueChange={(value) => setNewTest({ ...newTest, exam_type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select exam type' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='SAT'>SAT</SelectItem>
                      <SelectItem value='ACT'>ACT</SelectItem>
                      <SelectItem value='SAT_SUBJECT'>SAT Subject</SelectItem>
                      <SelectItem value='TOEFL'>TOEFL</SelectItem>
                      <SelectItem value='IELTS'>IELTS</SelectItem>
                      <SelectItem value='AP'>AP</SelectItem>
                      <SelectItem value='Other'>Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='source'>Source</Label>
                  <Select
                    value={newTest.source}
                    onValueChange={(value) => setNewTest({ ...newTest, source: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select source' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='practice_test'>Practice Test</SelectItem>
                      <SelectItem value='tutor_feedback'>Tutor Feedback</SelectItem>
                      <SelectItem value='official_practice'>Official Practice</SelectItem>
                      <SelectItem value='mock_exam'>Mock Exam</SelectItem>
                      <SelectItem value='prep_course'>Prep Course</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className='grid grid-cols-3 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='test_date'>Test Date</Label>
                  <Input
                    id='test_date'
                    type='date'
                    value={newTest.test_date}
                    onChange={(e) => setNewTest({ ...newTest, test_date: e.target.value })}
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='score'>Score</Label>
                  <Input
                    id='score'
                    type='number'
                    value={newTest.score}
                    onChange={(e) => setNewTest({ ...newTest, score: e.target.value })}
                    placeholder='Your score'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='max_score'>Max Score</Label>
                  <Input
                    id='max_score'
                    type='number'
                    value={newTest.max_score}
                    onChange={(e) => setNewTest({ ...newTest, max_score: e.target.value })}
                    placeholder='Maximum possible'
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant='outline' onClick={() => setIsAddingTest(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddTest}>Add Mock Test</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Mock Test Scores List */}
      <div className='space-y-4'>
        {testsLoading ? (
          <div className='grid gap-4'>
            {[1, 2, 3].map((i) => (
              <Card key={i} className='animate-pulse'>
                <CardHeader>
                  <div className='h-4 bg-muted rounded w-3/4'></div>
                  <div className='h-3 bg-muted rounded w-1/2'></div>
                </CardHeader>
                <CardContent>
                  <div className='h-2 bg-muted rounded w-full'></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : testsError ? (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-12'>
              <Icons.alertCircle className='text-muted-foreground mb-4 h-12 w-12' />
              <h3 className='mb-2 text-lg font-semibold'>Error Loading Mock Tests</h3>
              <p className='text-muted-foreground mb-4 text-center'>
                {testsError}
              </p>
              <Button onClick={fetchMockTests}>
                <Icons.refresh className='mr-2 h-4 w-4' />
                Try Again
              </Button>
            </CardContent>
          </Card>
        ) : !mockTests || mockTests.length === 0 ? (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-12'>
              <Icons.fileText className='text-muted-foreground mb-4 h-12 w-12' />
              <h3 className='mb-2 text-lg font-semibold'>No Mock Tests Yet</h3>
              <p className='text-muted-foreground mb-4 text-center'>
                Start tracking your practice test scores to monitor your progress
              </p>
              <Button onClick={() => setIsAddingTest(true)}>
                <Icons.plus className='mr-2 h-4 w-4' />
                Add Your First Mock Test
              </Button>
            </CardContent>
          </Card>
        ) : (
          mockTests.map((test) => (
            <Card key={test.id} className='transition-shadow hover:shadow-md'>
              <CardHeader>
                <div className='flex items-center justify-between'>
                  <div className='space-y-1'>
                    <div className='flex items-center space-x-2'>
                      <Badge className={getExamTypeColor(test.exam_type)}>
                        {test.exam_type.replace('_', ' ')}
                      </Badge>
                      <Badge variant='outline' className={getSourceColor(test.source)}>
                        {formatSource(test.source)}
                      </Badge>
                    </div>
                    <CardTitle className='text-lg'>
                      {test.exam_type} Practice Test
                    </CardTitle>
                    <CardDescription>
                      Taken on {formatDate(test.test_date)}
                      {test.created_by_user && (
                        <span className='ml-2'>
                          • Added by {test.created_by_user.profile_data?.name || test.created_by_user.email}
                        </span>
                      )}
                    </CardDescription>
                  </div>
                  <div className='flex items-center space-x-4'>
                    <div className='text-right'>
                      <div className={`text-2xl font-bold ${getScoreColor(test.score, test.max_score)}`}>
                        {test.score}/{test.max_score}
                      </div>
                      <div className='text-muted-foreground text-sm'>
                        {Math.round((test.score / test.max_score) * 100)}%
                      </div>
                    </div>
                    <div className='flex space-x-2'>
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() => setEditingTest(test)}
                      >
                        <Icons.edit className='h-4 w-4' />
                      </Button>
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() => handleDeleteTest(test.id)}
                      >
                        <Icons.trash className='h-4 w-4' />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardHeader>
              {(test.notes || test.tutor_feedback || test.improvement_areas) && (
                <CardContent>
                  <div className='space-y-3'>
                    {test.notes && (
                      <div>
                        <h4 className='text-sm font-medium mb-1'>Notes</h4>
                        <p className='text-muted-foreground text-sm'>{test.notes}</p>
                      </div>
                    )}
                    {test.tutor_feedback && (
                      <div>
                        <h4 className='text-sm font-medium mb-1'>Tutor Feedback</h4>
                        <p className='text-muted-foreground text-sm'>{test.tutor_feedback}</p>
                      </div>
                    )}
                    {test.improvement_areas && test.improvement_areas.length > 0 && (
                      <div>
                        <h4 className='text-sm font-medium mb-1'>Areas for Improvement</h4>
                        <div className='flex flex-wrap gap-1'>
                          {test.improvement_areas.map((area, index) => (
                            <Badge key={index} variant='secondary' className='text-xs'>
                              {area}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    {test.document_url && (
                      <div>
                        <h4 className='text-sm font-medium mb-1'>Document</h4>
                        <a
                          href={test.document_url}
                          target='_blank'
                          rel='noopener noreferrer'
                          className='text-blue-600 hover:text-blue-800 text-sm underline'
                        >
                          View Test Results
                        </a>
                      </div>
                    )}
                  </div>
                </CardContent>
              )}
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
