'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useApiGet, useApiPost, useApiPut } from '@/lib/api-client';
import { toast } from 'sonner';
import apClassesData from '@/data/ap-classes.json';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

interface APCourse {
  id: string;
  subject: string;
  score: number;
  year: string;
  exam_date?: string;
  is_estimated?: boolean;
}

export default function APCoursesPage() {
  const { user } = useCurrentUser();
  const [apCourses, setApCourses] = useState<APCourse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddingCourse, setIsAddingCourse] = useState(false);
  const [editingCourse, setEditingCourse] = useState<APCourse | null>(null);

  // Form state for adding new AP course
  const [newCourse, setNewCourse] = useState({
    subject: '',
    score: '',
    year: '',
    exam_date: '',
    is_estimated: false
  });

  // API hooks
  const { data: studentProfile, loading: profileLoading } = useApiGet<{ id: string }>('/api/profile/student');
  const { data: apCoursesData, loading: apLoading, error: apError, fetch: fetchAPCourses } = useApiGet<APCourse[]>(
    studentProfile?.id ? `/api/students/${studentProfile.id}/ap-courses` : ''
  );
  const { post: createAPCourse } = useApiPost();
  const { put: updateAPCourse } = useApiPut();

  useEffect(() => {
    if (apCoursesData) {
      setApCourses(apCoursesData);
    }
    setLoading(profileLoading || apLoading);
    if (apError) {
      setError(apError);
    }
  }, [apCoursesData, apLoading, apError, profileLoading]);

  const getScoreColor = (score: number) => {
    if (score >= 4) return 'bg-green-100 text-green-800';
    if (score >= 3) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getScoreDescription = (score: number) => {
    switch (score) {
      case 5:
        return 'Extremely well qualified';
      case 4:
        return 'Well qualified';
      case 3:
        return 'Qualified';
      case 2:
        return 'Possibly qualified';
      case 1:
        return 'No recommendation';
      default:
        return 'Unknown';
    }
  };

  const calculateAverageScore = () => {
    if (apCourses.length === 0) return 0;
    const totalScore = apCourses.reduce((sum, course) => sum + course.score, 0);
    return (totalScore / apCourses.length).toFixed(1);
  };

  const handleAddCourse = async () => {
    if (!newCourse.subject || !newCourse.score || !newCourse.year) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (!studentProfile?.id) {
      toast.error('Student profile not found');
      return;
    }

    try {
      const courseData = {
        subject: newCourse.subject,
        score: parseInt(newCourse.score),
        year: newCourse.year,
        exam_date: newCourse.exam_date || undefined,
        is_estimated: newCourse.is_estimated
      };

      const response = await createAPCourse(`/api/students/${studentProfile.id}/ap-courses`, courseData);

      if (response.success) {
        toast.success('AP course added successfully!');
        fetchAPCourses(); // Refresh the data
        setNewCourse({
          subject: '',
          score: '',
          year: '',
          exam_date: '',
          is_estimated: false
        });
        setIsAddingCourse(false);
      } else {
        toast.error('Failed to add AP course');
      }
    } catch (error) {
      console.error('Error adding AP course:', error);
      toast.error('Failed to add AP course');
    }
  };

  const handleEditCourse = async (course: APCourse) => {
    if (!studentProfile?.id) {
      toast.error('Student profile not found');
      return;
    }

    try {
      const response = await updateAPCourse(`/api/students/${studentProfile.id}/ap-courses/${course.id}`, course);

      if (response.success) {
        toast.success('AP course updated successfully!');
        fetchAPCourses(); // Refresh the data
        setEditingCourse(null);
      } else {
        toast.error('Failed to update AP course');
      }
    } catch (error) {
      console.error('Error updating AP course:', error);
      toast.error('Failed to update AP course');
    }
  };

  if (loading) {
    return (
      <ProfileSetupCheck requireStudentProfile={true}>
        <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
          <div className='flex h-64 items-center justify-center'>
            <Icons.spinner className='h-8 w-8 animate-spin' />
          </div>
        </div>
      </ProfileSetupCheck>
    );
  }

  if (error) {
    return (
      <ProfileSetupCheck requireStudentProfile={true}>
        <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
          <div className='flex h-64 items-center justify-center'>
            <div className='text-center'>
              <Icons.alertCircle className='mx-auto mb-4 h-12 w-12 text-red-500' />
              <h3 className='text-lg font-semibold'>
                Error Loading AP Courses
              </h3>
              <p className='text-muted-foreground mt-2'>{error}</p>
              <Button onClick={fetchAPCourses} className='mt-4'>
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </ProfileSetupCheck>
    );
  }

  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* Header */}
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>AP Performance</h2>
            <p className='text-muted-foreground'>
              Comprehensive management of your Advanced Placement courses, exam scores, and academic performance
            </p>
            <div className='mt-2 rounded-md bg-blue-50 p-3'>
              <p className='text-sm text-blue-800'>
                <Icons.alertCircle className='mr-1 inline h-4 w-4' />
                This is your central hub for all AP-related information. All AP scores and course data are managed here.
              </p>
            </div>
          </div>
          <Dialog open={isAddingCourse} onOpenChange={setIsAddingCourse}>
            <DialogTrigger asChild>
              <Button>
                <Icons.plus className='mr-2 h-4 w-4' />
                Add AP Course
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add AP Course & Score</DialogTitle>
                <DialogDescription>
                  Add a new Advanced Placement course with exam score and performance details
                </DialogDescription>
              </DialogHeader>
              <div className='space-y-4'>
                <div>
                  <Label htmlFor='subject'>Subject</Label>
                  <Select
                    value={newCourse.subject}
                    onValueChange={(value) =>
                      setNewCourse({ ...newCourse, subject: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select AP course' />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(apClassesData.categories).map(([categoryKey, categoryName]) => (
                        <div key={categoryKey}>
                          <div className='px-2 py-1.5 text-sm font-semibold text-muted-foreground'>
                            {categoryName}
                          </div>
                          {apClassesData.classes
                            .filter(course => course.category === categoryKey)
                            .map(course => (
                              <SelectItem key={course.id} value={course.name}>
                                {course.name}
                              </SelectItem>
                            ))}
                        </div>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor='score'>Score (1-5)</Label>
                  <Select
                    value={newCourse.score}
                    onValueChange={(value) =>
                      setNewCourse({ ...newCourse, score: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select score' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='5'>
                        5 - Extremely well qualified
                      </SelectItem>
                      <SelectItem value='4'>4 - Well qualified</SelectItem>
                      <SelectItem value='3'>3 - Qualified</SelectItem>
                      <SelectItem value='2'>2 - Possibly qualified</SelectItem>
                      <SelectItem value='1'>1 - No recommendation</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor='year'>Year</Label>
                  <Select
                    value={newCourse.year}
                    onValueChange={(value) =>
                      setNewCourse({ ...newCourse, year: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select year' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='2025'>2025</SelectItem>
                      <SelectItem value='2024'>2024</SelectItem>
                      <SelectItem value='2023'>2023</SelectItem>
                      <SelectItem value='2022'>2022</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor='exam_date'>Exam Date (Optional)</Label>
                  <Input
                    id='exam_date'
                    type='date'
                    value={newCourse.exam_date}
                    onChange={(e) =>
                      setNewCourse({ ...newCourse, exam_date: e.target.value })
                    }
                  />
                </div>
                <div className='flex justify-end space-x-2'>
                  <Button
                    variant='outline'
                    onClick={() => setIsAddingCourse(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleAddCourse}>Add Course</Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* AP Overview Stats */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Total AP Exams
              </CardTitle>
              <Icons.award className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{apCourses.length}</div>
              <p className='text-muted-foreground text-xs'>Courses completed</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Average Score
              </CardTitle>
              <Icons.trendingUp className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {calculateAverageScore()}
              </div>
              <p className='text-muted-foreground text-xs'>Out of 5.0</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                High Scores (4-5)
              </CardTitle>
              <Icons.star className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {apCourses.filter((course) => course.score >= 4).length}
              </div>
              <p className='text-muted-foreground text-xs'>
                Well qualified or better
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                College Credit
              </CardTitle>
              <Icons.graduationCap className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {apCourses.filter((course) => course.score >= 3).length}
              </div>
              <p className='text-muted-foreground text-xs'>
                Eligible for credit
              </p>
            </CardContent>
          </Card>
        </div>

        {/* AP Performance Results */}
        <Card>
          <CardHeader>
            <CardTitle>AP Performance Results</CardTitle>
            <CardDescription>
              Complete overview of your Advanced Placement courses, exam scores, and academic achievements
            </CardDescription>
          </CardHeader>
          <CardContent>
            {apCourses.length === 0 ? (
              <div className='py-8 text-center'>
                <Icons.award className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
                <h3 className='mb-2 text-lg font-medium'>No AP Performance Data Yet</h3>
                <p className='text-muted-foreground mb-4'>
                  Add your Advanced Placement courses, exam scores, and performance details to track your academic achievements
                </p>
                <Button onClick={() => setIsAddingCourse(true)}>
                  <Icons.plus className='mr-2 h-4 w-4' />
                  Add Your First AP Course
                </Button>
              </div>
            ) : (
              <div className='space-y-4'>
                {apCourses.map((course) => (
                  <div
                    key={course.id}
                    className='flex items-center justify-between rounded-lg border p-4'
                  >
                    <div className='flex items-center space-x-4'>
                      <Icons.award className='h-8 w-8 text-yellow-500' />
                      <div>
                        <h4 className='font-medium'>AP {course.subject}</h4>
                        <p className='text-muted-foreground text-sm'>
                          {course.year} •{' '}
                          {course.exam_date
                            ? new Date(course.exam_date).toLocaleDateString()
                            : 'Date not specified'}
                        </p>
                        <p className='text-muted-foreground text-xs'>
                          {getScoreDescription(course.score)}
                        </p>
                      </div>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <Badge className={getScoreColor(course.score)}>
                        Score: {course.score}
                      </Badge>
                      {course.is_estimated && (
                        <Badge variant='outline'>Estimated</Badge>
                      )}
                      <Button size='sm' variant='outline'>
                        <Icons.edit className='mr-2 h-4 w-4' />
                        Edit
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks for managing your academic records
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-2'>
            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/academics'>
                <Icons.chevronLeft className='mr-2 h-4 w-4' />
                Back to Academic Records
              </Link>
            </Button>
            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/academics/transcripts'>
                <Icons.fileText className='mr-2 h-4 w-4' />
                Manage Transcripts
              </Link>
            </Button>
            <Button asChild variant='outline' className='w-full justify-start'>
              <Link href='/dashboard/academics/test-scores'>
                <Icons.calculator className='mr-2 h-4 w-4' />
                Manage Standardized Tests
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </ProfileSetupCheck>
  );
}
