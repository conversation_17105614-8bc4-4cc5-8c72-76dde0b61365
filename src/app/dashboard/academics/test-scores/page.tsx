'use client';

import { useState, useEffect } from 'react';
import { useCurrentUser } from '@/hooks/use-current-user';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Icons } from '@/components/icons';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { useApiGet, useApiPost, useApiPut, useApiDelete } from '@/lib/api-client';
import { toast } from 'sonner';

interface TestScore {
  id: string;
  test_type: 'SAT' | 'ACT' | 'SAT_SUBJECT' | 'TOEFL' | 'IELTS';
  subject?: string;
  score: number;
  max_score: number;
  test_date: string;
  is_superscore?: boolean;
  notes?: string;
  subject_scores?: SubjectScores;
}

interface SubjectScores {
  // SAT Subject Scores
  sat_math?: number;
  sat_ebrw?: number; // Evidence-Based Reading and Writing

  // ACT Subject Scores
  act_english?: number;
  act_math?: number;
  act_reading?: number;
  act_science?: number;
  act_writing?: number; // Optional writing section
}

export default function TestScoresPage() {
  const { user } = useCurrentUser();
  const [testScores, setTestScores] = useState<TestScore[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddingScore, setIsAddingScore] = useState(false);
  const [editingScore, setEditingScore] = useState<TestScore | null>(null);

  // Form state for adding new test score
  const [newScore, setNewScore] = useState({
    test_type: '',
    subject: '',
    score: '',
    test_date: '',
    notes: '',
    // SAT Subject Scores
    sat_math: '',
    sat_ebrw: '',
    // ACT Subject Scores
    act_english: '',
    act_math: '',
    act_reading: '',
    act_science: '',
    act_writing: ''
  });

  // API hooks
  const { data: studentProfile } = useApiGet<{ id: string }>('/api/profile/student');
  const { data: testScoresData, loading: scoresLoading, error: scoresError, fetch: fetchTestScores } = useApiGet<TestScore[]>(
    studentProfile?.id ? `/api/students/${studentProfile.id}/test-scores` : ''
  );
  const { post: createTestScore } = useApiPost();
  const { put: updateTestScore } = useApiPut();
  const { delete: deleteTestScore } = useApiDelete();

  useEffect(() => {
    if (testScoresData) {
      setTestScores(testScoresData);
    }
    setLoading(scoresLoading);
    if (scoresError) {
      setError(scoresError);
    }
  }, [testScoresData, scoresLoading, scoresError]);

  const getTestTypeColor = (testType: string) => {
    switch (testType) {
      case 'SAT':
        return 'bg-blue-100 text-blue-800';
      case 'ACT':
        return 'bg-green-100 text-green-800';
      case 'SAT_SUBJECT':
        return 'bg-indigo-100 text-indigo-800';
      case 'TOEFL':
      case 'IELTS':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 80) return 'text-blue-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getMaxScoreForType = (testType: string) => {
    switch (testType) {
      case 'SAT':
        return 1600;
      case 'ACT':
        return 36;
      case 'TOEFL':
        return 120;
      case 'IELTS':
        return 9;
      default:
        return 100;
    }
  };

  const calculateSuperscore = (
    scores: TestScore[],
    testType: 'SAT' | 'ACT'
  ): TestScore | null => {
    const relevantScores = scores.filter(
      (s) => s.test_type === testType && s.subject_scores
    );
    if (relevantScores.length === 0) return null;

    if (testType === 'SAT') {
      const bestMath = Math.max(
        ...relevantScores.map((s) => s.subject_scores?.sat_math || 0)
      );
      const bestEBRW = Math.max(
        ...relevantScores.map((s) => s.subject_scores?.sat_ebrw || 0)
      );
      const superscoreTotal = bestMath + bestEBRW;

      return {
        id: 'superscore-sat',
        test_type: 'SAT',
        score: superscoreTotal,
        max_score: 1600,
        test_date: 'Superscore',
        is_superscore: true,
        subject_scores: {
          sat_math: bestMath,
          sat_ebrw: bestEBRW
        }
      };
    } else if (testType === 'ACT') {
      const bestEnglish = Math.max(
        ...relevantScores.map((s) => s.subject_scores?.act_english || 0)
      );
      const bestMath = Math.max(
        ...relevantScores.map((s) => s.subject_scores?.act_math || 0)
      );
      const bestReading = Math.max(
        ...relevantScores.map((s) => s.subject_scores?.act_reading || 0)
      );
      const bestScience = Math.max(
        ...relevantScores.map((s) => s.subject_scores?.act_science || 0)
      );
      const superscoreComposite = Math.round(
        (bestEnglish + bestMath + bestReading + bestScience) / 4
      );

      return {
        id: 'superscore-act',
        test_type: 'ACT',
        score: superscoreComposite,
        max_score: 36,
        test_date: 'Superscore',
        is_superscore: true,
        subject_scores: {
          act_english: bestEnglish,
          act_math: bestMath,
          act_reading: bestReading,
          act_science: bestScience
        }
      };
    }

    return null;
  };

  const handleAddScore = async () => {
    if (!newScore.test_type || !newScore.test_date) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (!studentProfile?.id) {
      toast.error('Student profile not found');
      return;
    }

    try {

    const maxScore = getMaxScoreForType(newScore.test_type);
    let compositeScore = parseInt(newScore.score) || 0;
    let subjectScores: SubjectScores | undefined;

    // Calculate composite score from subject scores if not provided
    if (
      newScore.test_type === 'SAT' &&
      (newScore.sat_math || newScore.sat_ebrw)
    ) {
      const mathScore = parseInt(newScore.sat_math) || 0;
      const ebrwScore = parseInt(newScore.sat_ebrw) || 0;
      compositeScore = mathScore + ebrwScore;
      subjectScores = {
        sat_math: mathScore || undefined,
        sat_ebrw: ebrwScore || undefined
      };
    } else if (
      newScore.test_type === 'ACT' &&
      (newScore.act_english ||
        newScore.act_math ||
        newScore.act_reading ||
        newScore.act_science)
    ) {
      const englishScore = parseInt(newScore.act_english) || 0;
      const mathScore = parseInt(newScore.act_math) || 0;
      const readingScore = parseInt(newScore.act_reading) || 0;
      const scienceScore = parseInt(newScore.act_science) || 0;
      const writingScore = parseInt(newScore.act_writing) || undefined;

      compositeScore = Math.round(
        (englishScore + mathScore + readingScore + scienceScore) / 4
      );
      subjectScores = {
        act_english: englishScore || undefined,
        act_math: mathScore || undefined,
        act_reading: readingScore || undefined,
        act_science: scienceScore || undefined,
        act_writing: writingScore
      };
    }

      const scoreData = {
        test_type: newScore.test_type as TestScore['test_type'],
        subject: newScore.subject || undefined,
        score: compositeScore,
        max_score: maxScore,
        test_date: newScore.test_date,
        notes: newScore.notes || undefined,
        subject_scores: subjectScores
      };

      const response = await createTestScore(`/api/students/${studentProfile.id}/test-scores`, scoreData);

      if (response.success) {
        toast.success('Test score added successfully!');
        fetchTestScores(); // Refresh the data
        setNewScore({
          test_type: '',
          subject: '',
          score: '',
          test_date: '',
          notes: '',
          sat_math: '',
          sat_ebrw: '',
          act_english: '',
          act_math: '',
          act_reading: '',
          act_science: '',
          act_writing: ''
        });
        setIsAddingScore(false);
      } else {
        toast.error('Failed to add test score');
      }
    } catch (error) {
      console.error('Error adding test score:', error);
      toast.error('Failed to add test score');
    }
  };

  const handleEditScore = async (score: TestScore) => {
    if (!studentProfile?.id) {
      toast.error('Student profile not found');
      return;
    }

    try {
      const response = await updateTestScore(`/api/students/${studentProfile.id}/test-scores/${score.id}`, score);

      if (response.success) {
        toast.success('Test score updated successfully!');
        fetchTestScores(); // Refresh the data
        setEditingScore(null);
      } else {
        toast.error('Failed to update test score');
      }
    } catch (error) {
      console.error('Error updating test score:', error);
      toast.error('Failed to update test score');
    }
  };

  const handleDeleteScore = async (scoreId: string) => {
    if (!studentProfile?.id) {
      toast.error('Student profile not found');
      return;
    }

    if (!confirm('Are you sure you want to delete this test score?')) {
      return;
    }

    try {
      const response = await deleteTestScore(`/api/students/${studentProfile.id}/test-scores/${scoreId}`);

      if (response.success) {
        toast.success('Test score deleted successfully!');
        fetchTestScores(); // Refresh the data
      } else {
        toast.error('Failed to delete test score');
      }
    } catch (error) {
      console.error('Error deleting test score:', error);
      toast.error('Failed to delete test score');
    }
  };

  // Calculate superscores and add them to the display
  const satSuperscore = calculateSuperscore(testScores, 'SAT');
  const actSuperscore = calculateSuperscore(testScores, 'ACT');

  const allScores = [...testScores];
  if (satSuperscore) allScores.push(satSuperscore);
  if (actSuperscore) allScores.push(actSuperscore);

  const groupedScores = allScores.reduce(
    (acc, score) => {
      const key = score.test_type;
      if (!acc[key]) acc[key] = [];
      acc[key].push(score);
      return acc;
    },
    {} as Record<string, TestScore[]>
  );

  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* Header */}
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>Standardized Tests</h2>
            <p className='text-muted-foreground'>
              Manage your SAT, ACT, TOEFL, IELTS and other standardized test scores
            </p>
          </div>
          <Dialog open={isAddingScore} onOpenChange={setIsAddingScore}>
            <DialogTrigger asChild>
              <Button>
                <Icons.plus className='mr-2 h-4 w-4' />
                Add Test Score
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Standardized Test Score</DialogTitle>
                <DialogDescription>
                  Add a new SAT, ACT, TOEFL, IELTS or other standardized test score to your academic profile
                </DialogDescription>
              </DialogHeader>
              <div className='space-y-4'>
                <div>
                  <Label htmlFor='test_type'>Test Type</Label>
                  <Select
                    value={newScore.test_type}
                    onValueChange={(value) =>
                      setNewScore({ ...newScore, test_type: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select test type' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='SAT'>SAT</SelectItem>
                      <SelectItem value='ACT'>ACT</SelectItem>
                      <SelectItem value='SAT_SUBJECT'>
                        SAT Subject Test
                      </SelectItem>
                      <SelectItem value='TOEFL'>TOEFL</SelectItem>
                      <SelectItem value='IELTS'>IELTS</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {newScore.test_type === 'SAT_SUBJECT' && (
                  <div>
                    <Label htmlFor='subject'>Subject</Label>
                    <Input
                      id='subject'
                      value={newScore.subject}
                      onChange={(e) =>
                        setNewScore({ ...newScore, subject: e.target.value })
                      }
                      placeholder='e.g., Calculus BC, Biology'
                    />
                  </div>
                )}


                {/* SAT Subject Scores */}
                {newScore.test_type === 'SAT' && (
                  <div className='space-y-4'>
                    <div className='text-sm font-medium'>
                      SAT Subject Scores
                    </div>
                    <div className='grid grid-cols-2 gap-4'>
                      <div>
                        <Label htmlFor='sat_math'>Math</Label>
                        <Input
                          id='sat_math'
                          type='number'
                          min='200'
                          max='800'
                          value={newScore.sat_math}
                          onChange={(e) =>
                            setNewScore({
                              ...newScore,
                              sat_math: e.target.value
                            })
                          }
                          placeholder='Math score (200-800)'
                        />
                      </div>
                      <div>
                        <Label htmlFor='sat_ebrw'>
                          Evidence-Based Reading and Writing
                        </Label>
                        <Input
                          id='sat_ebrw'
                          type='number'
                          min='200'
                          max='800'
                          value={newScore.sat_ebrw}
                          onChange={(e) =>
                            setNewScore({
                              ...newScore,
                              sat_ebrw: e.target.value
                            })
                          }
                          placeholder='EBRW score (200-800)'
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* ACT Subject Scores */}
                {newScore.test_type === 'ACT' && (
                  <div className='space-y-4'>
                    <div className='text-sm font-medium'>
                      ACT Subject Scores
                    </div>
                    <div className='grid grid-cols-2 gap-4'>
                      <div>
                        <Label htmlFor='act_english'>English</Label>
                        <Input
                          id='act_english'
                          type='number'
                          min='1'
                          max='36'
                          value={newScore.act_english}
                          onChange={(e) =>
                            setNewScore({
                              ...newScore,
                              act_english: e.target.value
                            })
                          }
                          placeholder='English (1-36)'
                        />
                      </div>
                      <div>
                        <Label htmlFor='act_math'>Math</Label>
                        <Input
                          id='act_math'
                          type='number'
                          min='1'
                          max='36'
                          value={newScore.act_math}
                          onChange={(e) =>
                            setNewScore({
                              ...newScore,
                              act_math: e.target.value
                            })
                          }
                          placeholder='Math (1-36)'
                        />
                      </div>
                      <div>
                        <Label htmlFor='act_reading'>Reading</Label>
                        <Input
                          id='act_reading'
                          type='number'
                          min='1'
                          max='36'
                          value={newScore.act_reading}
                          onChange={(e) =>
                            setNewScore({
                              ...newScore,
                              act_reading: e.target.value
                            })
                          }
                          placeholder='Reading (1-36)'
                        />
                      </div>
                      <div>
                        <Label htmlFor='act_science'>Science</Label>
                        <Input
                          id='act_science'
                          type='number'
                          min='1'
                          max='36'
                          value={newScore.act_science}
                          onChange={(e) =>
                            setNewScore({
                              ...newScore,
                              act_science: e.target.value
                            })
                          }
                          placeholder='Science (1-36)'
                        />
                      </div>
                      <div>
                        <Label htmlFor='act_writing'>Writing (Optional)</Label>
                        <Input
                          id='act_writing'
                          type='number'
                          min='2'
                          max='12'
                          value={newScore.act_writing}
                          onChange={(e) =>
                            setNewScore({
                              ...newScore,
                              act_writing: e.target.value
                            })
                          }
                          placeholder='Writing (2-12)'
                        />
                      </div>
                    </div>
                  </div>
                )}

                <div>
                  <Label htmlFor='score'>Composite Score (Optional)</Label>
                  <Input
                    id='score'
                    type='number'
                    value={newScore.score}
                    onChange={(e) =>
                      setNewScore({ ...newScore, score: e.target.value })
                    }
                    placeholder='Enter composite score or leave blank for auto-calculation'
                  />
                  {newScore.test_type && (
                    <p className='text-muted-foreground mt-1 text-xs'>
                      Max score: {getMaxScoreForType(newScore.test_type)}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor='test_date'>Test Date</Label>
                  <Input
                    id='test_date'
                    type='date'
                    value={newScore.test_date}
                    onChange={(e) =>
                      setNewScore({ ...newScore, test_date: e.target.value })
                    }
                  />
                </div>
                <div>
                  <Label htmlFor='notes'>Notes (Optional)</Label>
                  <Input
                    id='notes'
                    value={newScore.notes}
                    onChange={(e) =>
                      setNewScore({ ...newScore, notes: e.target.value })
                    }
                    placeholder='Any additional notes'
                  />
                </div>
                <div className='flex justify-end space-x-2'>
                  <Button
                    variant='outline'
                    onClick={() => setIsAddingScore(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleAddScore}>Add Score</Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Overview Stats */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Total Scores
              </CardTitle>
              <Icons.calculator className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{testScores.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>SAT Score</CardTitle>
              <Icons.award className='h-4 w-4 text-blue-600' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {testScores.find((s) => s.test_type === 'SAT')?.score || 'N/A'}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>ACT Score</CardTitle>
              <Icons.award className='h-4 w-4 text-green-600' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {testScores.find((s) => s.test_type === 'ACT')?.score || 'N/A'}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Language Tests</CardTitle>
              <Icons.star className='h-4 w-4 text-purple-600' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {testScores.filter((s) => s.test_type === 'TOEFL' || s.test_type === 'IELTS').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Standardized Test Scores by Type */}
        <div className='space-y-6'>
          {Object.entries(groupedScores).map(([testType, scores]) => (
            <Card key={testType}>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Badge className={getTestTypeColor(testType)}>
                    {testType.replace('_', ' ')}
                  </Badge>
                  {testType} Scores
                </CardTitle>
                <CardDescription>
                  Your {testType.replace('_', ' ')} test results
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  {scores.map((score) => (
                    <div
                      key={score.id}
                      className='hover:bg-muted/50 flex items-center justify-between rounded-lg border p-4 transition-colors'
                    >
                      <div className='flex items-center space-x-4'>
                        <div className='min-w-0 flex-1'>
                          <h4 className='text-sm font-medium'>
                            {score.subject
                              ? `${testType} ${score.subject}`
                              : testType}
                          </h4>
                          <div className='mt-1 flex items-center space-x-2'>
                            <span className='text-muted-foreground text-xs'>
                              {score.is_superscore
                                ? 'Superscore'
                                : `Test Date: ${score.test_date === 'Superscore' ? 'Superscore' : new Date(score.test_date).toLocaleDateString()}`}
                            </span>
                            {score.is_superscore && (
                              <Badge variant='outline' className='text-xs'>
                                Superscore
                              </Badge>
                            )}

                          </div>
                          {/* Subject Score Breakdown */}
                          {score.subject_scores && (
                            <div className='text-muted-foreground mt-2 text-xs'>
                              {score.test_type === 'SAT' && (
                                <div className='flex space-x-4'>
                                  {score.subject_scores.sat_math && (
                                    <span>
                                      Math: {score.subject_scores.sat_math}
                                    </span>
                                  )}
                                  {score.subject_scores.sat_ebrw && (
                                    <span>
                                      EBRW: {score.subject_scores.sat_ebrw}
                                    </span>
                                  )}
                                </div>
                              )}
                              {score.test_type === 'ACT' && (
                                <div className='flex flex-wrap space-x-2'>
                                  {score.subject_scores.act_english && (
                                    <span>
                                      E: {score.subject_scores.act_english}
                                    </span>
                                  )}
                                  {score.subject_scores.act_math && (
                                    <span>
                                      M: {score.subject_scores.act_math}
                                    </span>
                                  )}
                                  {score.subject_scores.act_reading && (
                                    <span>
                                      R: {score.subject_scores.act_reading}
                                    </span>
                                  )}
                                  {score.subject_scores.act_science && (
                                    <span>
                                      S: {score.subject_scores.act_science}
                                    </span>
                                  )}
                                  {score.subject_scores.act_writing && (
                                    <span>
                                      W: {score.subject_scores.act_writing}
                                    </span>
                                  )}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className='flex items-center space-x-4'>
                        <div className='text-right'>
                          <div
                            className={`text-lg font-bold ${getScoreColor(score.score, score.max_score)}`}
                          >
                            {score.score}/{score.max_score}
                          </div>
                          <div className='text-muted-foreground text-xs'>
                            {Math.round((score.score / score.max_score) * 100)}th
                            percentile
                          </div>
                        </div>
                        {/* Edit and Delete buttons - only show for non-superscore entries */}
                        {!score.is_superscore && (
                          <div className='flex space-x-2'>
                            <Button
                              size='sm'
                              variant='outline'
                              onClick={() => setEditingScore(score)}
                            >
                              <Icons.edit className='h-4 w-4' />
                            </Button>
                            <Button
                              size='sm'
                              variant='outline'
                              onClick={() => handleDeleteScore(score.id)}
                            >
                              <Icons.trash className='h-4 w-4' />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {testScores.length === 0 && !loading && (
          <Card>
            <CardContent className='py-8 text-center'>
              <Icons.calculator className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
              <h3 className='mb-2 text-lg font-medium'>No Standardized Test Scores Yet</h3>
              <p className='text-muted-foreground mb-4'>
                Add your SAT, ACT, TOEFL, IELTS and other standardized test scores to track your academic progress
              </p>
              <Button onClick={() => setIsAddingScore(true)}>
                <Icons.plus className='mr-2 h-4 w-4' />
                Add Your First Score
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Test Prep Resources */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Icons.book className='h-5 w-5' />
              Test Preparation Resources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='grid gap-4 md:grid-cols-2'>
              <div className='space-y-2'>
                <h4 className='font-medium'>SAT Preparation</h4>
                <ul className='text-muted-foreground space-y-1 text-sm'>
                  <li>• Khan Academy SAT Practice (Free)</li>
                  <li>• College Board Official SAT Practice Tests</li>
                  <li>• Princeton Review SAT Prep</li>
                </ul>
              </div>
              <div className='space-y-2'>
                <h4 className='font-medium'>ACT Preparation</h4>
                <ul className='text-muted-foreground space-y-1 text-sm'>
                  <li>• ACT Academy (Free)</li>
                  <li>• Official ACT Prep Guide</li>
                  <li>• Kaplan ACT Prep</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ProfileSetupCheck>
  );
}
