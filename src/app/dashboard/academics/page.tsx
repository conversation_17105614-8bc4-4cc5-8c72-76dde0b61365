import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Icons } from '@/components/icons';
import Link from 'next/link';

export default function AcademicsPage() {
  // Mock data - in real app, this would come from API
  const academicData = {
    gpa: {
      cumulative: 3.85,
      weighted: 4.12,
      lastUpdated: '1 week ago'
    },
    testScores: {
      sat: {
        total: 1450,
        math: 750,
        verbal: 700,
        date: 'October 2024'
      },
      act: {
        composite: 32,
        english: 34,
        math: 31,
        reading: 33,
        science: 30,
        date: 'September 2024'
      },
      psat: {
        total: 1380,
        date: 'October 2023'
      }
    },
    apCourses: [
      { name: 'AP Calculus BC', year: '2024', score: 5 },
      { name: 'AP Physics C', year: '2024', score: 4 },
      { name: 'AP English Literature', year: '2024', score: 4 },
      { name: 'AP US History', year: '2023', score: 5 },
      { name: 'AP Chemistry', year: '2023', score: 4 }
    ],
    transcripts: [
      {
        name: 'Senior Year Transcript',
        status: 'uploaded',
        date: '2 weeks ago'
      },
      {
        name: 'Junior Year Transcript',
        status: 'uploaded',
        date: '1 month ago'
      },
      {
        name: 'Sophomore Year Transcript',
        status: 'uploaded',
        date: '1 month ago'
      }
    ]
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>
            Academic Records
          </h2>
          <p className='text-muted-foreground'>
            Manage your GPA, standardized tests, AP performance, and transcripts
          </p>
        </div>
        <Button asChild>
          <Link href='/dashboard/academics/add'>
            <Icons.add className='mr-2 h-4 w-4' />
            Add Record
          </Link>
        </Button>
      </div>

      {/* GPA Overview */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Cumulative GPA
            </CardTitle>
            <Icons.graduationCap className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {academicData.gpa.cumulative}
            </div>
            <p className='text-muted-foreground text-xs'>
              Unweighted • Updated {academicData.gpa.lastUpdated}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Weighted GPA</CardTitle>
            <Icons.graduationCap className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {academicData.gpa.weighted}
            </div>
            <p className='text-muted-foreground text-xs'>
              Including AP/Honors courses
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>SAT Score</CardTitle>
            <Icons.calculator className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {academicData.testScores.sat.total}
            </div>
            <p className='text-muted-foreground text-xs'>
              {academicData.testScores.sat.date}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>AP Courses</CardTitle>
            <Icons.award className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {academicData.apCourses.length}
            </div>
            <p className='text-muted-foreground text-xs'>
              Avg. score:{' '}
              {(
                academicData.apCourses.reduce(
                  (sum, course) => sum + course.score,
                  0
                ) / academicData.apCourses.length
              ).toFixed(1)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Standardized Test Scores Detail */}
      <div className='grid gap-4 md:grid-cols-2'>
        <Card>
          <CardHeader>
            <CardTitle>SAT Scores</CardTitle>
            <CardDescription>Standardized test performance</CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>Total Score</span>
              <span className='text-lg font-bold'>
                {academicData.testScores.sat.total}
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm'>Math</span>
              <span className='font-medium'>
                {academicData.testScores.sat.math}
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm'>Evidence-Based Reading & Writing</span>
              <span className='font-medium'>
                {academicData.testScores.sat.verbal}
              </span>
            </div>
            <div className='text-muted-foreground flex items-center justify-between text-xs'>
              <span>Test Date</span>
              <span>{academicData.testScores.sat.date}</span>
            </div>
            <Button asChild variant='outline' className='w-full'>
              <Link href='/dashboard/academics/test-scores'>
                <Icons.calculator className='mr-2 h-4 w-4' />
                Update SAT Scores
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>ACT Scores</CardTitle>
            <CardDescription>Alternative standardized test</CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>Composite Score</span>
              <span className='text-lg font-bold'>
                {academicData.testScores.act.composite}
              </span>
            </div>
            <div className='grid grid-cols-2 gap-2 text-sm'>
              <div className='flex justify-between'>
                <span>English</span>
                <span className='font-medium'>
                  {academicData.testScores.act.english}
                </span>
              </div>
              <div className='flex justify-between'>
                <span>Math</span>
                <span className='font-medium'>
                  {academicData.testScores.act.math}
                </span>
              </div>
              <div className='flex justify-between'>
                <span>Reading</span>
                <span className='font-medium'>
                  {academicData.testScores.act.reading}
                </span>
              </div>
              <div className='flex justify-between'>
                <span>Science</span>
                <span className='font-medium'>
                  {academicData.testScores.act.science}
                </span>
              </div>
            </div>
            <div className='text-muted-foreground flex items-center justify-between text-xs'>
              <span>Test Date</span>
              <span>{academicData.testScores.act.date}</span>
            </div>
            <Button asChild variant='outline' className='w-full'>
              <Link href='/dashboard/academics/test-scores'>
                <Icons.calculator className='mr-2 h-4 w-4' />
                Update ACT Scores
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* AP Courses */}
      <Card>
        <CardHeader>
          <CardTitle>AP Courses & Scores</CardTitle>
          <CardDescription>
            Advanced Placement coursework and exam results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            {academicData.apCourses.map((course, index) => (
              <div
                key={index}
                className='flex items-center justify-between rounded-lg border p-3'
              >
                <div className='flex items-center space-x-3'>
                  <Icons.award className='h-5 w-5 text-yellow-500' />
                  <div>
                    <p className='font-medium'>{course.name}</p>
                    <p className='text-muted-foreground text-sm'>
                      Year: {course.year}
                    </p>
                  </div>
                </div>
                <Badge
                  variant={
                    course.score >= 4
                      ? 'default'
                      : course.score >= 3
                        ? 'secondary'
                        : 'destructive'
                  }
                >
                  Score: {course.score}
                </Badge>
              </div>
            ))}
          </div>
          <Button asChild variant='outline' className='mt-4 w-full'>
            <Link href='/dashboard/academics/ap-courses'>
              <Icons.award className='mr-2 h-4 w-4' />
              Manage AP Performance
            </Link>
          </Button>
        </CardContent>
      </Card>

      {/* Transcripts */}
      <Card>
        <CardHeader>
          <CardTitle>Transcripts</CardTitle>
          <CardDescription>Official academic records</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            {academicData.transcripts.map((transcript, index) => (
              <div
                key={index}
                className='flex items-center justify-between rounded-lg border p-3'
              >
                <div className='flex items-center space-x-3'>
                  <Icons.fileText className='h-5 w-5 text-blue-500' />
                  <div>
                    <p className='font-medium'>{transcript.name}</p>
                    <p className='text-muted-foreground text-sm'>
                      Uploaded {transcript.date}
                    </p>
                  </div>
                </div>
                <div className='flex items-center space-x-2'>
                  <Badge variant='default'>
                    {transcript.status === 'uploaded' ? 'Uploaded' : 'Pending'}
                  </Badge>
                  <Button size='sm' variant='outline'>
                    View
                  </Button>
                </div>
              </div>
            ))}
          </div>
          <Button asChild variant='outline' className='mt-4 w-full'>
            <Link href='/dashboard/academics/transcripts'>
              <Icons.fileText className='mr-2 h-4 w-4' />
              Upload Transcript
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
