'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs';
import { Icons } from '@/components/icons';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useApiGet, useApiPost, useApiPut } from '@/lib/api-client';
import { toast } from 'sonner';
import { FileUploader } from '@/components/file-uploader';
import { TranscriptFile, DocumentFile, CourseRecord } from '@/types/application';

export default function TranscriptsEnhancedPage() {
  const { user } = useCurrentUser();
  const [activeTab, setActiveTab] = useState('overview');
  const [isAddingTranscript, setIsAddingTranscript] = useState(false);
  const [isAddingManualGPA, setIsAddingManualGPA] = useState(false);
  const [uploadFiles, setUploadFiles] = useState<File[]>([]);
  
  // Form states
  const [newTranscript, setNewTranscript] = useState({
    academic_year: '',
    quarter: '',
    gpa: '',
    courses: [] as CourseRecord[]
  });
  
  const [newCourse, setNewCourse] = useState({
    name: '',
    grade: '',
    credits: '',
    semester: ''
  });

  const [manualGPA, setManualGPA] = useState({
    weighted: '',
    unweighted: '',
    scale: '4.0'
  });

  // Get student profile
  const {
    data: studentProfile,
    loading: profileLoading,
    error: profileError
  } = useApiGet<any>('/api/profile/student');

  // Get academic info
  const {
    data: academicInfo,
    loading: academicLoading,
    error: academicError,
    fetch: fetchAcademicInfo
  } = useApiGet<any>(
    studentProfile?.id ? `/api/students/${studentProfile.id}/academic` : ''
  );

  // API hooks
  const updateAcademicApi = useApiPut();
  const uploadDocumentApi = useApiPost();

  useEffect(() => {
    if (studentProfile?.id) {
      fetchAcademicInfo();
    }
  }, [studentProfile?.id, fetchAcademicInfo]);

  const handleAddCourse = () => {
    if (!newCourse.name || !newCourse.grade || !newCourse.credits) {
      toast.error('Please fill in all course fields');
      return;
    }

    const course: CourseRecord = {
      name: newCourse.name,
      grade: newCourse.grade,
      credits: parseFloat(newCourse.credits),
      semester: newCourse.semester
    };

    setNewTranscript(prev => ({
      ...prev,
      courses: [...prev.courses, course]
    }));

    setNewCourse({
      name: '',
      grade: '',
      credits: '',
      semester: ''
    });
  };

  const handleRemoveCourse = (index: number) => {
    setNewTranscript(prev => ({
      ...prev,
      courses: prev.courses.filter((_, i) => i !== index)
    }));
  };

  const handleSaveManualTranscript = async () => {
    if (!studentProfile?.id) return;

    try {
      const transcriptData = {
        academic_year: newTranscript.academic_year,
        quarter: newTranscript.quarter,
        gpa: parseFloat(newTranscript.gpa),
        courses: newTranscript.courses,
        status: 'verified' as const,
        upload_date: new Date().toISOString(),
        id: Date.now().toString(),
        filename: `${newTranscript.academic_year}-${newTranscript.quarter}-manual-entry`,
        file_url: ''
      };

      const currentTranscripts = academicInfo?.transcripts || [];
      const updatedAcademicInfo = {
        ...academicInfo,
        transcripts: [...currentTranscripts, transcriptData],
        data_entry_method: academicInfo?.data_entry_method === 'upload_only' ? 'both' : 'form_only'
      };

      const response = await updateAcademicApi.execute(() => 
        fetch(`/api/students/${studentProfile.id}/academic`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updatedAcademicInfo)
        }).then(res => res.json())
      );

      if (response.success) {
        toast.success('Transcript added successfully!');
        fetchAcademicInfo();
        setNewTranscript({
          academic_year: '',
          quarter: '',
          gpa: '',
          courses: []
        });
        setIsAddingTranscript(false);
      } else {
        toast.error('Failed to add transcript');
      }
    } catch (error) {
      console.error('Error adding transcript:', error);
      toast.error('Failed to add transcript');
    }
  };

  const handleSaveManualGPA = async () => {
    if (!studentProfile?.id) return;

    try {
      const gpaData = {
        weighted: parseFloat(manualGPA.weighted),
        unweighted: parseFloat(manualGPA.unweighted),
        scale: parseFloat(manualGPA.scale),
        manual_entry: true
      };

      const updatedAcademicInfo = {
        ...academicInfo,
        gpa: gpaData,
        data_entry_method: academicInfo?.data_entry_method === 'upload_only' ? 'both' : 'form_only'
      };

      const response = await updateAcademicApi.execute(() => 
        fetch(`/api/students/${studentProfile.id}/academic`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updatedAcademicInfo)
        }).then(res => res.json())
      );

      if (response.success) {
        toast.success('GPA updated successfully!');
        fetchAcademicInfo();
        setManualGPA({
          weighted: '',
          unweighted: '',
          scale: '4.0'
        });
        setIsAddingManualGPA(false);
      } else {
        toast.error('Failed to update GPA');
      }
    } catch (error) {
      console.error('Error updating GPA:', error);
      toast.error('Failed to update GPA');
    }
  };

  const handleFileUpload = async (files: File[]) => {
    if (!studentProfile?.id || files.length === 0) return;

    try {
      // In a real implementation, you would upload files to a storage service
      // For now, we'll simulate the upload
      const uploadedFiles = files.map(file => ({
        id: Date.now().toString() + Math.random(),
        filename: file.name,
        file_url: URL.createObjectURL(file), // Temporary URL for demo
        upload_date: new Date().toISOString(),
        document_type: 'transcript',
        status: 'pending' as const
      }));

      const currentGradeReports = academicInfo?.grade_reports || [];
      const updatedAcademicInfo = {
        ...academicInfo,
        grade_reports: [...currentGradeReports, ...uploadedFiles],
        data_entry_method: academicInfo?.data_entry_method === 'form_only' ? 'both' : 'upload_only'
      };

      const response = await updateAcademicApi.execute(() => 
        fetch(`/api/students/${studentProfile.id}/academic`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updatedAcademicInfo)
        }).then(res => res.json())
      );

      if (response.success) {
        toast.success('Documents uploaded successfully!');
        fetchAcademicInfo();
        setUploadFiles([]);
      } else {
        toast.error('Failed to upload documents');
      }
    } catch (error) {
      console.error('Error uploading documents:', error);
      toast.error('Failed to upload documents');
    }
  };

  const getDataEntryMethodBadge = (method?: string) => {
    switch (method) {
      case 'form_only':
        return <Badge variant="secondary">Manual Entry</Badge>;
      case 'upload_only':
        return <Badge variant="outline">Document Upload</Badge>;
      case 'both':
        return <Badge>Dual Support</Badge>;
      default:
        return <Badge variant="secondary">Not Set</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return <Badge className="bg-green-100 text-green-800">Verified</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (profileLoading || academicLoading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>Academic Records</h2>
            <p className='text-muted-foreground'>Loading your academic information...</p>
          </div>
        </div>
      </div>
    );
  }

  if (profileError || academicError) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>Academic Records</h2>
            <p className='text-muted-foreground text-red-600'>
              Error loading data: {profileError || academicError}
            </p>
          </div>
        </div>
      </div>
    );
  }

  const transcripts: TranscriptFile[] = academicInfo?.transcripts || [];
  const gradeReports: DocumentFile[] = academicInfo?.grade_reports || [];
  const gpa = academicInfo?.gpa;

  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* Header */}
        <div className='flex items-center justify-between space-y-2'>
          <div>
            <h2 className='text-3xl font-bold tracking-tight'>Academic Records</h2>
            <p className='text-muted-foreground'>
              Manage your GPA and transcripts with dual support for manual entry and document uploads
            </p>
          </div>
          <div className='flex items-center space-x-2'>
            {getDataEntryMethodBadge(academicInfo?.data_entry_method)}
          </div>
        </div>

        {/* Stats Cards */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Weighted GPA</CardTitle>
              <Icons.graduationCap className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {gpa?.weighted ? gpa.weighted.toFixed(2) : 'N/A'}
              </div>
              <p className='text-muted-foreground text-xs'>
                {gpa?.manual_entry ? 'Manual Entry' : 'From Documents'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Unweighted GPA</CardTitle>
              <Icons.calculator className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {gpa?.unweighted ? gpa.unweighted.toFixed(2) : 'N/A'}
              </div>
              <p className='text-muted-foreground text-xs'>
                Scale: {gpa?.scale || '4.0'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Transcripts</CardTitle>
              <Icons.fileText className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{transcripts.length}</div>
              <p className='text-muted-foreground text-xs'>
                {transcripts.filter((t) => t.status === 'verified').length} verified
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Documents</CardTitle>
              <Icons.fileText className='text-muted-foreground h-4 w-4' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{gradeReports.length}</div>
              <p className='text-muted-foreground text-xs'>
                {gradeReports.filter((d) => d.status === 'verified').length} verified
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Tabs for different views */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className='space-y-4'>
          <TabsList>
            <TabsTrigger value='overview'>Overview</TabsTrigger>
            <TabsTrigger value='manual-entry'>Manual Entry</TabsTrigger>
            <TabsTrigger value='document-upload'>Document Upload</TabsTrigger>
          </TabsList>

          <TabsContent value='overview' className='space-y-4'>
            {/* GPA Section */}
            <Card>
              <CardHeader className='flex flex-row items-center justify-between'>
                <div>
                  <CardTitle>GPA Information</CardTitle>
                  <CardDescription>Your current grade point average</CardDescription>
                </div>
                <Button
                  variant='outline'
                  onClick={() => setIsAddingManualGPA(true)}
                >
                  <Icons.edit className='mr-2 h-4 w-4' />
                  Update GPA
                </Button>
              </CardHeader>
              <CardContent>
                {gpa ? (
                  <div className='grid gap-4 md:grid-cols-3'>
                    <div>
                      <Label className='text-sm font-medium'>Weighted GPA</Label>
                      <p className='text-2xl font-bold'>{gpa.weighted?.toFixed(2) || 'N/A'}</p>
                    </div>
                    <div>
                      <Label className='text-sm font-medium'>Unweighted GPA</Label>
                      <p className='text-2xl font-bold'>{gpa.unweighted?.toFixed(2) || 'N/A'}</p>
                    </div>
                    <div>
                      <Label className='text-sm font-medium'>Scale</Label>
                      <p className='text-2xl font-bold'>{gpa.scale || '4.0'}</p>
                    </div>
                  </div>
                ) : (
                  <div className='text-center py-8'>
                    <Icons.graduationCap className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
                    <h3 className='mb-2 text-lg font-semibold'>No GPA Information</h3>
                    <p className='text-muted-foreground mb-4'>
                      Add your GPA information manually or upload transcripts
                    </p>
                    <Button onClick={() => setIsAddingManualGPA(true)}>
                      <Icons.plus className='mr-2 h-4 w-4' />
                      Add GPA
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='manual-entry' className='space-y-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between'>
                <div>
                  <CardTitle>Manual Transcript Entry</CardTitle>
                  <CardDescription>
                    Enter your academic records manually for calculations and analysis
                  </CardDescription>
                </div>
                <Button onClick={() => setIsAddingTranscript(true)}>
                  <Icons.plus className='mr-2 h-4 w-4' />
                  Add Transcript
                </Button>
              </CardHeader>
              <CardContent>
                {transcripts.length === 0 ? (
                  <div className='text-center py-8'>
                    <Icons.fileText className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
                    <h3 className='mb-2 text-lg font-semibold'>No Transcripts Yet</h3>
                    <p className='text-muted-foreground mb-4'>
                      Start by adding your first transcript manually
                    </p>
                    <Button onClick={() => setIsAddingTranscript(true)}>
                      <Icons.plus className='mr-2 h-4 w-4' />
                      Add Your First Transcript
                    </Button>
                  </div>
                ) : (
                  <div className='space-y-4'>
                    {transcripts.map((transcript) => (
                      <div
                        key={transcript.id}
                        className='hover:bg-muted/50 flex items-center justify-between rounded-lg border p-4 transition-colors'
                      >
                        <div className='space-y-1'>
                          <div className='flex items-center space-x-2'>
                            <h4 className='font-medium'>
                              {transcript.academic_year} - {transcript.quarter}
                            </h4>
                            {getStatusBadge(transcript.status)}
                          </div>
                          <p className='text-muted-foreground text-sm'>
                            GPA: {transcript.gpa?.toFixed(2) || 'N/A'}
                            {transcript.courses && (
                              <span className='ml-2'>
                                • {transcript.courses.length} courses
                              </span>
                            )}
                          </p>
                        </div>
                        <div className='flex items-center space-x-2'>
                          <Button size='sm' variant='outline'>
                            <Icons.eye className='h-4 w-4' />
                          </Button>
                          <Button size='sm' variant='outline'>
                            <Icons.edit className='h-4 w-4' />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='document-upload' className='space-y-4'>
            <Card>
              <CardHeader>
                <CardTitle>Document Upload</CardTitle>
                <CardDescription>
                  Upload official transcripts, grade reports, and other academic documents
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FileUploader
                  value={uploadFiles}
                  onValueChange={setUploadFiles}
                  onUpload={handleFileUpload}
                  accept={{
                    'application/pdf': ['.pdf'],
                    'image/*': ['.png', '.jpg', '.jpeg']
                  }}
                  maxFiles={5}
                  maxSize={10 * 1024 * 1024} // 10MB
                  multiple
                />
                
                {gradeReports.length > 0 && (
                  <div className='mt-6 space-y-4'>
                    <h4 className='font-medium'>Uploaded Documents</h4>
                    {gradeReports.map((doc) => (
                      <div
                        key={doc.id}
                        className='hover:bg-muted/50 flex items-center justify-between rounded-lg border p-4 transition-colors'
                      >
                        <div className='flex items-center space-x-3'>
                          <Icons.fileText className='text-muted-foreground h-5 w-5' />
                          <div>
                            <h5 className='font-medium'>{doc.filename}</h5>
                            <p className='text-muted-foreground text-sm'>
                              Uploaded {new Date(doc.upload_date).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className='flex items-center space-x-2'>
                          {getStatusBadge(doc.status)}
                          <Button size='sm' variant='outline'>
                            <Icons.download className='h-4 w-4' />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Add Manual GPA Dialog */}
        <Dialog open={isAddingManualGPA} onOpenChange={setIsAddingManualGPA}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Update GPA Information</DialogTitle>
              <DialogDescription>
                Enter your current GPA information manually
              </DialogDescription>
            </DialogHeader>
            <div className='grid gap-4 py-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='weighted'>Weighted GPA</Label>
                  <Input
                    id='weighted'
                    type='number'
                    step='0.01'
                    value={manualGPA.weighted}
                    onChange={(e) => setManualGPA({ ...manualGPA, weighted: e.target.value })}
                    placeholder='4.25'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='unweighted'>Unweighted GPA</Label>
                  <Input
                    id='unweighted'
                    type='number'
                    step='0.01'
                    value={manualGPA.unweighted}
                    onChange={(e) => setManualGPA({ ...manualGPA, unweighted: e.target.value })}
                    placeholder='3.85'
                  />
                </div>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='scale'>GPA Scale</Label>
                <Select
                  value={manualGPA.scale}
                  onValueChange={(value) => setManualGPA({ ...manualGPA, scale: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select GPA scale' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='4.0'>4.0 Scale</SelectItem>
                    <SelectItem value='5.0'>5.0 Scale</SelectItem>
                    <SelectItem value='100'>100 Point Scale</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant='outline' onClick={() => setIsAddingManualGPA(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveManualGPA}>Save GPA</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Add Manual Transcript Dialog */}
        <Dialog open={isAddingTranscript} onOpenChange={setIsAddingTranscript}>
          <DialogContent className='max-w-4xl max-h-[80vh] overflow-y-auto'>
            <DialogHeader>
              <DialogTitle>Add Transcript Manually</DialogTitle>
              <DialogDescription>
                Enter your academic transcript information for calculations and analysis
              </DialogDescription>
            </DialogHeader>
            <div className='grid gap-6 py-4'>
              {/* Basic Info */}
              <div className='grid grid-cols-3 gap-4'>
                <div className='space-y-2'>
                  <Label htmlFor='academic_year'>Academic Year</Label>
                  <Input
                    id='academic_year'
                    value={newTranscript.academic_year}
                    onChange={(e) => setNewTranscript({ ...newTranscript, academic_year: e.target.value })}
                    placeholder='2023-2024'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='quarter'>Quarter/Semester</Label>
                  <Select
                    value={newTranscript.quarter}
                    onValueChange={(value) => setNewTranscript({ ...newTranscript, quarter: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select quarter' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Fall'>Fall</SelectItem>
                      <SelectItem value='Spring'>Spring</SelectItem>
                      <SelectItem value='Summer'>Summer</SelectItem>
                      <SelectItem value='Winter'>Winter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='gpa'>GPA</Label>
                  <Input
                    id='gpa'
                    type='number'
                    step='0.01'
                    value={newTranscript.gpa}
                    onChange={(e) => setNewTranscript({ ...newTranscript, gpa: e.target.value })}
                    placeholder='3.85'
                  />
                </div>
              </div>

              {/* Course Entry */}
              <div className='space-y-4'>
                <h4 className='font-medium'>Add Courses</h4>
                <div className='grid grid-cols-4 gap-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='course_name'>Course Name</Label>
                    <Input
                      id='course_name'
                      value={newCourse.name}
                      onChange={(e) => setNewCourse({ ...newCourse, name: e.target.value })}
                      placeholder='AP Calculus BC'
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='grade'>Grade</Label>
                    <Select
                      value={newCourse.grade}
                      onValueChange={(value) => setNewCourse({ ...newCourse, grade: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Grade' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='A+'>A+</SelectItem>
                        <SelectItem value='A'>A</SelectItem>
                        <SelectItem value='A-'>A-</SelectItem>
                        <SelectItem value='B+'>B+</SelectItem>
                        <SelectItem value='B'>B</SelectItem>
                        <SelectItem value='B-'>B-</SelectItem>
                        <SelectItem value='C+'>C+</SelectItem>
                        <SelectItem value='C'>C</SelectItem>
                        <SelectItem value='C-'>C-</SelectItem>
                        <SelectItem value='D+'>D+</SelectItem>
                        <SelectItem value='D'>D</SelectItem>
                        <SelectItem value='F'>F</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='credits'>Credits</Label>
                    <Input
                      id='credits'
                      type='number'
                      step='0.5'
                      value={newCourse.credits}
                      onChange={(e) => setNewCourse({ ...newCourse, credits: e.target.value })}
                      placeholder='1.0'
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label>&nbsp;</Label>
                    <Button onClick={handleAddCourse} className='w-full'>
                      <Icons.plus className='mr-2 h-4 w-4' />
                      Add Course
                    </Button>
                  </div>
                </div>

                {/* Course List */}
                {newTranscript.courses.length > 0 && (
                  <div className='space-y-2'>
                    <h5 className='font-medium'>Added Courses</h5>
                    <div className='space-y-2'>
                      {newTranscript.courses.map((course, index) => (
                        <div
                          key={index}
                          className='flex items-center justify-between rounded-lg border p-3'
                        >
                          <div className='flex items-center space-x-4'>
                            <span className='font-medium'>{course.name}</span>
                            <Badge variant='outline'>{course.grade}</Badge>
                            <span className='text-muted-foreground text-sm'>
                              {course.credits} credits
                            </span>
                          </div>
                          <Button
                            size='sm'
                            variant='outline'
                            onClick={() => handleRemoveCourse(index)}
                          >
                            <Icons.trash className='h-4 w-4' />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <DialogFooter>
              <Button variant='outline' onClick={() => setIsAddingTranscript(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveManualTranscript}>Save Transcript</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </ProfileSetupCheck>
  );
}
