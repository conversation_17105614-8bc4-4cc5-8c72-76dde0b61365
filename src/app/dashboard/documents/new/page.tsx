'use client';

import { CreateDocumentForm } from '@/components/documents/create-document-form';
import { ProfileSetupCheck } from '@/components/profile/profile-setup-check';
import { useRouter } from 'next/navigation';

export default function NewDocumentPage() {
  const router = useRouter();

  const handleSuccess = (document: any) => {
    // Navigate to the created document
    router.push(`/dashboard/documents/${document.id}`);
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <ProfileSetupCheck requireStudentProfile={true}>
      <CreateDocumentForm onSuccess={handleSuccess} onCancel={handleCancel} />
    </ProfileSetupCheck>
  );
}
