'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Icons } from '@/components/icons';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import Link from 'next/link';

interface Document {
  id: string;
  title: string;
  type:
    | 'essay'
    | 'personal_statement'
    | 'transcript'
    | 'activity_resume'
    | 'other';
  student: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  status: 'draft' | 'in_review' | 'needs_revision' | 'completed';
  createdAt: string;
  updatedAt: string;
  deadline?: string;
  wordCount: number;
  wordLimit?: number;
  googleDocId?: string;
  hasGoogleDoc: boolean;
}

const mockDocuments: Document[] = [
  {
    id: '1',
    title: 'Harvard Personal Statement',
    type: 'personal_statement',
    student: {
      id: '1',
      name: 'Sarah Johnson',
      email: '<EMAIL>'
    },
    status: 'completed',
    createdAt: '2025-07-01T10:30:00Z',
    updatedAt: '2025-07-04T15:20:00Z',
    deadline: '2025-07-10T23:59:00Z',
    wordCount: 650,
    wordLimit: 650,
    googleDocId: 'abc123',
    hasGoogleDoc: true
  },
  {
    id: '2',
    title: 'MIT Supplemental Essay - Why Engineering?',
    type: 'essay',
    student: {
      id: '2',
      name: 'Michael Chen',
      email: '<EMAIL>'
    },
    status: 'in_review',
    createdAt: '2025-07-02T14:15:00Z',
    updatedAt: '2025-07-04T09:30:00Z',
    deadline: '2025-07-15T23:59:00Z',
    wordCount: 280,
    wordLimit: 300,
    googleDocId: 'def456',
    hasGoogleDoc: true
  },
  {
    id: '3',
    title: 'Stanford Activity Resume',
    type: 'activity_resume',
    student: {
      id: '3',
      name: 'Emily Rodriguez',
      email: '<EMAIL>'
    },
    status: 'draft',
    createdAt: '2025-07-03T09:45:00Z',
    updatedAt: '2025-07-03T16:20:00Z',
    deadline: '2025-07-20T23:59:00Z',
    wordCount: 0,
    hasGoogleDoc: false
  }
];

const statusColors = {
  draft: 'bg-gray-100 text-gray-800',
  in_review: 'bg-blue-100 text-blue-800',
  needs_revision: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-green-100 text-green-800'
};

const typeColors = {
  essay: 'bg-purple-100 text-purple-800',
  personal_statement: 'bg-blue-100 text-blue-800',
  transcript: 'bg-orange-100 text-orange-800',
  activity_resume: 'bg-green-100 text-green-800',
  other: 'bg-gray-100 text-gray-800'
};

export default function AllDocumentsPage() {
  const [documents, setDocuments] = useState<Document[]>(mockDocuments);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [studentFilter, setStudentFilter] = useState<string>('all');

  const filteredDocuments = documents.filter((doc) => {
    const matchesSearch =
      doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.student.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || doc.status === statusFilter;
    const matchesType = typeFilter === 'all' || doc.type === typeFilter;
    const matchesStudent =
      studentFilter === 'all' || doc.student.id === studentFilter;

    return matchesSearch && matchesStatus && matchesType && matchesStudent;
  });

  const getStats = () => {
    return {
      total: documents.length,
      draft: documents.filter((d) => d.status === 'draft').length,
      inReview: documents.filter((d) => d.status === 'in_review').length,
      needsRevision: documents.filter((d) => d.status === 'needs_revision')
        .length,
      completed: documents.filter((d) => d.status === 'completed').length,
      withGoogleDocs: documents.filter((d) => d.hasGoogleDoc).length
    };
  };

  const stats = getStats();
  const uniqueStudents = Array.from(new Set(documents.map((d) => d.student.id)))
    .map((id) => documents.find((d) => d.student.id === id)?.student)
    .filter(Boolean);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getDaysUntilDeadline = (deadline?: string) => {
    if (!deadline) return null;
    const deadlineDate = new Date(deadline);
    const now = new Date();
    const diffInDays = Math.ceil(
      (deadlineDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );
    return diffInDays;
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>All Documents</h1>
          <p className='text-muted-foreground'>
            View and manage all student documents across your assigned students
          </p>
        </div>
        <Button asChild>
          <Link href='/dashboard/documents/new'>
            <Icons.plus className='mr-2 h-4 w-4' />
            Create Document
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-5'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Documents
            </CardTitle>
            <Icons.fileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.total}</div>
            <p className='text-muted-foreground text-xs'>Across all students</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>In Review</CardTitle>
            <Icons.eye className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.inReview}</div>
            <p className='text-muted-foreground text-xs'>Awaiting feedback</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Completed</CardTitle>
            <Icons.checkCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.completed}</div>
            <p className='text-muted-foreground text-xs'>
              Ready for submission
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Google Docs</CardTitle>
            <Icons.externalLink className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.withGoogleDocs}</div>
            <p className='text-muted-foreground text-xs'>
              With Google integration
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Students</CardTitle>
            <Icons.users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{uniqueStudents.length}</div>
            <p className='text-muted-foreground text-xs'>Active students</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Documents</CardTitle>
          <CardDescription>
            {filteredDocuments.length} of {documents.length} documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex flex-col gap-4 md:flex-row md:items-center'>
            <div className='flex-1'>
              <Input
                placeholder='Search documents by title or student name...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='max-w-sm'
              />
            </div>
            <div className='flex gap-2'>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className='w-[140px]'>
                  <SelectValue placeholder='All Status' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Status</SelectItem>
                  <SelectItem value='draft'>Draft</SelectItem>
                  <SelectItem value='in_review'>In Review</SelectItem>
                  <SelectItem value='needs_revision'>Needs Revision</SelectItem>
                  <SelectItem value='completed'>Completed</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className='w-[140px]'>
                  <SelectValue placeholder='All Types' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Types</SelectItem>
                  <SelectItem value='essay'>Essay</SelectItem>
                  <SelectItem value='personal_statement'>
                    Personal Statement
                  </SelectItem>
                  <SelectItem value='transcript'>Transcript</SelectItem>
                  <SelectItem value='activity_resume'>
                    Activity Resume
                  </SelectItem>
                  <SelectItem value='other'>Other</SelectItem>
                </SelectContent>
              </Select>

              <Select value={studentFilter} onValueChange={setStudentFilter}>
                <SelectTrigger className='w-[160px]'>
                  <SelectValue placeholder='All Students' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Students</SelectItem>
                  {uniqueStudents.map((student) => (
                    <SelectItem key={student?.id} value={student?.id || ''}>
                      {student?.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents List */}
      <div className='space-y-4'>
        {filteredDocuments.length === 0 ? (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-12'>
              <Icons.fileText className='text-muted-foreground mb-4 h-12 w-12' />
              <h3 className='mb-2 text-lg font-semibold'>No documents found</h3>
              <p className='text-muted-foreground mb-4 text-center'>
                {searchTerm ||
                statusFilter !== 'all' ||
                typeFilter !== 'all' ||
                studentFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'No documents available'}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredDocuments.map((doc) => (
            <Card key={doc.id} className='transition-shadow hover:shadow-md'>
              <CardContent className='p-6'>
                <div className='flex items-start justify-between'>
                  <div className='flex-1'>
                    <div className='mb-3 flex items-center gap-3'>
                      <Avatar className='h-8 w-8'>
                        <AvatarImage src={doc.student.avatar} />
                        <AvatarFallback>
                          {doc.student.name
                            .split(' ')
                            .map((n) => n[0])
                            .join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className='font-semibold'>{doc.title}</h3>
                        <p className='text-muted-foreground text-sm'>
                          by {doc.student.name}
                        </p>
                      </div>
                    </div>

                    <div className='mb-3 flex items-center gap-2'>
                      <Badge className={statusColors[doc.status]}>
                        {doc.status.replace('_', ' ')}
                      </Badge>
                      <Badge className={typeColors[doc.type]}>
                        {doc.type.replace('_', ' ')}
                      </Badge>
                      {doc.hasGoogleDoc && (
                        <Badge variant='outline'>
                          <Icons.externalLink className='mr-1 h-3 w-3' />
                          Google Docs
                        </Badge>
                      )}
                    </div>

                    <div className='text-muted-foreground mb-3 flex items-center gap-4 text-sm'>
                      <div className='flex items-center gap-1'>
                        <Icons.calendar className='h-4 w-4' />
                        Created {formatDate(doc.createdAt)}
                      </div>
                      <div className='flex items-center gap-1'>
                        <Icons.clock className='h-4 w-4' />
                        Updated {formatDate(doc.updatedAt)}
                      </div>
                      {doc.deadline && (
                        <div className='flex items-center gap-1'>
                          <Icons.alertCircle className='h-4 w-4' />
                          Due in {getDaysUntilDeadline(doc.deadline)} days
                        </div>
                      )}
                      {doc.wordLimit && (
                        <div className='flex items-center gap-1'>
                          <Icons.fileText className='h-4 w-4' />
                          {doc.wordCount}/{doc.wordLimit} words
                        </div>
                      )}
                    </div>

                    {doc.wordLimit && doc.wordCount > 0 && (
                      <div className='mb-3'>
                        <div className='mb-1 flex items-center justify-between text-sm'>
                          <span>Progress</span>
                          <span>
                            {Math.round((doc.wordCount / doc.wordLimit) * 100)}%
                          </span>
                        </div>
                        <Progress
                          value={(doc.wordCount / doc.wordLimit) * 100}
                          className='h-2'
                        />
                      </div>
                    )}
                  </div>

                  <div className='ml-4 flex gap-2'>
                    <Button variant='outline' size='sm' asChild>
                      <Link href={`/dashboard/documents/${doc.id}`}>
                        <Icons.eye className='mr-2 h-4 w-4' />
                        View
                      </Link>
                    </Button>
                    {doc.hasGoogleDoc && (
                      <Button variant='outline' size='sm'>
                        <Icons.externalLink className='h-4 w-4' />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
