'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { useCurrentUser } from '@/hooks/use-current-user';

export default function DocumentReviewPage() {
  const { user, isConsultant, isAdmin } = useCurrentUser();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  // Mock data - in real app, this would come from API
  const documentsToReview = [
    {
      id: '1',
      title: 'Personal Statement',
      type: 'personal_statement',
      status: 'pending_review',
      studentName: '<PERSON>',
      studentEmail: '<EMAIL>',
      studentAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sarah',
      submittedAt: '2 hours ago',
      wordCount: 450,
      maxWords: 650,
      priority: 'high',
      googleDocUrl: 'https://docs.google.com/document/d/abc123/edit',
      lastReviewedAt: null,
      comments: 0,
      suggestions: 0,
      deadline: '2024-11-01'
    },
    {
      id: '2',
      title: 'Harvard Supplemental Essay',
      type: 'essay',
      status: 'in_review',
      studentName: 'Michael Chen',
      studentEmail: '<EMAIL>',
      studentAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=michael',
      submittedAt: '1 day ago',
      wordCount: 180,
      maxWords: 200,
      priority: 'medium',
      googleDocUrl: 'https://docs.google.com/document/d/def456/edit',
      lastReviewedAt: '6 hours ago',
      comments: 3,
      suggestions: 1,
      deadline: '2025-01-05'
    },
    {
      id: '3',
      title: 'Activity Resume',
      type: 'activity_resume',
      status: 'pending_review',
      studentName: 'Emily Rodriguez',
      studentEmail: '<EMAIL>',
      studentAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=emily',
      submittedAt: '3 days ago',
      wordCount: 800,
      maxWords: 1000,
      priority: 'low',
      googleDocUrl: 'https://docs.google.com/document/d/ghi789/edit',
      lastReviewedAt: null,
      comments: 0,
      suggestions: 0,
      deadline: '2024-11-30'
    },
    {
      id: '4',
      title: 'Stanford Supplemental - What matters to you?',
      type: 'essay',
      status: 'reviewed',
      studentName: 'Sarah Johnson',
      studentEmail: '<EMAIL>',
      studentAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sarah',
      submittedAt: '5 days ago',
      wordCount: 250,
      maxWords: 250,
      priority: 'medium',
      googleDocUrl: 'https://docs.google.com/document/d/jkl012/edit',
      lastReviewedAt: '2 days ago',
      comments: 8,
      suggestions: 3,
      deadline: '2025-01-05'
    }
  ];

  const filteredDocuments = documentsToReview.filter((doc) => {
    const matchesSearch =
      doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.studentName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || doc.status === statusFilter;
    const matchesType = typeFilter === 'all' || doc.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending_review':
        return 'bg-red-100 text-red-800';
      case 'in_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'reviewed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending_review':
        return 'Pending Review';
      case 'in_review':
        return 'In Review';
      case 'reviewed':
        return 'Reviewed';
      default:
        return 'Unknown';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'personal_statement':
        return 'Personal Statement';
      case 'essay':
        return 'Essay';
      case 'activity_resume':
        return 'Activity Resume';
      case 'transcript':
        return 'Transcript';
      default:
        return 'Document';
    }
  };

  const getDaysUntilDeadline = (deadline: string) => {
    const deadlineDate = new Date(deadline);
    const today = new Date();
    const diffTime = deadlineDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Show different content based on user role
  if (!isConsultant && !isAdmin) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <Icons.eye className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
            <h3 className='text-lg font-semibold'>Access Restricted</h3>
            <p className='text-muted-foreground mt-2 text-sm'>
              This page is only available to consultants and administrators.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Document Review</h2>
          <p className='text-muted-foreground'>
            Review and provide feedback on student documents
          </p>
        </div>
        <Button asChild>
          <Link href='/dashboard/documents'>
            <Icons.folder className='mr-2 h-4 w-4' />
            All Documents
          </Link>
        </Button>
      </div>

      {/* Filters */}
      <div className='flex items-center space-x-4'>
        <div className='max-w-sm flex-1'>
          <div className='relative'>
            <Icons.search className='text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform' />
            <Input
              placeholder='Search documents or students...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className='w-40'>
            <SelectValue placeholder='Status' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Status</SelectItem>
            <SelectItem value='pending_review'>Pending Review</SelectItem>
            <SelectItem value='in_review'>In Review</SelectItem>
            <SelectItem value='reviewed'>Reviewed</SelectItem>
          </SelectContent>
        </Select>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className='w-40'>
            <SelectValue placeholder='Type' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Types</SelectItem>
            <SelectItem value='personal_statement'>
              Personal Statement
            </SelectItem>
            <SelectItem value='essay'>Essay</SelectItem>
            <SelectItem value='activity_resume'>Activity Resume</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Overview Stats */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Pending Review
            </CardTitle>
            <Icons.eye className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {
                documentsToReview.filter((d) => d.status === 'pending_review')
                  .length
              }
            </div>
            <p className='text-muted-foreground text-xs'>
              Awaiting your review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>In Review</CardTitle>
            <Icons.fileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {documentsToReview.filter((d) => d.status === 'in_review').length}
            </div>
            <p className='text-muted-foreground text-xs'>Currently reviewing</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>High Priority</CardTitle>
            <Icons.warning className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {documentsToReview.filter((d) => d.priority === 'high').length}
            </div>
            <p className='text-muted-foreground text-xs'>Urgent documents</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Avg. Response Time
            </CardTitle>
            <Icons.calendar className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>2.5h</div>
            <p className='text-muted-foreground text-xs'>Average review time</p>
          </CardContent>
        </Card>
      </div>

      {/* Documents List */}
      <div className='space-y-4'>
        {filteredDocuments.map((document) => {
          const daysUntilDeadline = getDaysUntilDeadline(document.deadline);

          return (
            <Card key={document.id}>
              <CardHeader>
                <div className='flex items-start justify-between'>
                  <div className='flex items-center space-x-4'>
                    <Avatar className='h-10 w-10'>
                      <AvatarImage src={document.studentAvatar} />
                      <AvatarFallback>
                        {document.studentName
                          .split(' ')
                          .map((n) => n[0])
                          .join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className='flex items-center space-x-2'>
                        <CardTitle className='text-lg'>
                          {document.title}
                        </CardTitle>
                        <Badge variant='outline'>
                          {getTypeLabel(document.type)}
                        </Badge>
                        <Badge className={getStatusColor(document.status)}>
                          {getStatusText(document.status)}
                        </Badge>
                        <Badge className={getPriorityColor(document.priority)}>
                          {document.priority.toUpperCase()}
                        </Badge>
                      </div>
                      <CardDescription>
                        By {document.studentName} • Submitted{' '}
                        {document.submittedAt} • {document.wordCount} words
                        {document.maxWords && ` / ${document.maxWords} max`}
                      </CardDescription>
                    </div>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Button asChild size='sm' variant='outline'>
                      <Link
                        href={document.googleDocUrl}
                        target='_blank'
                        rel='noopener noreferrer'
                      >
                        <Icons.eye className='mr-2 h-4 w-4' />
                        Open in Google Docs
                      </Link>
                    </Button>
                    <Button asChild size='sm'>
                      <Link href={`/dashboard/documents/review/${document.id}`}>
                        Review
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className='space-y-4'>
                {/* Progress and Feedback Info */}
                <div className='grid grid-cols-2 gap-4 text-sm md:grid-cols-4'>
                  <div className='flex items-center justify-between'>
                    <span>Comments</span>
                    <span className='font-medium'>{document.comments}</span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span>Suggestions</span>
                    <span className='font-medium'>{document.suggestions}</span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span>Last Reviewed</span>
                    <span className='font-medium'>
                      {document.lastReviewedAt || 'Never'}
                    </span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span>Deadline</span>
                    <div className='flex items-center space-x-2'>
                      <span className='font-medium'>
                        {new Date(document.deadline).toLocaleDateString()}
                      </span>
                      <Badge
                        variant={
                          daysUntilDeadline <= 7
                            ? 'destructive'
                            : daysUntilDeadline <= 30
                              ? 'secondary'
                              : 'outline'
                        }
                      >
                        {daysUntilDeadline} days
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Word Count Progress */}
                {document.maxWords && (
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between text-sm'>
                      <span>Word Count Progress</span>
                      <span>
                        {Math.round(
                          (document.wordCount / document.maxWords) * 100
                        )}
                        %
                      </span>
                    </div>
                    <div className='h-2 w-full rounded-full bg-gray-200'>
                      <div
                        className='h-2 rounded-full bg-blue-600'
                        style={{
                          width: `${Math.min((document.wordCount / document.maxWords) * 100, 100)}%`
                        }}
                      />
                    </div>
                  </div>
                )}

                {/* Quick Actions */}
                <div className='flex items-center justify-between border-t pt-4'>
                  <div className='flex items-center space-x-2'>
                    <Button asChild size='sm' variant='outline'>
                      <Link
                        href={`/dashboard/students/${document.studentName.toLowerCase().replace(' ', '-')}`}
                      >
                        <Icons.user className='mr-2 h-4 w-4' />
                        Student Profile
                      </Link>
                    </Button>
                    {document.status === 'pending_review' && (
                      <Button size='sm' variant='outline'>
                        <Icons.eye className='mr-2 h-4 w-4' />
                        Start Review
                      </Button>
                    )}
                  </div>
                  <div className='text-muted-foreground text-xs'>
                    Priority: {document.priority} • Student:{' '}
                    {document.studentEmail}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredDocuments.length === 0 && (
        <Card>
          <CardContent className='flex h-64 items-center justify-center'>
            <div className='text-center'>
              <Icons.eye className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
              <h3 className='text-lg font-semibold'>No documents found</h3>
              <p className='text-muted-foreground mt-2 text-sm'>
                {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                  ? 'Try adjusting your filters'
                  : 'No documents pending review'}
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
