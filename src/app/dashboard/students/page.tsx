'use client';

import { useCurrentUser } from '@/hooks/use-current-user';
import { Icons } from '@/components/icons';

export default function StudentsPage() {
  const { isConsultant, isAdmin } = useCurrentUser();

  if (!isConsultant && !isAdmin) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <Icons.users className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
            <h3 className='text-lg font-semibold'>Access Restricted</h3>
            <p className='text-muted-foreground mt-2 text-sm'>
              This page is only available to consultants and administrators.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>
            {isConsultant ? 'My Students' : 'All Students'}
          </h2>
          <p className='text-muted-foreground'>
            {isConsultant
              ? 'Manage and track progress of your assigned students'
              : 'Overview of all students in the system'}
          </p>
        </div>
      </div>

      <div className='py-8 text-center'>
        <Icons.users className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
        <h3 className='mb-2 text-lg font-medium'>Students Page</h3>
        <p className='text-muted-foreground mb-4'>
          This page is under development. Student management features will be available soon.
        </p>
      </div>
    </div>
  );
}
