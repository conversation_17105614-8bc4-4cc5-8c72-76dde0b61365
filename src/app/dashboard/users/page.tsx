'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Icons } from '@/components/icons';
import Link from 'next/link';
import { useCurrentUser } from '@/hooks/use-current-user';

export default function UsersPage() {
  const { user, isAdmin } = useCurrentUser();
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock data - in real app, this would come from API
  const users = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'student',
      status: 'active',
      joinedAt: '2024-09-01',
      lastActive: '2 hours ago',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sarah',
      profile: {
        grade: '12th Grade',
        gpa: 3.85,
        targetSchools: 4,
        assignedConsultant: 'Dr. <PERSON> <PERSON>'
      }
    },
    {
      id: '2',
      name: 'Dr. Sarah Wilson',
      email: '<EMAIL>',
      role: 'consultant',
      status: 'active',
      joinedAt: '2024-08-15',
      lastActive: '1 hour ago',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=wilson',
      profile: {
        specialties: ['Pre-Med', 'STEM'],
        assignedStudents: 12,
        experience: '8 years'
      }
    },
    {
      id: '3',
      name: 'Michael Chen',
      email: '<EMAIL>',
      role: 'student',
      status: 'active',
      joinedAt: '2024-09-15',
      lastActive: '1 day ago',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=michael',
      profile: {
        grade: '12th Grade',
        gpa: 3.92,
        targetSchools: 5,
        assignedConsultant: 'Dr. Sarah Wilson'
      }
    },
    {
      id: '4',
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      role: 'student',
      status: 'inactive',
      joinedAt: '2024-08-20',
      lastActive: '1 week ago',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=emily',
      profile: {
        grade: '12th Grade',
        gpa: 3.78,
        targetSchools: 4,
        assignedConsultant: 'Dr. Sarah Wilson'
      }
    },
    {
      id: '5',
      name: 'Dr. James Martinez',
      email: '<EMAIL>',
      role: 'consultant',
      status: 'active',
      joinedAt: '2024-07-01',
      lastActive: '3 hours ago',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=james',
      profile: {
        specialties: ['Liberal Arts', 'Business'],
        assignedStudents: 8,
        experience: '12 years'
      }
    },
    {
      id: '6',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      joinedAt: '2024-06-01',
      lastActive: '30 minutes ago',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
      profile: {
        permissions: ['Full Access'],
        department: 'System Administration'
      }
    }
  ];

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    const matchesStatus =
      statusFilter === 'all' || user.status === statusFilter;

    return matchesSearch && matchesRole && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-purple-100 text-purple-800';
      case 'consultant':
        return 'bg-blue-100 text-blue-800';
      case 'student':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return Icons.user;
      case 'consultant':
        return Icons.userCheck;
      case 'student':
        return Icons.graduationCap;
      default:
        return Icons.user;
    }
  };

  // Show different content based on user role
  if (!isAdmin) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <Icons.users className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
            <h3 className='text-lg font-semibold'>Access Restricted</h3>
            <p className='text-muted-foreground mt-2 text-sm'>
              This page is only available to administrators.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>User Management</h2>
          <p className='text-muted-foreground'>
            Manage all users in the system
          </p>
        </div>
        <Button asChild>
          <Link href='/dashboard/users/invite'>
            <Icons.add className='mr-2 h-4 w-4' />
            Invite User
          </Link>
        </Button>
      </div>

      {/* Filters */}
      <div className='flex items-center space-x-4'>
        <div className='max-w-sm flex-1'>
          <div className='relative'>
            <Icons.search className='text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform' />
            <Input
              placeholder='Search users...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>
        </div>

        <Select value={roleFilter} onValueChange={setRoleFilter}>
          <SelectTrigger className='w-40'>
            <SelectValue placeholder='Role' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Roles</SelectItem>
            <SelectItem value='student'>Students</SelectItem>
            <SelectItem value='consultant'>Consultants</SelectItem>
            <SelectItem value='admin'>Admins</SelectItem>
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className='w-40'>
            <SelectValue placeholder='Status' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Status</SelectItem>
            <SelectItem value='active'>Active</SelectItem>
            <SelectItem value='inactive'>Inactive</SelectItem>
            <SelectItem value='suspended'>Suspended</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Overview Stats */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Users</CardTitle>
            <Icons.users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{users.length}</div>
            <p className='text-muted-foreground text-xs'>
              {users.filter((u) => u.status === 'active').length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Students</CardTitle>
            <Icons.graduationCap className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {users.filter((u) => u.role === 'student').length}
            </div>
            <p className='text-muted-foreground text-xs'>
              {Math.round(
                (users.filter((u) => u.role === 'student').length /
                  users.length) *
                  100
              )}
              % of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Consultants</CardTitle>
            <Icons.userCheck className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {users.filter((u) => u.role === 'consultant').length}
            </div>
            <p className='text-muted-foreground text-xs'>
              Avg.{' '}
              {Math.round(
                users.filter((u) => u.role === 'student').length /
                  users.filter((u) => u.role === 'consultant').length
              )}{' '}
              students each
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              New This Month
            </CardTitle>
            <Icons.calendar className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {
                users.filter(
                  (u) =>
                    new Date(u.joinedAt) >
                    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                ).length
              }
            </div>
            <p className='text-muted-foreground text-xs'>New registrations</p>
          </CardContent>
        </Card>
      </div>

      {/* Users List */}
      <div className='space-y-4'>
        {filteredUsers.map((user) => {
          const RoleIcon = getRoleIcon(user.role);

          return (
            <Card key={user.id}>
              <CardHeader>
                <div className='flex items-start justify-between'>
                  <div className='flex items-center space-x-4'>
                    <Avatar className='h-12 w-12'>
                      <AvatarImage src={user.avatar} />
                      <AvatarFallback>
                        {user.name
                          .split(' ')
                          .map((n) => n[0])
                          .join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className='flex items-center space-x-2'>
                        <CardTitle className='text-lg'>{user.name}</CardTitle>
                        <Badge className={getRoleColor(user.role)}>
                          <RoleIcon className='mr-1 h-3 w-3' />
                          {user.role.charAt(0).toUpperCase() +
                            user.role.slice(1)}
                        </Badge>
                        <Badge className={getStatusColor(user.status)}>
                          {user.status.charAt(0).toUpperCase() +
                            user.status.slice(1)}
                        </Badge>
                      </div>
                      <CardDescription>
                        {user.email} • Joined{' '}
                        {new Date(user.joinedAt).toLocaleDateString()} • Last
                        active {user.lastActive}
                      </CardDescription>
                    </div>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Button asChild size='sm' variant='outline'>
                      <Link href={`/dashboard/users/${user.id}`}>
                        View Profile
                      </Link>
                    </Button>
                    <Button size='sm' variant='outline'>
                      Edit
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className='space-y-4'>
                {/* Role-specific Information */}
                {user.role === 'student' && user.profile && (
                  <div className='grid grid-cols-2 gap-4 text-sm md:grid-cols-4'>
                    <div className='flex items-center justify-between'>
                      <span>Grade</span>
                      <span className='font-medium'>{user.profile.grade}</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span>GPA</span>
                      <span className='font-medium'>{user.profile.gpa}</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span>Target Schools</span>
                      <span className='font-medium'>
                        {user.profile.targetSchools}
                      </span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span>Consultant</span>
                      <span className='font-medium'>
                        {user.profile.assignedConsultant}
                      </span>
                    </div>
                  </div>
                )}

                {user.role === 'consultant' && user.profile && (
                  <div className='grid grid-cols-2 gap-4 text-sm md:grid-cols-3'>
                    <div className='flex items-center justify-between'>
                      <span>Specialties</span>
                      <span className='font-medium'>
                        {user.profile.specialties?.join(', ')}
                      </span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span>Students</span>
                      <span className='font-medium'>
                        {user.profile.assignedStudents}
                      </span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span>Experience</span>
                      <span className='font-medium'>
                        {user.profile.experience}
                      </span>
                    </div>
                  </div>
                )}

                {user.role === 'admin' && user.profile && (
                  <div className='grid grid-cols-2 gap-4 text-sm'>
                    <div className='flex items-center justify-between'>
                      <span>Permissions</span>
                      <span className='font-medium'>
                        {user.profile.permissions?.join(', ')}
                      </span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span>Department</span>
                      <span className='font-medium'>
                        {user.profile.department}
                      </span>
                    </div>
                  </div>
                )}

                {/* Quick Actions */}
                <div className='flex items-center justify-between border-t pt-4'>
                  <div className='flex items-center space-x-2'>
                    {user.role === 'student' && (
                      <>
                        <Button asChild size='sm' variant='outline'>
                          <Link href={`/dashboard/students/${user.id}`}>
                            <Icons.graduationCap className='mr-2 h-4 w-4' />
                            Student Profile
                          </Link>
                        </Button>
                        <Button size='sm' variant='outline'>
                          <Icons.userCheck className='mr-2 h-4 w-4' />
                          Assign Consultant
                        </Button>
                      </>
                    )}

                    {user.role === 'consultant' && (
                      <>
                        <Button asChild size='sm' variant='outline'>
                          <Link href={`/dashboard/consultants/${user.id}`}>
                            <Icons.userCheck className='mr-2 h-4 w-4' />
                            Consultant Profile
                          </Link>
                        </Button>
                        <Button size='sm' variant='outline'>
                          <Icons.users className='mr-2 h-4 w-4' />
                          Manage Students
                        </Button>
                      </>
                    )}

                    {user.status === 'active' ? (
                      <Button size='sm' variant='outline'>
                        <Icons.userX className='mr-2 h-4 w-4' />
                        Suspend
                      </Button>
                    ) : (
                      <Button size='sm' variant='outline'>
                        <Icons.userCheck className='mr-2 h-4 w-4' />
                        Activate
                      </Button>
                    )}
                  </div>
                  <div className='text-muted-foreground text-xs'>
                    ID: {user.id}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredUsers.length === 0 && (
        <Card>
          <CardContent className='flex h-64 items-center justify-center'>
            <div className='text-center'>
              <Icons.users className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
              <h3 className='text-lg font-semibold'>No users found</h3>
              <p className='text-muted-foreground mt-2 text-sm'>
                {searchTerm || roleFilter !== 'all' || statusFilter !== 'all'
                  ? 'Try adjusting your filters'
                  : 'No users in the system'}
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
