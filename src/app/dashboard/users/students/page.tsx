'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Icons } from '@/components/icons';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { AssignConsultantDialog } from '@/components/admin/assign-consultant-dialog';

// Import the correct type
type StudentWithUser = {
  id: string;
  users: {
    id: string;
    email: string;
    profile_data: {
      first_name?: string;
      last_name?: string;
    };
  };
  current_consultant?: {
    id: string;
    users: {
      id: string;
      email: string;
      profile_data: {
        first_name?: string;
        last_name?: string;
      };
    };
  };
};

interface Student {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'inactive' | 'pending';
  avatar?: string;
  createdAt: string;
  lastLoginAt?: string;
  consultant?: {
    id: string;
    name: string;
  };
  profile: {
    graduationYear?: string;
    gpa?: number;
    schoolName?: string;
    intendedMajor?: string;
  };
  stats: {
    documentsCount: number;
    completedDocuments: number;
    meetingsCount: number;
    applicationProgress: number;
  };
}

const mockStudents: Student[] = [
  {
    id: '1',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    status: 'active',
    createdAt: '2025-06-15T10:30:00Z',
    lastLoginAt: '2025-07-04T09:15:00Z',
    consultant: {
      id: '1',
      name: 'Dr. Emily Rodriguez'
    },
    profile: {
      graduationYear: '2025',
      gpa: 3.85,
      schoolName: 'Lincoln High School',
      intendedMajor: 'Computer Science'
    },
    stats: {
      documentsCount: 8,
      completedDocuments: 5,
      meetingsCount: 12,
      applicationProgress: 75
    }
  },
  {
    id: '2',
    name: 'Michael Chen',
    email: '<EMAIL>',
    status: 'active',
    createdAt: '2025-06-20T14:20:00Z',
    lastLoginAt: '2025-07-03T16:45:00Z',
    consultant: {
      id: '2',
      name: 'David Kim'
    },
    profile: {
      graduationYear: '2025',
      gpa: 4.0,
      schoolName: 'Washington High School',
      intendedMajor: 'Engineering'
    },
    stats: {
      documentsCount: 6,
      completedDocuments: 3,
      meetingsCount: 8,
      applicationProgress: 45
    }
  },
  {
    id: '3',
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    status: 'pending',
    createdAt: '2025-07-01T12:00:00Z',
    profile: {
      graduationYear: '2026',
      schoolName: 'Roosevelt High School',
      intendedMajor: 'Pre-Med'
    },
    stats: {
      documentsCount: 2,
      completedDocuments: 0,
      meetingsCount: 1,
      applicationProgress: 10
    }
  }
];

const statusColors = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-gray-100 text-gray-800',
  pending: 'bg-yellow-100 text-yellow-800'
};

export default function StudentsPage() {
  const [students, setStudents] = useState<Student[]>(mockStudents);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [consultantFilter, setConsultantFilter] = useState<string>('all');
  const [graduationYearFilter, setGraduationYearFilter] =
    useState<string>('all');

  // Assignment dialog state
  const [selectedStudentForAssignment, setSelectedStudentForAssignment] = useState<StudentWithUser | null>(null);
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);

  const filteredStudents = students.filter((student) => {
    const matchesSearch =
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.profile.schoolName
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' || student.status === statusFilter;
    const matchesConsultant =
      consultantFilter === 'all' || student.consultant?.id === consultantFilter;
    const matchesGradYear =
      graduationYearFilter === 'all' ||
      student.profile.graduationYear === graduationYearFilter;

    return (
      matchesSearch && matchesStatus && matchesConsultant && matchesGradYear
    );
  });

  const getStats = () => {
    return {
      total: students.length,
      active: students.filter((s) => s.status === 'active').length,
      pending: students.filter((s) => s.status === 'pending').length,
      withConsultant: students.filter((s) => s.consultant).length,
      avgProgress: Math.round(
        students.reduce((sum, s) => sum + s.stats.applicationProgress, 0) /
          students.length
      )
    };
  };

  const stats = getStats();
  const uniqueConsultants = Array.from(
    new Set(students.map((s) => s.consultant?.id).filter(Boolean))
  )
    .map((id) => students.find((s) => s.consultant?.id === id)?.consultant)
    .filter(Boolean);

  const uniqueGradYears = Array.from(
    new Set(students.map((s) => s.profile.graduationYear).filter(Boolean))
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatLastLogin = (dateString?: string) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return formatDate(dateString);
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 75) return 'bg-green-500';
    if (progress >= 50) return 'bg-yellow-500';
    if (progress >= 25) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const handleAssignConsultant = (student: Student) => {
    // Convert the student format to what the dialog expects
    const adaptedStudent = {
      id: student.id,
      users: {
        id: student.id,
        email: student.email,
        profile_data: {
          first_name: student.name.split(' ')[0] || '',
          last_name: student.name.split(' ').slice(1).join(' ') || ''
        }
      },
      current_consultant: student.consultant ? {
        id: student.consultant.id,
        users: {
          profile_data: {
            first_name: student.consultant.name.split(' ')[0] || '',
            last_name: student.consultant.name.split(' ').slice(1).join(' ') || ''
          }
        }
      } : undefined
    };

    setSelectedStudentForAssignment(adaptedStudent as any);
    setIsAssignDialogOpen(true);
  };

  const handleAssignmentComplete = () => {
    // In a real app, you would refetch the students data here
    // For now, we'll just show a success message
    console.log('Assignment completed, should refetch students data');
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Students</h1>
          <p className='text-muted-foreground'>
            Manage student accounts and track their progress
          </p>
        </div>
        <div className='flex gap-2'>
          <Button variant='outline'>
            <Icons.download className='mr-2 h-4 w-4' />
            Export
          </Button>
          <Button>
            <Icons.userPlus className='mr-2 h-4 w-4' />
            Add Student
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-5'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Students
            </CardTitle>
            <Icons.graduationCap className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.total}</div>
            <p className='text-muted-foreground text-xs'>
              All registered students
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Active</CardTitle>
            <Icons.checkCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.active}</div>
            <p className='text-muted-foreground text-xs'>Currently active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Pending</CardTitle>
            <Icons.clock className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.pending}</div>
            <p className='text-muted-foreground text-xs'>Awaiting approval</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              With Consultant
            </CardTitle>
            <Icons.userCheck className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.withConsultant}</div>
            <p className='text-muted-foreground text-xs'>
              Assigned to consultant
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Avg Progress</CardTitle>
            <Icons.trendingUp className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.avgProgress}%</div>
            <p className='text-muted-foreground text-xs'>
              Application progress
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Students</CardTitle>
          <CardDescription>
            {filteredStudents.length} of {students.length} students
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex flex-col gap-4 md:flex-row md:items-center'>
            <div className='flex-1'>
              <Input
                placeholder='Search students by name, email, or school...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='max-w-sm'
              />
            </div>
            <div className='flex gap-2'>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className='w-[120px]'>
                  <SelectValue placeholder='Status' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Status</SelectItem>
                  <SelectItem value='active'>Active</SelectItem>
                  <SelectItem value='inactive'>Inactive</SelectItem>
                  <SelectItem value='pending'>Pending</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={consultantFilter}
                onValueChange={setConsultantFilter}
              >
                <SelectTrigger className='w-[160px]'>
                  <SelectValue placeholder='Consultant' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Consultants</SelectItem>
                  <SelectItem value='unassigned'>Unassigned</SelectItem>
                  {uniqueConsultants.map((consultant) => (
                    <SelectItem
                      key={consultant?.id}
                      value={consultant?.id || ''}
                    >
                      {consultant?.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={graduationYearFilter}
                onValueChange={setGraduationYearFilter}
              >
                <SelectTrigger className='w-[120px]'>
                  <SelectValue placeholder='Grad Year' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Years</SelectItem>
                  {uniqueGradYears.map((year) => (
                    <SelectItem key={year} value={year || ''}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Students List */}
      <div className='space-y-4'>
        {filteredStudents.length === 0 ? (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-12'>
              <Icons.graduationCap className='text-muted-foreground mb-4 h-12 w-12' />
              <h3 className='mb-2 text-lg font-semibold'>No students found</h3>
              <p className='text-muted-foreground mb-4 text-center'>
                {searchTerm ||
                statusFilter !== 'all' ||
                consultantFilter !== 'all' ||
                graduationYearFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'No students registered yet'}
              </p>
              <Button>
                <Icons.userPlus className='mr-2 h-4 w-4' />
                Add Student
              </Button>
            </CardContent>
          </Card>
        ) : (
          filteredStudents.map((student) => (
            <Card
              key={student.id}
              className='transition-shadow hover:shadow-md'
            >
              <CardContent className='p-6'>
                <div className='flex items-start justify-between'>
                  <div className='flex-1'>
                    <div className='mb-3 flex items-center gap-3'>
                      <Avatar className='h-10 w-10'>
                        <AvatarImage src={student.avatar} />
                        <AvatarFallback>
                          {student.name
                            .split(' ')
                            .map((n) => n[0])
                            .join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className='font-semibold'>{student.name}</h3>
                        <p className='text-muted-foreground text-sm'>
                          {student.email}
                        </p>
                      </div>
                    </div>

                    <div className='mb-3 flex items-center gap-2'>
                      <Badge className={statusColors[student.status]}>
                        {student.status}
                      </Badge>
                      {student.profile.graduationYear && (
                        <Badge variant='outline'>
                          Class of {student.profile.graduationYear}
                        </Badge>
                      )}
                      {student.consultant && (
                        <Badge variant='outline'>
                          <Icons.userCheck className='mr-1 h-3 w-3' />
                          {student.consultant.name}
                        </Badge>
                      )}
                    </div>

                    <div className='mb-4 grid grid-cols-2 gap-4 md:grid-cols-4'>
                      <div>
                        <p className='text-sm font-medium'>School</p>
                        <p className='text-muted-foreground text-sm'>
                          {student.profile.schoolName || 'Not specified'}
                        </p>
                      </div>
                      <div>
                        <p className='text-sm font-medium'>Major</p>
                        <p className='text-muted-foreground text-sm'>
                          {student.profile.intendedMajor || 'Undecided'}
                        </p>
                      </div>
                      <div>
                        <p className='text-sm font-medium'>GPA</p>
                        <p className='text-muted-foreground text-sm'>
                          {student.profile.gpa || 'Not provided'}
                        </p>
                      </div>
                      <div>
                        <p className='text-sm font-medium'>Last Login</p>
                        <p className='text-muted-foreground text-sm'>
                          {formatLastLogin(student.lastLoginAt)}
                        </p>
                      </div>
                    </div>

                    <div className='mb-4 grid grid-cols-2 gap-4 md:grid-cols-4'>
                      <div className='text-center'>
                        <p className='text-2xl font-bold'>
                          {student.stats.documentsCount}
                        </p>
                        <p className='text-muted-foreground text-xs'>
                          Documents
                        </p>
                      </div>
                      <div className='text-center'>
                        <p className='text-2xl font-bold'>
                          {student.stats.completedDocuments}
                        </p>
                        <p className='text-muted-foreground text-xs'>
                          Completed
                        </p>
                      </div>
                      <div className='text-center'>
                        <p className='text-2xl font-bold'>
                          {student.stats.meetingsCount}
                        </p>
                        <p className='text-muted-foreground text-xs'>
                          Meetings
                        </p>
                      </div>
                      <div className='text-center'>
                        <p className='text-2xl font-bold'>
                          {student.stats.applicationProgress}%
                        </p>
                        <p className='text-muted-foreground text-xs'>
                          Progress
                        </p>
                      </div>
                    </div>

                    <div>
                      <div className='mb-2 flex items-center justify-between text-sm'>
                        <span>Application Progress</span>
                        <span>{student.stats.applicationProgress}%</span>
                      </div>
                      <Progress
                        value={student.stats.applicationProgress}
                        className='h-2'
                      />
                    </div>
                  </div>

                  <div className='ml-4 flex gap-2'>
                    <Button variant='outline' size='sm'>
                      <Icons.eye className='mr-2 h-4 w-4' />
                      View Profile
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant='outline' size='sm'>
                          <Icons.moreHorizontal className='h-4 w-4' />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end'>
                        <DropdownMenuItem>
                          <Icons.edit className='mr-2 h-4 w-4' />
                          Edit Student
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleAssignConsultant(student)}>
                          <Icons.userCheck className='mr-2 h-4 w-4' />
                          Assign Consultant
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Icons.messageSquare className='mr-2 h-4 w-4' />
                          Send Message
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Icons.userX className='mr-2 h-4 w-4' />
                          Deactivate
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Assignment Dialog */}
      <AssignConsultantDialog
        student={selectedStudentForAssignment}
        isOpen={isAssignDialogOpen}
        onClose={() => setIsAssignDialogOpen(false)}
        onAssignmentComplete={handleAssignmentComplete}
      />
    </div>
  );
}
