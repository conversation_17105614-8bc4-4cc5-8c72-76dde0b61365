'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Icons } from '@/components/icons';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useCurrentUser } from '@/hooks/use-current-user';

export default function ProfilePage() {
  const { user, loading } = useCurrentUser();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    bio: '',
    graduationYear: '',
    gpa: '',
    schoolName: '',
    intendedMajor: '',
    careerGoals: ''
  });

  if (loading) {
    return (
      <div className='flex h-96 items-center justify-center'>
        <Icons.spinner className='h-8 w-8 animate-spin' />
      </div>
    );
  }

  const handleSave = () => {
    // TODO: Implement save functionality
    setIsEditing(false);
  };

  const handleCancel = () => {
    // Reset form data
    setIsEditing(false);
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      {/* Header */}
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Profile</h2>
          <p className='text-muted-foreground'>
            Manage your personal information and preferences
          </p>
        </div>
        <div className='flex gap-2'>
          {isEditing ? (
            <>
              <Button variant='outline' onClick={handleCancel}>
                Cancel
              </Button>
              <Button onClick={handleSave}>
                <Icons.save className='mr-2 h-4 w-4' />
                Save Changes
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)}>
              <Icons.edit className='mr-2 h-4 w-4' />
              Edit Profile
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue='personal' className='space-y-6'>
        <TabsList>
          <TabsTrigger value='personal'>Personal Information</TabsTrigger>
          <TabsTrigger value='academic'>Academic Information</TabsTrigger>
          <TabsTrigger value='preferences'>Preferences</TabsTrigger>
          <TabsTrigger value='security'>Security</TabsTrigger>
        </TabsList>

        <TabsContent value='personal' className='space-y-6'>
          {/* Profile Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Profile Overview</CardTitle>
              <CardDescription>
                Your basic profile information and avatar
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='flex items-center space-x-4'>
                <Avatar className='h-20 w-20'>
                  <AvatarImage src={user?.profile_data?.avatar} />
                  <AvatarFallback className='text-lg'>
                    {user?.profile_data?.name?.charAt(0) ||
                      user?.email?.charAt(0) ||
                      'U'}
                  </AvatarFallback>
                </Avatar>
                <div className='space-y-2'>
                  <h3 className='text-lg font-semibold'>
                    {user?.profile_data?.name || 'User'}
                  </h3>
                  <p className='text-muted-foreground'>{user?.email}</p>
                  <Badge variant='secondary'>{user?.role}</Badge>
                </div>
                <div className='ml-auto'>
                  <Button variant='outline' size='sm'>
                    <Icons.camera className='mr-2 h-4 w-4' />
                    Change Photo
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Personal Details */}
          <Card>
            <CardHeader>
              <CardTitle>Personal Details</CardTitle>
              <CardDescription>
                Your personal information and contact details
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='firstName'>First Name</Label>
                  <Input
                    id='firstName'
                    value={formData.firstName}
                    onChange={(e) =>
                      setFormData({ ...formData, firstName: e.target.value })
                    }
                    disabled={!isEditing}
                    placeholder='Enter your first name'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='lastName'>Last Name</Label>
                  <Input
                    id='lastName'
                    value={formData.lastName}
                    onChange={(e) =>
                      setFormData({ ...formData, lastName: e.target.value })
                    }
                    disabled={!isEditing}
                    placeholder='Enter your last name'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='email'>Email</Label>
                  <Input
                    id='email'
                    type='email'
                    value={user?.email || ''}
                    disabled
                    className='bg-muted'
                  />
                  <p className='text-muted-foreground text-xs'>
                    Email cannot be changed here. Contact support if needed.
                  </p>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='phone'>Phone Number</Label>
                  <Input
                    id='phone'
                    value={formData.phone}
                    onChange={(e) =>
                      setFormData({ ...formData, phone: e.target.value })
                    }
                    disabled={!isEditing}
                    placeholder='Enter your phone number'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='dateOfBirth'>Date of Birth</Label>
                  <Input
                    id='dateOfBirth'
                    type='date'
                    value={formData.dateOfBirth}
                    onChange={(e) =>
                      setFormData({ ...formData, dateOfBirth: e.target.value })
                    }
                    disabled={!isEditing}
                  />
                </div>
              </div>

              <Separator />

              <div className='space-y-4'>
                <h4 className='text-sm font-medium'>Address Information</h4>
                <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                  <div className='space-y-2 md:col-span-2'>
                    <Label htmlFor='address'>Street Address</Label>
                    <Input
                      id='address'
                      value={formData.address}
                      onChange={(e) =>
                        setFormData({ ...formData, address: e.target.value })
                      }
                      disabled={!isEditing}
                      placeholder='Enter your street address'
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='city'>City</Label>
                    <Input
                      id='city'
                      value={formData.city}
                      onChange={(e) =>
                        setFormData({ ...formData, city: e.target.value })
                      }
                      disabled={!isEditing}
                      placeholder='Enter your city'
                    />
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='state'>State</Label>
                    <Select
                      value={formData.state}
                      onValueChange={(value) =>
                        setFormData({ ...formData, state: value })
                      }
                      disabled={!isEditing}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select state' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='CA'>California</SelectItem>
                        <SelectItem value='NY'>New York</SelectItem>
                        <SelectItem value='TX'>Texas</SelectItem>
                        <SelectItem value='FL'>Florida</SelectItem>
                        {/* Add more states as needed */}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className='space-y-2'>
                    <Label htmlFor='zipCode'>ZIP Code</Label>
                    <Input
                      id='zipCode'
                      value={formData.zipCode}
                      onChange={(e) =>
                        setFormData({ ...formData, zipCode: e.target.value })
                      }
                      disabled={!isEditing}
                      placeholder='Enter ZIP code'
                    />
                  </div>
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='bio'>Bio</Label>
                <Textarea
                  id='bio'
                  value={formData.bio}
                  onChange={(e) =>
                    setFormData({ ...formData, bio: e.target.value })
                  }
                  disabled={!isEditing}
                  placeholder='Tell us about yourself...'
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='academic' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Academic Information</CardTitle>
              <CardDescription>
                Your educational background and academic goals
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='schoolName'>Current School</Label>
                  <Input
                    id='schoolName'
                    value={formData.schoolName}
                    onChange={(e) =>
                      setFormData({ ...formData, schoolName: e.target.value })
                    }
                    disabled={!isEditing}
                    placeholder='Enter your school name'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='graduationYear'>Graduation Year</Label>
                  <Select
                    value={formData.graduationYear}
                    onValueChange={(value) =>
                      setFormData({ ...formData, graduationYear: value })
                    }
                    disabled={!isEditing}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select year' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='2025'>2025</SelectItem>
                      <SelectItem value='2026'>2026</SelectItem>
                      <SelectItem value='2027'>2027</SelectItem>
                      <SelectItem value='2028'>2028</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='gpa'>Current GPA</Label>
                  <Input
                    id='gpa'
                    value={formData.gpa}
                    onChange={(e) =>
                      setFormData({ ...formData, gpa: e.target.value })
                    }
                    disabled={!isEditing}
                    placeholder='e.g., 3.85'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='intendedMajor'>Intended Major</Label>
                  <Input
                    id='intendedMajor'
                    value={formData.intendedMajor}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        intendedMajor: e.target.value
                      })
                    }
                    disabled={!isEditing}
                    placeholder='Enter your intended major'
                  />
                </div>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='careerGoals'>Career Goals</Label>
                <Textarea
                  id='careerGoals'
                  value={formData.careerGoals}
                  onChange={(e) =>
                    setFormData({ ...formData, careerGoals: e.target.value })
                  }
                  disabled={!isEditing}
                  placeholder='Describe your career aspirations...'
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='preferences' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Preferences</CardTitle>
              <CardDescription>
                Customize your experience and notification settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>
                Preferences settings coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='security' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Manage your account security and authentication
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>
                Security settings are managed through your authentication
                provider.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
