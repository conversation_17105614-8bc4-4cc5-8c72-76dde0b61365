'use client';

import { useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Icons } from '@/components/icons';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useApiGet } from '@/lib/api-client';

export default function AnalyticsPage() {
  const { user, isAdmin } = useCurrentUser();

  // Fetch real analytics data from API
  const {
    data: analyticsData,
    loading,
    error,
    fetch: fetchAnalytics
  } = useApiGet<any>('/api/analytics');

  useEffect(() => {
    if (isAdmin) {
      fetchAnalytics();
    }
  }, [isAdmin, fetchAnalytics]);

  // Use real data with fallbacks
  const overview = analyticsData?.overview || {
    totalUsers: 0,
    totalStudents: 0,
    totalConsultants: 0,
    totalAdmins: 0,
    activeApplications: 0,
    completedApplications: 0,
    documentsCreated: 0,
    avgApplicationProgress: 0
  };

  const userGrowth = analyticsData?.userGrowth || [];
  const applicationStats = analyticsData?.applicationStats || {
    byStatus: {},
    bySchoolType: {}
  };

  const consultantPerformance = analyticsData?.consultantPerformance || [];
  const documentStats = analyticsData?.documentStats || {
    totalDocuments: 0,
    byType: {},
    byStatus: {},
    collaborationMetrics: {
      avgCommentsPerDocument: 0,
      avgRevisionsPerDocument: 0,
      documentsWithGoogleDocs: 0
    }
  };
  const systemHealth = analyticsData?.systemHealth || {
    uptime: '99.9%',
    responseTime: '245ms',
    errorRate: '0.1%',
    activeUsers: 0
  };

  // Show different content based on user role
  if (!isAdmin) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <Icons.barChart className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
            <h3 className='text-lg font-semibold'>Access Restricted</h3>
            <p className='text-muted-foreground mt-2 text-sm'>
              This page is only available to administrators.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between space-y-2'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>
            System Analytics
          </h2>
          <p className='text-muted-foreground'>
            Comprehensive overview of system performance and user activity
          </p>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='flex items-center gap-2'>
              <Icons.spinner className='h-4 w-4 animate-spin' />
              <span>Loading analytics...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='text-center'>
              <Icons.alertCircle className='text-destructive mx-auto mb-4 h-12 w-12' />
              <h3 className='mb-2 text-lg font-semibold'>Error Loading Analytics</h3>
              <p className='text-muted-foreground mb-4'>
                {error || 'Failed to load analytics data'}
              </p>
              <button
                onClick={fetchAnalytics}
                className='inline-flex items-center gap-2 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90'
              >
                <Icons.refresh className='h-4 w-4' />
                Try Again
              </button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overview Stats */}
      {!loading && !error && (
        <>
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Users</CardTitle>
            <Icons.users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {overview.totalUsers}
            </div>
            <p className='text-muted-foreground text-xs'>
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Active Applications
            </CardTitle>
            <Icons.fileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {overview.activeApplications}
            </div>
            <p className='text-muted-foreground text-xs'>
              {overview.completedApplications} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Documents Created
            </CardTitle>
            <Icons.folder className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {overview.documentsCreated.toLocaleString()}
            </div>
            <p className='text-muted-foreground text-xs'>+8% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Avg. Progress</CardTitle>
            <Icons.target className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {overview.avgApplicationProgress}%
            </div>
            <Progress
              value={overview.avgApplicationProgress}
              className='mt-2'
            />
          </CardContent>
        </Card>
      </div>

      {/* Application Statistics */}
      <div className='grid gap-4 md:grid-cols-2'>
        <Card>
          <CardHeader>
            <CardTitle>Application Status Distribution</CardTitle>
            <CardDescription>
              Current status of all applications
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            {Object.entries(applicationStats.byStatus).map(
              ([status, count]) => {
                const total = (Object.values(
                  applicationStats.byStatus
                ) as number[]).reduce((a: number, b: number) => a + b, 0);
                const percentage = Math.round(((count as number) / total) * 100);

                return (
                  <div key={status} className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm font-medium'>{status}</span>
                      <div className='flex items-center space-x-2'>
                        <span className='text-muted-foreground text-sm'>
                          {count as number}
                        </span>
                        <Badge variant='outline'>{percentage}%</Badge>
                      </div>
                    </div>
                    <Progress value={percentage} className='h-2' />
                  </div>
                );
              }
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>School Type Distribution</CardTitle>
            <CardDescription>Applications by school category</CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            {Object.entries(applicationStats.bySchoolType).map(
              ([type, count]) => {
                const total = (Object.values(
                  applicationStats.bySchoolType
                ) as number[]).reduce((a: number, b: number) => a + b, 0);
                const percentage = Math.round(((count as number) / total) * 100);

                return (
                  <div key={type} className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm font-medium'>{type}</span>
                      <div className='flex items-center space-x-2'>
                        <span className='text-muted-foreground text-sm'>
                          {count as number}
                        </span>
                        <Badge variant='outline'>{percentage}%</Badge>
                      </div>
                    </div>
                    <Progress value={percentage} className='h-2' />
                  </div>
                );
              }
            )}
          </CardContent>
        </Card>
      </div>

      {/* Consultant Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Consultant Performance</CardTitle>
          <CardDescription>
            Key metrics for consultant effectiveness
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {consultantPerformance.map((consultant: any, index: number) => (
              <div
                key={index}
                className='flex items-center justify-between rounded-lg border p-4'
              >
                <div className='space-y-1'>
                  <p className='font-medium'>{consultant.name}</p>
                  <p className='text-muted-foreground text-sm'>
                    {consultant.students} students •{' '}
                    {consultant.documentsReviewed} documents reviewed
                  </p>
                </div>
                <div className='flex items-center space-x-6 text-sm'>
                  <div className='text-center'>
                    <p className='font-medium'>{consultant.avgProgress}%</p>
                    <p className='text-muted-foreground'>Avg. Progress</p>
                  </div>
                  <div className='text-center'>
                    <p className='font-medium'>{consultant.responseTime}</p>
                    <p className='text-muted-foreground'>Response Time</p>
                  </div>
                  <Badge
                    variant={
                      consultant.avgProgress >= 70 ? 'default' : 'secondary'
                    }
                  >
                    {consultant.avgProgress >= 70 ? 'Excellent' : 'Good'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* User Growth and System Health */}
      <div className='grid gap-4 md:grid-cols-2'>
        <Card>
          <CardHeader>
            <CardTitle>User Growth Trend</CardTitle>
            <CardDescription>Monthly user registration trends</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {userGrowth.map((month: any, index: number) => (
                <div key={index} className='flex items-center justify-between'>
                  <span className='text-sm font-medium'>{month.month}</span>
                  <div className='flex items-center space-x-4 text-sm'>
                    <div className='flex items-center space-x-2'>
                      <Icons.graduationCap className='h-4 w-4 text-blue-500' />
                      <span>{month.students} students</span>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <Icons.userCheck className='h-4 w-4 text-green-500' />
                      <span>{month.consultants} consultants</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Health</CardTitle>
            <CardDescription>
              Current system performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <span className='text-sm font-medium'>System Uptime</span>
                <div className='flex items-center space-x-2'>
                  <span className='text-sm'>
                    {systemHealth.uptime}
                  </span>
                  <Badge variant='default'>Excellent</Badge>
                </div>
              </div>
              <Progress
                value={parseFloat(systemHealth.uptime.replace('%', ''))}
                className='h-2'
              />
            </div>

            <div className='grid grid-cols-2 gap-4 text-sm'>
              <div className='space-y-1'>
                <p className='font-medium'>Response Time</p>
                <p className='text-2xl font-bold'>
                  {systemHealth.responseTime}
                </p>
              </div>
              <div className='space-y-1'>
                <p className='font-medium'>Error Rate</p>
                <p className='text-2xl font-bold'>
                  {systemHealth.errorRate}
                </p>
              </div>
            </div>

            <div className='border-t pt-4'>
              <div className='flex items-center justify-between'>
                <span className='text-sm font-medium'>Active Users (24h)</span>
                <span className='text-lg font-bold'>
                  {systemHealth.activeUsers}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Key Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Key Insights</CardTitle>
          <CardDescription>
            Important trends and recommendations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <div className='flex items-start space-x-3 rounded-lg border border-blue-200 bg-blue-50 p-3'>
              <Icons.barChart className='mt-0.5 h-5 w-5 text-blue-600' />
              <div>
                <p className='text-sm font-medium text-blue-900'>
                  Strong User Growth
                </p>
                <p className='text-sm text-blue-700'>
                  User registrations increased by 12% this month, with
                  particularly strong growth in student signups.
                </p>
              </div>
            </div>

            <div className='flex items-start space-x-3 rounded-lg border border-green-200 bg-green-50 p-3'>
              <Icons.checkCircle className='mt-0.5 h-5 w-5 text-green-600' />
              <div>
                <p className='text-sm font-medium text-green-900'>
                  High Application Completion Rate
                </p>
                <p className='text-sm text-green-700'>
                  67% average application progress indicates strong student
                  engagement and consultant effectiveness.
                </p>
              </div>
            </div>

            <div className='flex items-start space-x-3 rounded-lg border border-yellow-200 bg-yellow-50 p-3'>
              <Icons.warning className='mt-0.5 h-5 w-5 text-yellow-600' />
              <div>
                <p className='text-sm font-medium text-yellow-900'>
                  Consultant Workload
                </p>
                <p className='text-sm text-yellow-700'>
                  Some consultants are managing 12+ students. Consider
                  redistributing workload or hiring additional consultants.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
        </>
      )}
    </div>
  );
}
