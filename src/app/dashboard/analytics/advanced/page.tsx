'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Icons } from '@/components/icons';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCurrentUser } from '@/hooks/use-current-user';

export default function AdvancedAnalyticsPage() {
  const { user: currentUser, loading: userLoading, isAdmin } = useCurrentUser();
  const [timeRange, setTimeRange] = useState('30d');

  // Show loading state while checking user role
  if (userLoading) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='flex items-center gap-2'>
              <Icons.spinner className='h-4 w-4 animate-spin' />
              <span>Loading...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show access denied for non-admin users
  if (!isAdmin) {
    return (
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <Icons.barChart className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
            <h3 className='text-lg font-semibold'>Access Restricted</h3>
            <p className='text-muted-foreground mt-2 text-sm'>
              This page is only available to administrators.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Mock analytics data
  const userEngagementData = {
    totalSessions: 12847,
    averageSessionDuration: '8m 32s',
    bounceRate: '23.4%',
    pageViews: 45623,
    uniqueUsers: 3421,
    returningUsers: 2156
  };

  const performanceMetrics = {
    averageLoadTime: '1.2s',
    serverUptime: '99.9%',
    errorRate: '0.1%',
    apiResponseTime: '245ms',
    databaseQueryTime: '89ms',
    cacheHitRate: '94.2%'
  };

  const conversionMetrics = {
    documentCreationRate: '78.5%',
    profileCompletionRate: '92.1%',
    consultantEngagementRate: '65.3%',
    applicationSubmissionRate: '43.7%'
  };

  const topPages = [
    { page: '/dashboard', views: 8934, uniqueViews: 3421, avgTime: '4m 12s' },
    { page: '/dashboard/essays/personal-statement', views: 6782, uniqueViews: 2876, avgTime: '12m 45s' },
    { page: '/dashboard/documents', views: 5643, uniqueViews: 2234, avgTime: '6m 23s' },
    { page: '/dashboard/schools', views: 4521, uniqueViews: 1987, avgTime: '8m 56s' },
    { page: '/dashboard/academics/test-scores', views: 3876, uniqueViews: 1654, avgTime: '5m 34s' }
  ];

  const userBehaviorInsights = [
    {
      insight: 'Peak Usage Hours',
      description: 'Most users are active between 7-9 PM EST',
      impact: 'high',
      recommendation: 'Schedule maintenance outside peak hours'
    },
    {
      insight: 'Document Creation Patterns',
      description: 'Students create 3.2 documents on average before first consultant meeting',
      impact: 'medium',
      recommendation: 'Provide guided document creation workflow'
    },
    {
      insight: 'Mobile Usage Growth',
      description: 'Mobile usage increased by 34% in the last month',
      impact: 'high',
      recommendation: 'Prioritize mobile experience improvements'
    },
    {
      insight: 'Feature Adoption',
      description: 'Google Docs integration has 89% adoption rate',
      impact: 'low',
      recommendation: 'Promote remaining features to increase adoption'
    }
  ];

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Advanced Analytics</h2>
          <p className='text-muted-foreground'>
            Detailed insights and performance metrics
          </p>
        </div>
        <div className='flex gap-2'>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className='w-32'>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='7d'>Last 7 days</SelectItem>
              <SelectItem value='30d'>Last 30 days</SelectItem>
              <SelectItem value='90d'>Last 90 days</SelectItem>
              <SelectItem value='1y'>Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant='outline'>
            <Icons.download className='mr-2 h-4 w-4' />
            Export Report
          </Button>
          <Button>
            <Icons.arrowRight className='mr-2 h-4 w-4' />
            Refresh Data
          </Button>
        </div>
      </div>

      <Tabs defaultValue='engagement' className='space-y-4'>
        <TabsList className='grid w-full grid-cols-4'>
          <TabsTrigger value='engagement'>User Engagement</TabsTrigger>
          <TabsTrigger value='performance'>Performance</TabsTrigger>
          <TabsTrigger value='conversion'>Conversion</TabsTrigger>
          <TabsTrigger value='insights'>Insights</TabsTrigger>
        </TabsList>

        <TabsContent value='engagement' className='space-y-4'>
          {/* User Engagement Metrics */}
          <div className='grid gap-4 md:grid-cols-3 lg:grid-cols-6'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Total Sessions</CardTitle>
                <Icons.users className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{userEngagementData.totalSessions.toLocaleString()}</div>
                <p className='text-xs text-muted-foreground'>
                  +12.5% from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Avg Session Duration</CardTitle>
                <Icons.clock className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{userEngagementData.averageSessionDuration}</div>
                <p className='text-xs text-muted-foreground'>
                  +8.3% from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Bounce Rate</CardTitle>
                <Icons.download className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{userEngagementData.bounceRate}</div>
                <p className='text-xs text-muted-foreground'>
                  -2.1% from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Page Views</CardTitle>
                <Icons.eye className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{userEngagementData.pageViews.toLocaleString()}</div>
                <p className='text-xs text-muted-foreground'>
                  +15.7% from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Unique Users</CardTitle>
                <Icons.userCheck className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{userEngagementData.uniqueUsers.toLocaleString()}</div>
                <p className='text-xs text-muted-foreground'>
                  +9.4% from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Returning Users</CardTitle>
                <Icons.users className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{userEngagementData.returningUsers.toLocaleString()}</div>
                <p className='text-xs text-muted-foreground'>
                  +6.8% from last period
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Top Pages */}
          <Card>
            <CardHeader>
              <CardTitle>Top Pages</CardTitle>
              <CardDescription>
                Most visited pages and their performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {topPages.map((page, index) => (
                  <div key={index} className='flex items-center justify-between rounded-lg border p-4'>
                    <div className='space-y-1'>
                      <h4 className='font-medium'>{page.page}</h4>
                      <div className='flex items-center gap-4 text-sm text-muted-foreground'>
                        <span>{page.views.toLocaleString()} views</span>
                        <span>{page.uniqueViews.toLocaleString()} unique</span>
                        <span>Avg time: {page.avgTime}</span>
                      </div>
                    </div>
                    <Badge variant='outline'>#{index + 1}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='performance' className='space-y-4'>
          {/* Performance Metrics */}
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Average Load Time</CardTitle>
                <Icons.clock className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{performanceMetrics.averageLoadTime}</div>
                <p className='text-xs text-muted-foreground'>
                  -0.3s from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Server Uptime</CardTitle>
                <Icons.database className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-green-600'>{performanceMetrics.serverUptime}</div>
                <p className='text-xs text-muted-foreground'>
                  Excellent reliability
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Error Rate</CardTitle>
                <Icons.alertTriangle className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-green-600'>{performanceMetrics.errorRate}</div>
                <p className='text-xs text-muted-foreground'>
                  Very low error rate
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>API Response Time</CardTitle>
                <Icons.barChart className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{performanceMetrics.apiResponseTime}</div>
                <p className='text-xs text-muted-foreground'>
                  -15ms from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Database Query Time</CardTitle>
                <Icons.database className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{performanceMetrics.databaseQueryTime}</div>
                <p className='text-xs text-muted-foreground'>
                  -8ms from last period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Cache Hit Rate</CardTitle>
                <Icons.target className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-green-600'>{performanceMetrics.cacheHitRate}</div>
                <p className='text-xs text-muted-foreground'>
                  +1.2% from last period
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='conversion' className='space-y-4'>
          {/* Conversion Metrics */}
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Document Creation</CardTitle>
                <Icons.fileText className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{conversionMetrics.documentCreationRate}</div>
                <p className='text-xs text-muted-foreground'>
                  Users who create documents
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Profile Completion</CardTitle>
                <Icons.user className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{conversionMetrics.profileCompletionRate}</div>
                <p className='text-xs text-muted-foreground'>
                  Complete profile setup
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Consultant Engagement</CardTitle>
                <Icons.userCheck className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{conversionMetrics.consultantEngagementRate}</div>
                <p className='text-xs text-muted-foreground'>
                  Active with consultants
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Application Submission</CardTitle>
                <Icons.send className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{conversionMetrics.applicationSubmissionRate}</div>
                <p className='text-xs text-muted-foreground'>
                  Submit applications
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='insights' className='space-y-4'>
          {/* User Behavior Insights */}
          <Card>
            <CardHeader>
              <CardTitle>User Behavior Insights</CardTitle>
              <CardDescription>
                AI-powered insights and recommendations based on user behavior patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {userBehaviorInsights.map((insight, index) => (
                  <div key={index} className='rounded-lg border p-4'>
                    <div className='flex items-start justify-between'>
                      <div className='space-y-2'>
                        <div className='flex items-center gap-2'>
                          <h4 className='font-medium'>{insight.insight}</h4>
                          <Badge className={getImpactColor(insight.impact)}>
                            {insight.impact} impact
                          </Badge>
                        </div>
                        <p className='text-sm text-muted-foreground'>
                          {insight.description}
                        </p>
                        <div className='flex items-center gap-2 text-sm'>
                          <Icons.lightbulb className='h-4 w-4 text-yellow-600' />
                          <span className='font-medium'>Recommendation:</span>
                          <span>{insight.recommendation}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
