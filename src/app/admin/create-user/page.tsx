'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, User, RefreshCw } from 'lucide-react';

export default function CreateUserPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const createUser = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/test-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to create user');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const checkUserStatus = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/test-auth');
      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to check user status');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            User Management Utility
          </CardTitle>
          <CardDescription>
            This utility helps resolve authentication issues by creating users in the database
            when the Clerk webhook fails to trigger automatically.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button 
              onClick={checkUserStatus} 
              disabled={loading}
              variant="outline"
              className="flex items-center gap-2"
            >
              {loading ? <RefreshCw className="h-4 w-4 animate-spin" /> : <CheckCircle className="h-4 w-4" />}
              Check User Status
            </Button>
            <Button 
              onClick={createUser} 
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading ? <RefreshCw className="h-4 w-4 animate-spin" /> : <User className="h-4 w-4" />}
              Create User in Database
            </Button>
          </div>

          {error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {result && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p><strong>Status:</strong> {result.message}</p>
                  {result.data?.user && (
                    <div className="text-sm">
                      <p><strong>User ID:</strong> {result.data.user.id}</p>
                      <p><strong>Email:</strong> {result.data.user.email}</p>
                      <p><strong>Role:</strong> {result.data.user.role}</p>
                      <p><strong>Clerk ID:</strong> {result.data.user.clerk_id}</p>
                    </div>
                  )}
                  {result.data?.action === 'user_created' && (
                    <p className="text-green-600 font-medium">✅ User successfully created! You can now access the dashboard.</p>
                  )}
                  {result.data?.action === 'no_action_needed' && (
                    <p className="text-blue-600 font-medium">ℹ️ User already exists in database.</p>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="text-sm text-muted-foreground space-y-2">
            <p><strong>When to use this:</strong></p>
            <ul className="list-disc list-inside space-y-1">
              <li>You see &quot;401 Unauthorized&quot; errors when accessing the dashboard</li>
              <li>A new user signed up but can&apos;t access their account</li>
              <li>The Clerk webhook failed to create the user in Supabase</li>
            </ul>

            <p className="mt-4"><strong>How it works:</strong></p>
            <ul className="list-disc list-inside space-y-1">
              <li>Checks if the current authenticated user exists in the database</li>
              <li>If not, creates the user with default &quot;student&quot; role</li>
              <li>Creates the associated student profile</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
