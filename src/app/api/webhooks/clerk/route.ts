import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { Webhook } from 'svix';
import { upsertUser } from '@/lib/supabase';
import { createSuccessResponse, createErrorResponse } from '@/lib/api-utils';

const CLERK_WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET;

if (!CLERK_WEBHOOK_SECRET) {
  throw new Error('Missing CLERK_WEBHOOK_SECRET environment variable');
}

export async function POST(request: NextRequest) {
  try {
    // Get headers
    const headerPayload = await headers();
    const svix_id = headerPayload.get('svix-id');
    const svix_timestamp = headerPayload.get('svix-timestamp');
    const svix_signature = headerPayload.get('svix-signature');

    // If there are no headers, error out
    if (!svix_id || !svix_timestamp || !svix_signature) {
      return createErrorResponse('Missing svix headers', 400);
    }

    // Get the body
    const payload = await request.text();

    // Create a new Svix instance with your secret
    const wh = new Webhook(CLERK_WEBHOOK_SECRET!);

    let evt: any;

    // Verify the payload with the headers
    try {
      evt = wh.verify(payload, {
        'svix-id': svix_id,
        'svix-timestamp': svix_timestamp,
        'svix-signature': svix_signature
      });
    } catch (err) {
      console.error('Error verifying webhook:', err);
      return createErrorResponse('Invalid webhook signature', 400);
    }

    // Handle the webhook
    const eventType = evt.type;
    const { id, email_addresses, first_name, last_name } = evt.data;

    console.log(`Webhook received: ${eventType} for user ${id}`);

    switch (eventType) {
      case 'user.created':
      case 'user.updated':
        // Get primary email
        const primaryEmail = email_addresses?.find(
          (email: any) => email.id === evt.data.primary_email_address_id
        );

        if (!primaryEmail) {
          console.error('No primary email found for user:', id);
          return createErrorResponse('No primary email found', 400);
        }

        // Create or update user in Supabase
        // Default role is 'student' - admins will need to manually change roles
        const userData = {
          clerk_id: id,
          email: primaryEmail.email_address,
          role: 'student' as const,
          profile_data: {
            first_name,
            last_name,
            full_name: `${first_name || ''} ${last_name || ''}`.trim()
          }
        };

        const user = await upsertUser(userData);

        // If this is a new student, create their profile
        if (eventType === 'user.created' && user.role === 'student') {
          const { supabaseAdmin } = await import('@/lib/supabase');

          const { error: profileError } = await supabaseAdmin
            .from('student_profiles')
            .upsert(
              {
                user_id: user.id,
                academic_info: {},
                target_schools: [],
                application_status: {}
              },
              {
                onConflict: 'user_id'
              }
            );

          if (profileError) {
            console.error('Error creating student profile:', profileError);
            // Don't throw error to avoid webhook failure, but log it
          } else {
            console.log(
              'Student profile created successfully for user:',
              user.id
            );
          }
        }

        console.log(
          `User ${eventType === 'user.created' ? 'created' : 'updated'} in Supabase:`,
          user.id
        );
        break;

      case 'user.deleted':
        // Handle user deletion
        const { supabaseAdmin } = await import('@/lib/supabase');

        await supabaseAdmin.from('users').delete().eq('clerk_id', id);

        console.log(`User deleted from Supabase: ${id}`);
        break;

      default:
        console.log(`Unhandled webhook event: ${eventType}`);
    }

    return createSuccessResponse(
      { received: true },
      'Webhook processed successfully'
    );
  } catch (error) {
    console.error('Webhook error:', error);
    return createErrorResponse('Webhook processing failed', 500);
  }
}
