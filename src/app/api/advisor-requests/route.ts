import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  createStandardPaginatedResponse,
  handleAsyncOperation,
  parseRequestBody,
  parsePaginationParams,
  AuthorizationError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { User } from '@/types/application';

interface AdvisorRequest {
  id?: string;
  student_id: string;
  message?: string;
  preferred_specialties?: string[];
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  admin_notes?: string;
  assigned_consultant_id?: string;
  created_at?: string;
  updated_at?: string;
}

// GET /api/advisor-requests - List advisor requests (Admin only)
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // Only admins can view all advisor requests
      if (currentUser.role !== 'admin') {
        throw new AuthorizationError('Only admins can view advisor requests');
      }

      const pagination = parsePaginationParams(request);
      const url = new URL(request.url);
      const status = url.searchParams.get('status');

      let query = supabaseAdmin
        .from('advisor_requests')
        .select(`
          *,
          student_profiles!inner(
            id,
            users!inner(id, email, profile_data)
          ),
          consultants(
            id,
            users!inner(id, email, profile_data)
          )
        `, { count: 'exact' })
        .range(pagination.offset, pagination.offset + pagination.limit - 1)
        .order('created_at', { ascending: false });

      if (status) {
        query = query.eq('status', status);
      }

      const { data: requests, error, count } = await query;

      if (error) {
        throw error;
      }

      return createStandardPaginatedResponse(
        requests || [],
        count || 0,
        pagination,
        'Advisor requests retrieved successfully',
        {
          user_role: currentUser.role,
          operation_type: 'list',
          resource_type: 'advisor_requests',
          applied_filters: { status }
        }
      );
    }, 'Failed to retrieve advisor requests');
  }
);

// POST /api/advisor-requests - Create new advisor request (Student only)
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // Only students can create advisor requests
      if (currentUser.role !== 'student') {
        throw new AuthorizationError('Only students can request advisors');
      }

      const body = await parseRequestBody<{
        message?: string;
        preferred_specialties?: string[];
      }>(request);

      // Get student profile ID for current user
      const { data: studentProfile } = await supabaseAdmin
        .from('student_profiles')
        .select('id')
        .eq('user_id', currentUser.id)
        .single();

      if (!studentProfile) {
        throw new Error('Student profile not found');
      }

      // Check if student already has an active advisor
      const { data: existingAssignment } = await supabaseAdmin
        .from('student_consultant_relations')
        .select('id')
        .eq('student_id', studentProfile.id)
        .eq('status', 'active')
        .single();

      if (existingAssignment) {
        throw new Error('You already have an assigned advisor');
      }

      // Check if student already has a pending request
      const { data: existingRequest } = await supabaseAdmin
        .from('advisor_requests')
        .select('id')
        .eq('student_id', studentProfile.id)
        .eq('status', 'pending')
        .single();

      if (existingRequest) {
        throw new Error('You already have a pending advisor request');
      }

      const requestData: Omit<AdvisorRequest, 'id' | 'created_at' | 'updated_at'> = {
        student_id: studentProfile.id,
        message: body.message,
        preferred_specialties: body.preferred_specialties || [],
        status: 'pending'
      };

      const { data: advisorRequest, error } = await supabaseAdmin
        .from('advisor_requests')
        .insert(requestData)
        .select(`
          *,
          student_profiles!inner(
            id,
            users!inner(id, email, profile_data)
          )
        `)
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        advisorRequest,
        'Advisor request submitted successfully',
        201,
        {
          user_role: currentUser.role,
          operation_type: 'create',
          resource_type: 'advisor_request',
          student_id: studentProfile.id
        }
      );
    }, 'Failed to submit advisor request');
  }
);
