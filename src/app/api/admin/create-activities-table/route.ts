import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

// POST /api/admin/create-activities-table - Create activities table
export async function POST(request: NextRequest) {
  try {
    console.log('Creating activities table...');

    // Simple approach: try to create the table directly
    const createTableSQL = `CREATE TABLE IF NOT EXISTS activities (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
        activity_type TEXT NOT NULL,
        organization_name TEXT NOT NULL,
        position_title TEXT,
        description TEXT,
        participation_grade_levels TEXT[] DEFAULT '{}',
        hours_per_week INTEGER NOT NULL DEFAULT 0,
        weeks_per_year INTEGER NOT NULL DEFAULT 0,
        leadership_role BOOLEAN DEFAULT FALSE,
        awards_recognition TEXT,
        file_uploads JSONB DEFAULT '{"certificates": [], "videos": [], "photos": []}'::jsonb,
        name TEXT,
        category TEXT,
        organization TEXT,
        position TEXT,
        years_active TEXT,
        achievements TEXT[],
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );`;

    // Execute the SQL using direct database connection
    const { error } = await supabaseAdmin.rpc('exec_sql', { sql: createTableSQL });

    if (error) {
      console.error('Error creating activities table:', error);

      // Try alternative approach using direct SQL execution
      try {
        // Use the supabase client's direct query method
        const result = await supabaseAdmin
          .from('activities')
          .select('id')
          .limit(1);

        if (result.error && result.error.code === '42P01') {
          // Table doesn't exist, let's create it using a different method
          console.log('Table does not exist, creating manually...');

          // Since exec_sql might not be available, let's try creating the table
          // by inserting the schema directly into the database
          const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
            method: 'POST',
            headers: {
              'apikey': serviceKey,
              'Authorization': `Bearer ${serviceKey}`,
              'Content-Type': 'application/json',
              'Prefer': 'return=minimal'
            },
            body: JSON.stringify({ sql: createTableSQL })
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
          }

          console.log('Table created via REST API');
        }
      } catch (altError) {
        console.error('Alternative creation method failed:', altError);
        return NextResponse.json(
          { success: false, error: `Failed to create table: ${error.message}` },
          { status: 500 }
        );
      }
    }

    console.log('Activities table created successfully!');
    return NextResponse.json(
      { success: true, message: 'Activities table created successfully' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Failed to create activities table:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create activities table' },
      { status: 500 }
    );
  }
}
