import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

// POST /api/admin/create-activities-table - Create activities table
export async function POST(request: NextRequest) {
  try {
    console.log('Creating activities table...');

    // Create the activities table with all necessary fields
    // Try direct SQL execution first
    const { error } = await supabaseAdmin.from('_temp').select('1').limit(1);

    // If that works, try creating the table using a different approach
    console.log('Testing Supabase connection...');

    // Let's try creating the table step by step
    const createTableResult = await supabaseAdmin.rpc('create_activities_table_if_not_exists');

    if (createTableResult.error) {
      console.log('RPC function not found, trying direct approach...');

      // Alternative: Use the REST API directly
      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
        method: 'POST',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY!,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sql: `
        -- Create activities table
        CREATE TABLE IF NOT EXISTS activities (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
          
          -- Common App structure fields
          activity_type TEXT NOT NULL,
          organization_name TEXT NOT NULL,
          position_title TEXT,
          description TEXT,
          participation_grade_levels TEXT[] DEFAULT '{}',
          
          -- Time commitment
          hours_per_week INTEGER NOT NULL DEFAULT 0,
          weeks_per_year INTEGER NOT NULL DEFAULT 0,
          
          -- Leadership and recognition
          leadership_role BOOLEAN DEFAULT FALSE,
          awards_recognition TEXT,
          
          -- File uploads
          file_uploads JSONB DEFAULT '{"certificates": [], "videos": [], "photos": []}'::jsonb,
          
          -- Legacy fields for backward compatibility
          name TEXT,
          category TEXT,
          organization TEXT,
          position TEXT,
          years_active TEXT,
          achievements TEXT[],
          
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create indexes
        CREATE INDEX IF NOT EXISTS idx_activities_student_id ON activities(student_id);
        CREATE INDEX IF NOT EXISTS idx_activities_activity_type ON activities(activity_type);
        CREATE INDEX IF NOT EXISTS idx_activities_leadership_role ON activities(leadership_role);

        -- Enable RLS
        ALTER TABLE activities ENABLE ROW LEVEL SECURITY;

        -- Create RLS policies
        DROP POLICY IF EXISTS "Students can view their own activities" ON activities;
        CREATE POLICY "Students can view their own activities" ON activities
          FOR SELECT USING (
            student_id IN (
              SELECT id FROM student_profiles 
              WHERE user_id = auth.uid()
            )
          );

        DROP POLICY IF EXISTS "Students can insert their own activities" ON activities;
        CREATE POLICY "Students can insert their own activities" ON activities
          FOR INSERT WITH CHECK (
            student_id IN (
              SELECT id FROM student_profiles 
              WHERE user_id = auth.uid()
            )
          );

        DROP POLICY IF EXISTS "Students can update their own activities" ON activities;
        CREATE POLICY "Students can update their own activities" ON activities
          FOR UPDATE USING (
            student_id IN (
              SELECT id FROM student_profiles 
              WHERE user_id = auth.uid()
            )
          );

        DROP POLICY IF EXISTS "Students can delete their own activities" ON activities;
        CREATE POLICY "Students can delete their own activities" ON activities
          FOR DELETE USING (
            student_id IN (
              SELECT id FROM student_profiles 
              WHERE user_id = auth.uid()
            )
          );

        DROP POLICY IF EXISTS "Consultants can view activities of their students" ON activities;
        CREATE POLICY "Consultants can view activities of their students" ON activities
          FOR SELECT USING (
            student_id IN (
              SELECT sp.id FROM student_profiles sp
              JOIN student_consultant_relations scr ON sp.id = scr.student_id
              JOIN consultants c ON scr.consultant_id = c.id
              WHERE c.user_id = auth.uid() AND scr.status = 'active'
            )
          );

        DROP POLICY IF EXISTS "Consultants can update activities of their students" ON activities;
        CREATE POLICY "Consultants can update activities of their students" ON activities
          FOR UPDATE USING (
            student_id IN (
              SELECT sp.id FROM student_profiles sp
              JOIN student_consultant_relations scr ON sp.id = scr.student_id
              JOIN consultants c ON scr.consultant_id = c.id
              WHERE c.user_id = auth.uid() AND scr.status = 'active'
            )
          );

        DROP POLICY IF EXISTS "Admins can manage all activities" ON activities;
        CREATE POLICY "Admins can manage all activities" ON activities
          FOR ALL USING (
            EXISTS (
              SELECT 1 FROM users 
              WHERE id = auth.uid() AND role = 'admin'
            )
          );
      `
    });

    if (error) {
      console.error('Error creating activities table:', error);
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      );
    }

    console.log('Activities table created successfully!');
    return NextResponse.json(
      { success: true, message: 'Activities table created successfully' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Failed to create activities table:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create activities table' },
      { status: 500 }
    );
  }
}
