import { NextRequest } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation
} from '@/lib/api-utils';
import { User } from '@/types/application';

// POST /api/admin/migrate - Run database migrations (admin only)
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // Only allow admins to run migrations
      if (currentUser.role !== 'admin') {
        throw new Error('Unauthorized: Admin access required');
      }

      // Create activities table
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS activities (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
          
          -- Common App structure fields
          activity_type TEXT NOT NULL,
          organization_name TEXT NOT NULL,
          position_title TEXT,
          description TEXT,
          participation_grade_levels TEXT[] DEFAULT '{}',
          
          -- Time commitment
          hours_per_week INTEGER NOT NULL DEFAULT 0,
          weeks_per_year INTEGER NOT NULL DEFAULT 0,
          
          -- Leadership and recognition
          leadership_role BOOLEAN DEFAULT FALSE,
          awards_recognition TEXT,
          
          -- File uploads
          file_uploads JSONB DEFAULT '{"certificates": [], "videos": [], "photos": []}'::jsonb,
          
          -- Legacy fields for backward compatibility
          name TEXT,
          category TEXT,
          organization TEXT,
          position TEXT,
          years_active TEXT,
          achievements TEXT[],
          
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      const { error: createError } = await supabaseAdmin.rpc('exec_sql', {
        sql: createTableSQL
      });

      if (createError) {
        console.error('Error creating activities table:', createError);
        throw new Error(`Failed to create activities table: ${createError.message}`);
      }

      // Create indexes
      const indexSQL = `
        CREATE INDEX IF NOT EXISTS idx_activities_student_id ON activities(student_id);
        CREATE INDEX IF NOT EXISTS idx_activities_activity_type ON activities(activity_type);
        CREATE INDEX IF NOT EXISTS idx_activities_leadership_role ON activities(leadership_role);
      `;

      const { error: indexError } = await supabaseAdmin.rpc('exec_sql', {
        sql: indexSQL
      });

      if (indexError) {
        console.warn('Warning creating indexes:', indexError);
      }

      // Enable RLS
      const rlsSQL = `ALTER TABLE activities ENABLE ROW LEVEL SECURITY;`;
      
      const { error: rlsError } = await supabaseAdmin.rpc('exec_sql', {
        sql: rlsSQL
      });

      if (rlsError) {
        console.warn('Warning enabling RLS:', rlsError);
      }

      return createStandardSuccessResponse(
        { table_created: true },
        'Activities table migration completed successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'migration',
          resource_type: 'activities_table'
        }
      );
    }, 'Failed to run migration');
  }
);
