import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  withStandardRole,
  createStandardSuccessResponse,
  createStandardPaginatedResponse,
  parsePaginationParams,
  handleAsyncOperation
} from '@/lib/api-utils';
import { supabase } from '@/lib/supabase';
import type { User } from '@/types/application';

// GET /api/consultants - List consultant profiles
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const pagination = parsePaginationParams(request);
      const url = new URL(request.url);
      const specialty = url.searchParams.get('specialty');

      let query = supabase
        .from('consultants')
        .select(
          `
        *,
        users!inner(id, email, profile_data, created_at)
      `,
          { count: 'exact' }
        )
        .range(pagination.offset, pagination.offset + pagination.limit - 1)
        .order('created_at', { ascending: false });

      // Role-based filtering
      if (currentUser.role === 'consultant') {
        // Consultants can only see their own profile
        query = query.eq('user_id', currentUser.id);
      }
      // Students and admins can see all consultants

      // Filter by specialty if provided
      if (specialty) {
        query = query.contains('specialties', [specialty]);
      }

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      return createStandardPaginatedResponse(
        data || [],
        count || 0,
        pagination,
        'Consultant profiles retrieved successfully',
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'consultants',
          applied_filters: {
            specialty: specialty || null,
            role_based_filter:
              currentUser.role === 'consultant'
                ? 'own_profile_only'
                : 'all_consultants'
          },
          total_results: count || 0
        }
      );
    }, 'Failed to retrieve consultant profiles');
  }
);
