import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  parseRequestBody,
  handleAsyncOperation,
  NotFoundError,
  AuthorizationError
} from '@/lib/api-utils';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import type { User, ConsultantAvailability } from '@/types/application';

interface RouteContext {
  params: { id: string };
}

// Helper function to check if user can access consultant profile
async function canAccessConsultant(
  currentUser: User,
  consultantId: string
): Promise<boolean> {
  if (currentUser.role === 'admin') {
    return true;
  }

  if (currentUser.role === 'consultant') {
    // Check if this is their own profile
    const { data: consultantProfile } = await supabase
      .from('consultants')
      .select('user_id')
      .eq('id', consultantId)
      .single();

    return consultantProfile?.user_id === currentUser.id;
  }

  // Students can view consultant profiles (read-only)
  return currentUser.role === 'student';
}

// GET /api/consultants/[id] - Get consultant profile
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      if (!(await canAccessConsultant(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this consultant profile'
        );
      }

      const { data: consultant, error } = await supabase
        .from('consultants')
        .select(
          `
        *,
        users!inner(id, email, profile_data, created_at)
      `
        )
        .eq('id', id)
        .single();

      if (error || !consultant) {
        throw new NotFoundError('Consultant profile not found');
      }

      // If student is requesting, also include assigned status
      if (currentUser.role === 'student') {
        const { data: studentProfile } = await supabase
          .from('student_profiles')
          .select('id')
          .eq('user_id', currentUser.id)
          .single();

        if (studentProfile) {
          const { data: relation } = await supabase
            .from('student_consultant_relations')
            .select('status, start_date')
            .eq('student_id', studentProfile.id)
            .eq('consultant_id', id)
            .single();

          (consultant as any).assignment_status =
            relation?.status || 'not_assigned';
          (consultant as any).assignment_start_date = relation?.start_date;
        }
      }

      return createStandardSuccessResponse(
        consultant,
        'Consultant profile retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'consultant_profile',
          consultant_id: id,
          includes_assignment_status: currentUser.role === 'student'
        }
      );
    }, 'Failed to retrieve consultant profile');
  }
);

// PUT /api/consultants/[id] - Update consultant profile
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      // Only the consultant themselves or admin can update
      if (currentUser.role !== 'admin') {
        const { data: consultantProfile } = await supabase
          .from('consultants')
          .select('user_id')
          .eq('id', id)
          .single();

        if (consultantProfile?.user_id !== currentUser.id) {
          throw new AuthorizationError(
            'You can only update your own consultant profile'
          );
        }
      }

      const body = await parseRequestBody<{
        specialties?: string[];
        availability?: ConsultantAvailability;
      }>(request);

      const { data: consultant, error } = await supabaseAdmin
        .from('consultants')
        .update({
          ...body,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      if (!consultant) {
        throw new NotFoundError('Consultant profile not found');
      }

      return createStandardSuccessResponse(
        consultant,
        'Consultant profile updated successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'update',
          resource_type: 'consultant_profile',
          consultant_id: id,
          updated_fields: Object.keys(body),
          admin_operation: currentUser.role === 'admin'
        }
      );
    }, 'Failed to update consultant profile');
  }
);
