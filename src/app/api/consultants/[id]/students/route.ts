import { NextRequest } from 'next/server';
import {
  withAuth,
  createSuccessResponse,
  parseRequestBody,
  NotFoundError,
  AuthorizationError,
  validateRequiredFields,
  parsePaginationParams,
  createPaginatedResponse
} from '@/lib/api-utils';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

interface RouteContext {
  params: { id: string };
}

// GET /api/consultants/[id]/students - Get assigned students
export const GET = withAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    const { id } = context.params;
    const pagination = parsePaginationParams(request);
    const url = new URL(request.url);
    const status = url.searchParams.get('status') || 'active';

    // Check access permissions
    if (currentUser.role === 'consultant') {
      const { data: consultantProfile } = await supabase
        .from('consultants')
        .select('user_id')
        .eq('id', id)
        .single();

      if (consultantProfile?.user_id !== currentUser.id) {
        throw new AuthorizationError(
          'You can only view your own assigned students'
        );
      }
    } else if (currentUser.role !== 'admin') {
      throw new AuthorizationError(
        'Only consultants and admins can view student assignments'
      );
    }

    const {
      data: relations,
      error: relationsError,
      count
    } = await supabase
      .from('student_consultant_relations')
      .select(
        `
      *,
      student_profiles!inner(
        *,
        users!inner(id, email, profile_data)
      )
    `,
        { count: 'exact' }
      )
      .eq('consultant_id', id)
      .eq('status', status)
      .range(pagination.offset, pagination.offset + pagination.limit - 1)
      .order('start_date', { ascending: false });

    if (relationsError) {
      throw relationsError;
    }

    const students =
      relations?.map((relation) => ({
        ...relation.student_profiles,
        assignment: {
          id: relation.id,
          start_date: relation.start_date,
          status: relation.status
        }
      })) || [];

    return createPaginatedResponse(
      students,
      count || 0,
      pagination,
      'Assigned students retrieved successfully'
    );
  }
);

// POST /api/consultants/[id]/students - Assign student to consultant
export const POST = withAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    const { id } = context.params;

    // Only admins can assign students to consultants
    if (currentUser.role !== 'admin') {
      throw new AuthorizationError(
        'Only admins can assign students to consultants'
      );
    }

    const body = await parseRequestBody<{
      student_id: string;
      start_date?: string;
    }>(request);

    validateRequiredFields(body, ['student_id']);

    // Check if consultant exists
    const { data: consultant, error: consultantError } = await supabase
      .from('consultants')
      .select('id')
      .eq('id', id)
      .single();

    if (consultantError || !consultant) {
      throw new NotFoundError('Consultant not found');
    }

    // Check if student exists
    const { data: student, error: studentError } = await supabase
      .from('student_profiles')
      .select('id')
      .eq('id', body.student_id)
      .single();

    if (studentError || !student) {
      throw new NotFoundError('Student not found');
    }

    // Check if assignment already exists
    const { data: existingRelation } = await supabase
      .from('student_consultant_relations')
      .select('id, status')
      .eq('student_id', body.student_id)
      .eq('consultant_id', id)
      .single();

    if (existingRelation) {
      if (existingRelation.status === 'active') {
        throw new Error('Student is already assigned to this consultant');
      } else {
        // Reactivate existing relation
        const { data: relation, error } = await supabaseAdmin
          .from('student_consultant_relations')
          .update({
            status: 'active',
            start_date:
              body.start_date || new Date().toISOString().split('T')[0],
            updated_at: new Date().toISOString()
          })
          .eq('id', existingRelation.id)
          .select()
          .single();

        if (error) {
          throw error;
        }

        return createSuccessResponse(
          relation,
          'Student assignment reactivated successfully'
        );
      }
    }

    // Create new assignment
    const { data: relation, error } = await supabaseAdmin
      .from('student_consultant_relations')
      .insert({
        student_id: body.student_id,
        consultant_id: id,
        start_date: body.start_date || new Date().toISOString().split('T')[0],
        status: 'active'
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return createSuccessResponse(
      relation,
      'Student assigned to consultant successfully',
      201
    );
  }
);
