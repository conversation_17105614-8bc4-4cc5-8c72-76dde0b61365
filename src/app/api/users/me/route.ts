import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  parseRequestBody,
  handleAsyncOperation
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

// GET /api/users/me - Get current user profile
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // Get user with related profile data based on role
      let profileData = null;

      if (currentUser.role === 'student') {
        const { data: studentProfile } = await supabaseAdmin
          .from('student_profiles')
          .select('*')
          .eq('user_id', currentUser.id)
          .single();

        profileData = studentProfile;
      } else if (currentUser.role === 'consultant') {
        const { data: consultantProfile } = await supabaseAdmin
          .from('consultants')
          .select('*')
          .eq('user_id', currentUser.id)
          .single();

        profileData = consultantProfile;
      }

      const userData = {
        ...currentUser,
        profile: profileData
      };

      return createStandardSuccessResponse(
        userData,
        'Current user profile retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          has_profile: !!profileData,
          profile_type:
            currentUser.role === 'student'
              ? 'student_profile'
              : currentUser.role === 'consultant'
                ? 'consultant_profile'
                : null
        }
      );
    }, 'Failed to retrieve user profile');
  }
);

// PUT /api/users/me - Update current user profile
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        email?: string;
        profile_data?: any;
      }>(request);

      const { data: user, error } = await supabaseAdmin
        .from('users')
        .update({
          ...body,
          updated_at: new Date().toISOString()
        })
        .eq('id', currentUser.id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        user,
        'Profile updated successfully',
        200,
        {
          updated_fields: Object.keys(body),
          user_role: currentUser.role
        }
      );
    }, 'Failed to update user profile');
  }
);
