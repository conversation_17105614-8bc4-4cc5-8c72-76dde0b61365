import { NextRequest } from 'next/server';
import {
  withAuth,
  withRole,
  createSuccessResponse,
  parseRequestBody,
  validateRequiredFields,
  parsePaginationParams,
  createPaginatedResponse
} from '@/lib/api-utils';
import { supabaseAdmin, upsertUser } from '@/lib/supabase';
import type { User } from '@/types/application';

// GET /api/users - List users (Admin only)
export const GET = withRole(['admin'], async (request: NextRequest) => {
  const pagination = parsePaginationParams(request);
  const url = new URL(request.url);
  const role = url.searchParams.get('role');

  let query = supabaseAdmin
    .from('users')
    .select('*', { count: 'exact' })
    .range(pagination.offset, pagination.offset + pagination.limit - 1)
    .order('created_at', { ascending: false });

  if (role) {
    query = query.eq('role', role);
  }

  const { data, error, count } = await query;

  if (error) {
    throw error;
  }

  return createPaginatedResponse(
    data || [],
    count || 0,
    pagination,
    'Users retrieved successfully'
  );
});

// POST /api/users - Create or update user
export const POST = withAuth(
  async (request: NextRequest, currentUser: User) => {
    const body = await parseRequestBody<{
      clerk_id: string;
      email: string;
      role: 'student' | 'consultant' | 'admin';
      profile_data?: any;
    }>(request);

    validateRequiredFields(body, ['clerk_id', 'email', 'role']);

    // Only admins can create users with admin or consultant roles
    if (body.role !== 'student' && currentUser.role !== 'admin') {
      throw new Error('Only admins can create consultant or admin users');
    }

    // Only admins can create users other than themselves
    if (
      body.clerk_id !== currentUser.clerk_id &&
      currentUser.role !== 'admin'
    ) {
      throw new Error('You can only create your own user profile');
    }

    const user = await upsertUser(body);

    // Create role-specific profile
    if (body.role === 'student') {
      const { error: profileError } = await supabaseAdmin
        .from('student_profiles')
        .upsert(
          {
            user_id: user.id,
            academic_info: {},
            target_schools: [],
            application_status: {}
          },
          {
            onConflict: 'user_id'
          }
        );

      if (profileError) {
        throw profileError;
      }
    } else if (body.role === 'consultant') {
      const { error: profileError } = await supabaseAdmin
        .from('consultants')
        .upsert(
          {
            user_id: user.id,
            specialties: [],
            availability: {}
          },
          {
            onConflict: 'user_id'
          }
        );

      if (profileError) {
        throw profileError;
      }
    }

    return createSuccessResponse(
      user,
      'User created/updated successfully',
      201
    );
  }
);
