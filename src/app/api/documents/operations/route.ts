import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  parseRequestBody,
  validateRequiredFields,
  handleAsyncOperation,
  AuthorizationError
} from '@/lib/api-utils';
import { DocumentManagementService } from '@/lib/document-management';
import { AdvancedPermissionManager } from '@/lib/permission-manager';
import { GoogleDocsService } from '@/lib/google-apis';
import { supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

// POST /api/documents/operations - Perform bulk operations on documents
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        operation:
          | 'bulk_create'
          | 'bulk_permission_repair'
          | 'sync_content'
          | 'export_documents';
        documents?: Array<{
          student_id: string;
          doc_type:
            | 'essay'
            | 'personal_statement'
            | 'transcript'
            | 'activity_resume'
            | 'other';
          metadata: {
            title: string;
            school_id?: string;
            word_limit?: number;
            deadline?: string;
          };
          template_id?: string;
          initial_content?: string;
        }>;
        student_ids?: string[];
        document_ids?: string[];
        export_format?: 'pdf' | 'docx' | 'txt';
      }>(request);

      validateRequiredFields(body, ['operation']);

      switch (body.operation) {
        case 'bulk_create':
          // Only admins and students can bulk create documents
          if (currentUser.role === 'consultant') {
            throw new AuthorizationError('Consultants cannot create documents');
          }

          if (!body.documents || body.documents.length === 0) {
            throw new Error(
              'documents array is required for bulk_create operation'
            );
          }

          const createdDocuments = [];
          const errors = [];

          for (const docRequest of body.documents) {
            try {
              // Permission check for students
              if (currentUser.role === 'student') {
                const { data: studentProfile } = await supabaseAdmin
                  .from('student_profiles')
                  .select('user_id')
                  .eq('id', docRequest.student_id)
                  .single();

                if (
                  !studentProfile ||
                  studentProfile.user_id !== currentUser.id
                ) {
                  throw new Error(`You can only create documents for yourself`);
                }
              }

              const document =
                await DocumentManagementService.createDocument(docRequest);
              createdDocuments.push(document);
            } catch (error) {
              errors.push({
                document: docRequest,
                error: error instanceof Error ? error.message : 'Unknown error'
              });
            }
          }

          return createStandardSuccessResponse(
            {
              created: createdDocuments,
              errors: errors,
              summary: {
                total: body.documents.length,
                successful: createdDocuments.length,
                failed: errors.length
              }
            },
            'Bulk document creation completed',
            200,
            {
              user_role: currentUser.role,
              operation: 'bulk_create',
              documents_requested: body.documents.length,
              success_rate: Math.round(
                (createdDocuments.length / body.documents.length) * 100
              )
            }
          );

        case 'bulk_permission_repair':
          // Only admins can perform bulk permission repairs
          if (currentUser.role !== 'admin') {
            throw new AuthorizationError(
              'Only admins can perform bulk permission repairs'
            );
          }

          if (!body.student_ids || body.student_ids.length === 0) {
            throw new Error(
              'student_ids array is required for bulk_permission_repair operation'
            );
          }

          const repairResults = [];
          const repairErrors = [];

          for (const studentId of body.student_ids) {
            try {
              await AdvancedPermissionManager.repairStudentPermissions(
                studentId
              );
              repairResults.push({ student_id: studentId, status: 'success' });
            } catch (error) {
              repairErrors.push({
                student_id: studentId,
                error: error instanceof Error ? error.message : 'Unknown error'
              });
            }
          }

          return createStandardSuccessResponse(
            {
              repaired: repairResults,
              errors: repairErrors,
              summary: {
                total: body.student_ids.length,
                successful: repairResults.length,
                failed: repairErrors.length
              }
            },
            'Bulk permission repair completed',
            200,
            {
              user_role: currentUser.role,
              operation: 'bulk_permission_repair',
              students_processed: body.student_ids.length,
              success_rate: Math.round(
                (repairResults.length / body.student_ids.length) * 100
              )
            }
          );

        case 'sync_content':
          // Sync content between database and Google Docs
          if (!body.document_ids || body.document_ids.length === 0) {
            throw new Error(
              'document_ids array is required for sync_content operation'
            );
          }

          const syncResults = [];
          const syncErrors = [];

          for (const documentId of body.document_ids) {
            try {
              // Get document with permission check
              const document = await DocumentManagementService.getDocument(
                documentId,
                currentUser.id,
                currentUser.role
              );

              if (!document) {
                throw new Error('Document not found or access denied');
              }

              if (!document.google_doc_id) {
                throw new Error('No Google Doc associated with this document');
              }

              // Get current content from Google Docs
              const content = await GoogleDocsService.getDocumentContent(
                document.google_doc_id
              );

              // Update metadata with content info
              const updatedMetadata = {
                ...document.metadata,
                last_synced: new Date().toISOString(),
                word_count: content.split(/\s+/).length,
                character_count: content.length
              };

              // Update document in database
              const { supabaseAdmin } = await import('@/lib/supabase');
              await supabaseAdmin
                .from('documents')
                .update({ metadata: updatedMetadata })
                .eq('id', documentId);

              syncResults.push({
                document_id: documentId,
                status: 'success',
                word_count: updatedMetadata.word_count,
                character_count: updatedMetadata.character_count
              });
            } catch (error) {
              syncErrors.push({
                document_id: documentId,
                error: error instanceof Error ? error.message : 'Unknown error'
              });
            }
          }

          return createStandardSuccessResponse(
            {
              synced: syncResults,
              errors: syncErrors,
              summary: {
                total: body.document_ids.length,
                successful: syncResults.length,
                failed: syncErrors.length
              }
            },
            'Content sync completed',
            200,
            {
              user_role: currentUser.role,
              operation: 'sync_content',
              documents_processed: body.document_ids.length,
              success_rate: Math.round(
                (syncResults.length / body.document_ids.length) * 100
              )
            }
          );

        case 'export_documents':
          // Export documents in specified format
          if (!body.document_ids || body.document_ids.length === 0) {
            throw new Error(
              'document_ids array is required for export_documents operation'
            );
          }

          const exportFormat = body.export_format || 'pdf';
          const exportResults = [];
          const exportErrors = [];

          for (const documentId of body.document_ids) {
            try {
              // Get document with permission check
              const document = await DocumentManagementService.getDocument(
                documentId,
                currentUser.id,
                currentUser.role
              );

              if (!document) {
                throw new Error('Document not found or access denied');
              }

              if (!document.google_doc_id) {
                throw new Error('No Google Doc associated with this document');
              }

              // For now, return the Google Docs export URL
              // In a full implementation, you would use the Google Drive API to export
              const exportUrl = `https://docs.google.com/document/d/${document.google_doc_id}/export?format=${exportFormat}`;

              exportResults.push({
                document_id: documentId,
                title: document.metadata.title,
                export_url: exportUrl,
                format: exportFormat
              });
            } catch (error) {
              exportErrors.push({
                document_id: documentId,
                error: error instanceof Error ? error.message : 'Unknown error'
              });
            }
          }

          return createStandardSuccessResponse(
            {
              exports: exportResults,
              errors: exportErrors,
              summary: {
                total: body.document_ids.length,
                successful: exportResults.length,
                failed: exportErrors.length
              }
            },
            'Document export completed',
            200,
            {
              user_role: currentUser.role,
              operation: 'export_documents',
              export_format: body.export_format || 'pdf',
              documents_processed: body.document_ids.length,
              success_rate: Math.round(
                (exportResults.length / body.document_ids.length) * 100
              )
            }
          );

        default:
          throw new Error(`Unknown operation: ${body.operation}`);
      }
    }, 'Failed to perform document operation');
  }
);
