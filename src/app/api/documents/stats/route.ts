import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation
} from '@/lib/api-utils';
import { DocumentManagementService } from '@/lib/document-management';
import type { User } from '@/types/application';

// GET /api/documents/stats - Get document statistics
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const url = new URL(request.url);
      const studentId = url.searchParams.get('student_id');

      // Use DocumentManagementService to avoid RLS infinite recursion issues
      const documents = await DocumentManagementService.getDocumentsForUser(
        currentUser.id,
        currentUser.role
      );

      // Apply additional filters from query params
      let filteredDocuments = documents;

      if (studentId) {
        filteredDocuments = filteredDocuments.filter(
          (doc) => doc.student_id === studentId
        );
      }
      // Note: Role-based filtering and permissions are already handled by DocumentManagementService

      // Calculate statistics
      const totalDocuments = filteredDocuments?.length || 0;
      const documentsWithGoogleDocs =
        filteredDocuments?.filter((doc) => doc.google_doc_id).length || 0;

      // Documents by type
      const documentsByType =
        filteredDocuments?.reduce(
          (acc, doc) => {
            acc[doc.doc_type] = (acc[doc.doc_type] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        ) || {};

      // Recent activity (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentDocuments =
        filteredDocuments?.filter((doc) => new Date(doc.created_at) > thirtyDaysAgo) ||
        [];

      // For now, skip student names to avoid RLS issues - focus on getting stats working
      const recentActivity = recentDocuments.map((doc) => ({
        id: doc.id,
        title: doc.metadata?.title || 'Untitled',
        doc_type: doc.doc_type,
        created_at: doc.created_at,
        student_name: 'Student', // Simplified to avoid RLS issues
        has_google_doc: !!doc.google_doc_id
      }));

      // Completion statistics
      const completionStats = {
        draft:
          filteredDocuments?.filter((doc) => doc.metadata?.status === 'draft').length ||
          0,
        in_review:
          filteredDocuments?.filter((doc) => doc.metadata?.status === 'in_review')
            .length || 0,
        completed:
          filteredDocuments?.filter((doc) => doc.metadata?.status === 'completed')
            .length || 0,
        no_status: filteredDocuments?.filter((doc) => !doc.metadata?.status).length || 0
      };

      // Word count statistics (if available in metadata)
      const wordCountStats = {
        total_words: 0,
        average_words: 0,
        documents_with_word_count: 0
      };

      const documentsWithWordCount =
        filteredDocuments?.filter(
          (doc) =>
            (doc.metadata as any)?.word_count &&
            typeof (doc.metadata as any).word_count === 'number'
        ) || [];

      if (documentsWithWordCount.length > 0) {
        wordCountStats.total_words = documentsWithWordCount.reduce(
          (sum, doc) => sum + ((doc.metadata as any).word_count || 0),
          0
        );
        wordCountStats.average_words = Math.round(
          wordCountStats.total_words / documentsWithWordCount.length
        );
        wordCountStats.documents_with_word_count =
          documentsWithWordCount.length;
      }

      // Document type breakdown with Google Docs integration status
      const typeBreakdown = Object.keys(documentsByType).map((type) => {
        const typeDocuments =
          filteredDocuments?.filter((doc) => doc.doc_type === type) || [];
        const withGoogleDocs = typeDocuments.filter(
          (doc) => doc.google_doc_id
        ).length;

        return {
          type,
          total: documentsByType[type],
          with_google_docs: withGoogleDocs,
          integration_percentage:
            documentsByType[type] > 0
              ? Math.round((withGoogleDocs / documentsByType[type]) * 100)
              : 0
        };
      });

      // Monthly creation trend (last 6 months)
      const monthlyTrend = [];
      for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
        const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

        const monthDocuments =
          filteredDocuments?.filter((doc) => {
            const docDate = new Date(doc.created_at);
            return docDate >= monthStart && docDate <= monthEnd;
          }).length || 0;

        monthlyTrend.push({
          month: date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short'
          }),
          documents_created: monthDocuments
        });
      }

      const stats = {
        total_documents: totalDocuments,
        documents_with_google_docs: documentsWithGoogleDocs,
        google_docs_integration_percentage:
          totalDocuments > 0
            ? Math.round((documentsWithGoogleDocs / totalDocuments) * 100)
            : 0,
        documents_by_type: documentsByType,
        type_breakdown: typeBreakdown,
        recent_activity: recentActivity.slice(0, 10), // Limit to 10 most recent
        completion_stats: completionStats,
        word_count_stats: wordCountStats,
        monthly_trend: monthlyTrend,
        summary: {
          most_common_type: Object.keys(documentsByType).reduce(
            (a, b) => (documentsByType[a] > documentsByType[b] ? a : b),
            'none'
          ),
          completion_rate:
            totalDocuments > 0
              ? Math.round((completionStats.completed / totalDocuments) * 100)
              : 0,
          recent_activity_count: recentDocuments.length
        }
      };

      return createStandardSuccessResponse(
        stats,
        'Document statistics retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          student_filter_applied: !!studentId,
          total_documents_analyzed: totalDocuments,
          google_docs_integration_rate:
            stats.google_docs_integration_percentage,
          operation_type: 'stats'
        }
      );
    }, 'Failed to retrieve document statistics');
  }
);
