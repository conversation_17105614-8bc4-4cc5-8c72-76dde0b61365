import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  parseRequestBody,
  handleAsyncOperation,
  NotFoundError,
  AuthorizationError
} from '@/lib/api-utils';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import { DocumentManagementService } from '@/lib/document-management';
import type { User, DocumentMetadata } from '@/types/application';

interface RouteContext {
  params: Promise<{ id: string }>;
}

// Helper function to check if user can access a specific document
async function canAccessDocument(
  currentUser: User,
  documentId: string
): Promise<boolean> {
  const { data: document } = await supabase
    .from('documents')
    .select('student_id')
    .eq('id', documentId)
    .single();

  if (!document) return false;

  if (currentUser.role === 'admin') return true;

  if (currentUser.role === 'student') {
    const { data: studentProfile } = await supabase
      .from('student_profiles')
      .select('user_id')
      .eq('id', document.student_id)
      .single();
    return studentProfile?.user_id === currentUser.id;
  }

  if (currentUser.role === 'consultant') {
    const { data: consultant } = await supabase
      .from('consultants')
      .select('id')
      .eq('user_id', currentUser.id)
      .single();

    if (!consultant) return false;

    const { data: relation } = await supabase
      .from('student_consultant_relations')
      .select('id')
      .eq('student_id', document.student_id)
      .eq('consultant_id', consultant.id)
      .eq('status', 'active')
      .single();

    return !!relation;
  }

  return false;
}

// GET /api/documents/[id] - Get document by ID
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = await context.params;

      const document = await DocumentManagementService.getDocument(
        id,
        currentUser.id,
        currentUser.role
      );

      if (!document) {
        throw new NotFoundError('Document not found');
      }

      return createStandardSuccessResponse(
        document,
        'Document retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          document_id: id,
          has_google_doc: !!document.google_doc_id,
          doc_type: document.doc_type,
          operation_type: 'read'
        }
      );
    }, 'Failed to retrieve document');
  }
);

// PUT /api/documents/[id] - Update document
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = await context.params;

      if (!(await canAccessDocument(currentUser, id))) {
        throw new AuthorizationError('You do not have access to this document');
      }

      const body = await parseRequestBody<{
        google_doc_id?: string;
        doc_type?:
          | 'essay'
          | 'personal_statement'
          | 'transcript'
          | 'activity_resume'
          | 'other';
        metadata?: DocumentMetadata;
      }>(request);

      const { data: document, error } = await supabaseAdmin
        .from('documents')
        .update({
          ...body,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      if (!document) {
        throw new NotFoundError('Document not found');
      }

      return createStandardSuccessResponse(
        document,
        'Document updated successfully',
        200,
        {
          user_role: currentUser.role,
          document_id: id,
          updated_fields: Object.keys(body),
          operation_type: 'update'
        }
      );
    }, 'Failed to update document');
  }
);

// DELETE /api/documents/[id] - Delete document
export const DELETE = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = await context.params;

      // Only students and admins can delete documents
      if (currentUser.role === 'consultant') {
        throw new AuthorizationError('Consultants cannot delete documents');
      }

      await DocumentManagementService.deleteDocument(
        id,
        currentUser.id,
        currentUser.role
      );

      return createStandardSuccessResponse(
        null,
        'Document deleted successfully',
        200,
        {
          user_role: currentUser.role,
          document_id: id,
          operation_type: 'delete'
        }
      );
    }, 'Failed to delete document');
  }
);
