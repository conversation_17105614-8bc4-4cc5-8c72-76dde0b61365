import { NextRequest } from 'next/server';
import {
  withAuth,
  createSuccessResponse,
  parseRequestBody,
  NotFoundError,
  AuthorizationError,
  validateRequiredFields
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import {
  GoogleDocsService,
  DocumentPermissionService
} from '@/lib/google-apis';
import { DocumentManagementService } from '@/lib/document-management';
import type { User } from '@/types/application';

interface RouteContext {
  params: Promise<{ id: string }>;
}

// POST /api/documents/[id]/google-doc - Create Google Doc for document
export const POST = withAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    const { id } = await context.params;

    // Use DocumentManagementService for consistent permission checking
    const document = await DocumentManagementService.getDocument(
      id,
      currentUser.id,
      currentUser.role
    );

    if (!document) {
      throw new AuthorizationError('You do not have access to this document');
    }

    const body = await parseRequestBody<{
      title: string;
      content?: string;
      template_id?: string;
    }>(request);

    validateRequiredFields(body, ['title']);

    // Document is already retrieved and validated above

    // Check if Google Doc already exists
    if (document.google_doc_id) {
      throw new Error('Google Doc already exists for this document');
    }

    try {
      // Get student name for folder organization
      const studentName = document.student_name || 'Student';

      // Create Google Doc with proper folder structure
      const validDocType = document.doc_type as
        | 'essay'
        | 'personal_statement'
        | 'transcript'
        | 'activity_resume'
        | 'other';
      const { documentId, documentUrl, folderId } =
        await GoogleDocsService.createDocument(
          body.title,
          studentName,
          validDocType,
          body.content,
          body.template_id
        );

      // Get student email
      const studentEmail = document.student_email;
      if (!studentEmail) {
        throw new Error('Student email not found for document permissions');
      }

      // Get consultant emails if any
      const consultantEmails = document.consultant_emails || [];

      // Set up permissions
      await DocumentPermissionService.setupDocumentPermissions(
        documentId,
        studentEmail,
        consultantEmails
      );

      // Also set up folder permissions for the student if this is their first document
      await DocumentPermissionService.setupStudentFolderPermissions(
        folderId,
        studentEmail,
        consultantEmails
      );

      // Update document record with Google Doc ID
      const { data: updatedDocument, error: updateError } = await supabaseAdmin
        .from('documents')
        .update({
          google_doc_id: documentId,
          metadata: {
            ...document.metadata,
            google_doc_url: documentUrl,
            folder_id: folderId,
            title: body.title,
            created_from_template: !!body.template_id
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (updateError) {
        throw updateError;
      }

      return createSuccessResponse(
        {
          document: updatedDocument,
          google_doc_id: documentId,
          google_doc_url: documentUrl
        },
        'Google Doc created successfully',
        201
      );
    } catch (error) {
      console.error('Error creating Google Doc:', error);
      throw new Error('Failed to create Google Doc');
    }
  }
);

// GET /api/documents/[id]/google-doc - Get Google Doc content
export const GET = withAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    const { id } = await context.params;

    // Use DocumentManagementService for consistent permission checking
    const document = await DocumentManagementService.getDocument(
      id,
      currentUser.id,
      currentUser.role
    );

    if (!document) {
      throw new AuthorizationError('You do not have access to this document');
    }

    if (!document.google_doc_id) {
      throw new NotFoundError('No Google Doc associated with this document');
    }

    try {
      const content = await GoogleDocsService.getDocumentContent(
        document.google_doc_id
      );

      return createSuccessResponse(
        {
          google_doc_id: document.google_doc_id,
          content,
          metadata: document.metadata
        },
        'Google Doc content retrieved successfully'
      );
    } catch (error) {
      console.error('Error getting Google Doc content:', error);
      throw new Error('Failed to get Google Doc content');
    }
  }
);
