import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  createStandardPaginatedResponse,
  parseRequestBody,
  validateRequiredFields,
  parsePaginationParams,
  handleAsyncOperation,
  AuthorizationError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { optimizedQueries } from '@/lib/database-optimization';
import {
  DocumentManagementService,
  type CreateDocumentRequest
} from '@/lib/document-management';
import type { User, DocumentMetadata } from '@/types/application';

// Helper function to check if user can access documents for a student
async function canAccessStudentDocuments(
  currentUser: User,
  studentId: string
): Promise<boolean> {
  if (currentUser.role === 'admin') return true;

  if (currentUser.role === 'student') {
    const { data: studentProfile } = await supabaseAdmin
      .from('student_profiles')
      .select('user_id')
      .eq('id', studentId)
      .single();
    return studentProfile?.user_id === currentUser.id;
  }

  if (currentUser.role === 'consultant') {
    const { data: consultant } = await supabaseAdmin
      .from('consultants')
      .select('id')
      .eq('user_id', currentUser.id)
      .single();

    if (!consultant) return false;

    const { data: relation } = await supabaseAdmin
      .from('student_consultant_relations')
      .select('id')
      .eq('student_id', studentId)
      .eq('consultant_id', consultant.id)
      .eq('status', 'active')
      .single();

    return !!relation;
  }

  return false;
}

// GET /api/documents - List documents
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const documents = await DocumentManagementService.getDocumentsForUser(
        currentUser.id,
        currentUser.role
      );

      // Apply additional filters from query params
      const url = new URL(request.url);
      const studentId = url.searchParams.get('student_id');
      const docType = url.searchParams.get('doc_type');

      let filteredDocuments = documents;

      if (studentId) {
        filteredDocuments = filteredDocuments.filter(
          (doc) => doc.student_id === studentId
        );
      }

      if (docType) {
        filteredDocuments = filteredDocuments.filter(
          (doc) => doc.doc_type === docType
        );
      }

      // Apply pagination
      const pagination = parsePaginationParams(request);
      const startIndex = pagination.offset;
      const endIndex = pagination.offset + pagination.limit;
      const paginatedDocuments = filteredDocuments.slice(startIndex, endIndex);

      return createStandardPaginatedResponse(
        paginatedDocuments,
        filteredDocuments.length,
        pagination,
        'Documents retrieved successfully',
        {
          user_role: currentUser.role,
          total_documents: documents.length,
          filtered_documents: filteredDocuments.length,
          applied_filters: {
            student_id: studentId,
            doc_type: docType
          },
          operation_type: 'list'
        }
      );
    }, 'Failed to retrieve documents');
  }
);

// POST /api/documents - Create new document with Google Docs integration
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        student_id: string;
        doc_type:
          | 'essay'
          | 'personal_statement'
          | 'transcript'
          | 'activity_resume'
          | 'other';
        metadata: DocumentMetadata;
        template_id?: string;
        initial_content?: string;
      }>(request);

      validateRequiredFields(body, ['student_id', 'doc_type', 'metadata']);

      // Check if user can create documents for this student
      if (!(await canAccessStudentDocuments(currentUser, body.student_id))) {
        throw new AuthorizationError(
          'You do not have access to create documents for this student'
        );
      }

      // Ensure metadata has required title
      if (!body.metadata.title) {
        throw new Error('Document title is required in metadata');
      }

      const createRequest: CreateDocumentRequest = {
        student_id: body.student_id,
        doc_type: body.doc_type,
        metadata: {
          title: body.metadata.title!, // Ensure title is required
          school_id: body.metadata.school_id,
          word_limit: (body.metadata as any).word_limit,
          deadline: (body.metadata as any).deadline,
          status: body.metadata.status || 'draft',
          google_doc_url: (body.metadata as any).google_doc_url,
          folder_id: (body.metadata as any).folder_id
        },
        template_id: body.template_id,
        initial_content: body.initial_content
      };

      const document =
        await DocumentManagementService.createDocument(createRequest);

      return createStandardSuccessResponse(
        document,
        'Document created successfully with Google Docs integration',
        201,
        {
          user_role: currentUser.role,
          student_id: body.student_id,
          doc_type: body.doc_type,
          has_template: !!body.template_id,
          has_initial_content: !!body.initial_content,
          google_docs_integration: !!document.google_doc_id,
          operation_type: 'create'
        }
      );
    }, 'Failed to create document');
  }
);
