import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  AuthorizationError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

interface DashboardStats {
  user: {
    totalUsers: number;
    totalStudents: number;
    totalConsultants: number;
    activeUsers: number;
    newUsersThisMonth: number;
  };
  documents: {
    totalDocuments: number;
    documentsWithGoogleDocs: number;
    googleDocsIntegrationPercentage: number;
    documentsByType: Record<string, number>;
    completionStats: {
      draft: number;
      in_review: number;
      completed: number;
    };
    recentActivityCount: number;
  };
  applications: {
    totalApplications: number;
    activeApplications: number;
    submittedApplications: number;
    averageProgress: number;
  };
  activity: {
    totalActivities: number;
    totalHours: number;
    categoriesCount: number;
    leadershipRoles: number;
  };
}

interface StudentDashboardStats {
  progress: {
    applicationProgress: number;
    essaysCompleted: number;
    totalEssays: number;
    schoolsApplied: number;
    totalSchools: number;
  };
  documents: {
    totalDocuments: number;
    completedDocuments: number;
    inReviewDocuments: number;
    draftDocuments: number;
  };
  activities: {
    totalActivities: number;
    totalHours: number;
    leadershipRoles: number;
  };
  testScores: {
    satScore?: number;
    actScore?: number;
    apScores: number;
  };
  advisor?: {
    id: string;
    name: string;
    email: string;
    title: string;
  };
}

interface ConsultantDashboardStats {
  students: {
    totalStudents: number;
    activeStudents: number;
    studentsNeedingAttention: number;
  };
  documents: {
    documentsToReview: number;
    documentsReviewedThisWeek: number;
    totalDocumentsManaged: number;
  };
  meetings: {
    upcomingMeetings: number;
    meetingsThisWeek: number;
    totalMeetingsHeld: number;
  };
  performance: {
    averageStudentProgress: number;
    studentsWithHighProgress: number;
    studentsWithLowProgress: number;
  };
}

// GET /api/stats/dashboard - Get dashboard statistics based on user role
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const url = new URL(request.url);
      const role = url.searchParams.get('role') || currentUser.role;

      // Ensure users can only access their own role stats (except admins)
      if (currentUser.role !== 'admin' && role !== currentUser.role) {
        throw new AuthorizationError(
          'You can only access statistics for your own role'
        );
      }

      switch (role) {
        case 'student':
          const studentStats = await getStudentDashboardStats(currentUser.id);
          return createStandardSuccessResponse(
            studentStats,
            'Student dashboard statistics retrieved successfully',
            200,
            {
              user_role: currentUser.role,
              stats_type: 'student_dashboard',
              user_id: currentUser.id
            }
          );

        case 'consultant':
          const consultantStats = await getConsultantDashboardStats(
            currentUser.id
          );
          return createStandardSuccessResponse(
            consultantStats,
            'Consultant dashboard statistics retrieved successfully',
            200,
            {
              user_role: currentUser.role,
              stats_type: 'consultant_dashboard',
              user_id: currentUser.id
            }
          );

        case 'admin':
          const adminStats = await getAdminDashboardStats();
          return createStandardSuccessResponse(
            adminStats,
            'Admin dashboard statistics retrieved successfully',
            200,
            {
              user_role: currentUser.role,
              stats_type: 'admin_dashboard',
              user_id: currentUser.id
            }
          );

        default:
          throw new Error(`Invalid role: ${role}`);
      }
    });
  }
);

async function getStudentDashboardStats(
  userId: string
): Promise<StudentDashboardStats> {
  // Get student profile
  const { data: studentProfile } = await supabaseAdmin
    .from('student_profiles')
    .select('id')
    .eq('user_id', userId)
    .single();

  if (!studentProfile) {
    throw new Error('Student profile not found');
  }

  const studentId = studentProfile.id;

  // Get documents stats
  const { data: documents } = await supabaseAdmin
    .from('documents')
    .select('id, metadata')
    .eq('student_id', studentId);

  const totalDocuments = documents?.length || 0;
  const completedDocuments =
    documents?.filter(
      (d) => d.metadata && (d.metadata as any).status === 'completed'
    ).length || 0;
  const inReviewDocuments =
    documents?.filter(
      (d) => d.metadata && (d.metadata as any).status === 'in_review'
    ).length || 0;
  const draftDocuments =
    documents?.filter(
      (d) =>
        d.metadata &&
        ((d.metadata as any).status === 'draft' || !(d.metadata as any).status)
    ).length || 0;

  // Get activities stats
  const { data: activities } = await supabaseAdmin
    .from('activities')
    .select('id, hours_per_week, weeks_per_year, position')
    .eq('student_id', studentId);

  const totalActivities = activities?.length || 0;
  const totalHours =
    activities?.reduce(
      (sum, activity) =>
        sum + (activity.hours_per_week || 0) * (activity.weeks_per_year || 0),
      0
    ) || 0;
  const leadershipRoles =
    activities?.filter(
      (activity) =>
        (activity.position &&
          activity.position.toLowerCase().includes('president')) ||
        (activity.position &&
          activity.position.toLowerCase().includes('captain')) ||
        (activity.position &&
          activity.position.toLowerCase().includes('leader'))
    ).length || 0;

  // Get test scores
  const { data: testScores } = await supabaseAdmin
    .from('test_scores')
    .select('test_type, score')
    .eq('student_id', studentId);

  const satScore = testScores?.find((ts) => ts.test_type === 'SAT')?.score;
  const actScore = testScores?.find((ts) => ts.test_type === 'ACT')?.score;
  const apScores =
    testScores?.filter((ts) => ts.test_type === 'AP').length || 0;

  // Get target schools count
  const { data: targetSchools } = await supabaseAdmin
    .from('target_schools')
    .select('id')
    .eq('student_id', studentId);

  const totalSchools = targetSchools?.length || 0;

  // Get application status
  const { data: applications } = await supabaseAdmin
    .from('application_status')
    .select('id, status')
    .eq('student_id', studentId);

  const schoolsApplied =
    applications?.filter(
      (app) => app.status === 'submitted' || app.status === 'in_progress'
    ).length || 0;

  // Calculate application progress (simplified)
  const applicationProgress =
    totalSchools > 0 ? Math.round((schoolsApplied / totalSchools) * 100) : 0;

  // Get assigned consultant
  const { data: consultantRelation } = await supabaseAdmin
    .from('student_consultant_relations')
    .select(
      `
      consultants!inner(
        id,
        users!inner(email, profile_data)
      )
    `
    )
    .eq('student_id', studentId)
    .eq('status', 'active')
    .single();

  let advisor = undefined;
  if (consultantRelation) {
    const consultant = (consultantRelation as any).consultants;
    const user = consultant.users;
    advisor = {
      id: consultant.id,
      name: user.profile_data?.name || user.email.split('@')[0],
      email: user.email,
      title: 'College Counselor'
    };
  }

  return {
    progress: {
      applicationProgress,
      essaysCompleted: completedDocuments,
      totalEssays: totalDocuments,
      schoolsApplied,
      totalSchools
    },
    documents: {
      totalDocuments,
      completedDocuments,
      inReviewDocuments,
      draftDocuments
    },
    activities: {
      totalActivities,
      totalHours,
      leadershipRoles
    },
    testScores: {
      satScore,
      actScore,
      apScores
    },
    advisor
  };
}

async function getConsultantDashboardStats(
  userId: string
): Promise<ConsultantDashboardStats> {
  // Get consultant profile
  const { data: consultantProfile } = await supabaseAdmin
    .from('consultants')
    .select('id')
    .eq('user_id', userId)
    .single();

  if (!consultantProfile) {
    throw new Error('Consultant profile not found');
  }

  const consultantId = consultantProfile.id;

  // Get assigned students
  const { data: studentRelations } = await supabaseAdmin
    .from('student_consultant_relations')
    .select('student_id')
    .eq('consultant_id', consultantId)
    .eq('status', 'active');

  const assignedStudentIds = studentRelations?.map((r) => r.student_id) || [];
  const totalStudents = assignedStudentIds.length;

  if (totalStudents === 0) {
    return {
      students: {
        totalStudents: 0,
        activeStudents: 0,
        studentsNeedingAttention: 0
      },
      documents: {
        documentsToReview: 0,
        documentsReviewedThisWeek: 0,
        totalDocumentsManaged: 0
      },
      meetings: {
        upcomingMeetings: 0,
        meetingsThisWeek: 0,
        totalMeetingsHeld: 0
      },
      performance: {
        averageStudentProgress: 0,
        studentsWithHighProgress: 0,
        studentsWithLowProgress: 0
      }
    };
  }

  // Get documents for assigned students
  const { data: documents } = await supabaseAdmin
    .from('documents')
    .select('id, metadata, updated_at')
    .in('student_id', assignedStudentIds);

  const totalDocumentsManaged = documents?.length || 0;
  const documentsToReview =
    documents?.filter(
      (d) => d.metadata && (d.metadata as any).status === 'in_review'
    ).length || 0;

  // Documents reviewed this week (simplified - documents updated this week)
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  const documentsReviewedThisWeek =
    documents?.filter((d) => new Date(d.updated_at) > oneWeekAgo).length || 0;

  // Calculate student progress metrics (simplified)
  const activeStudents = totalStudents; // Assume all assigned students are active
  const studentsNeedingAttention = Math.floor(totalStudents * 0.2); // 20% need attention
  const averageStudentProgress = 65; // Simplified calculation
  const studentsWithHighProgress = Math.floor(totalStudents * 0.6);
  const studentsWithLowProgress = Math.floor(totalStudents * 0.2);

  return {
    students: {
      totalStudents,
      activeStudents,
      studentsNeedingAttention
    },
    documents: {
      documentsToReview,
      documentsReviewedThisWeek,
      totalDocumentsManaged
    },
    meetings: {
      upcomingMeetings: 3, // Placeholder - would need meetings table
      meetingsThisWeek: 5,
      totalMeetingsHeld: 45
    },
    performance: {
      averageStudentProgress,
      studentsWithHighProgress,
      studentsWithLowProgress
    }
  };
}

async function getAdminDashboardStats(): Promise<DashboardStats> {
  // Get user statistics
  const { data: users } = await supabaseAdmin
    .from('users')
    .select('id, role, created_at');

  const totalUsers = users?.length || 0;
  const totalStudents = users?.filter((u) => u.role === 'student').length || 0;
  const totalConsultants =
    users?.filter((u) => u.role === 'consultant').length || 0;
  const activeUsers = totalUsers; // Simplified - assume all users are active

  // New users this month
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
  const newUsersThisMonth =
    users?.filter((u) => new Date(u.created_at) > oneMonthAgo).length || 0;

  // Get document statistics
  const { data: documents } = await supabaseAdmin
    .from('documents')
    .select('id, doc_type, google_doc_id, metadata, created_at');

  const totalDocuments = documents?.length || 0;
  const documentsWithGoogleDocs =
    documents?.filter((d) => d.google_doc_id).length || 0;
  const googleDocsIntegrationPercentage =
    totalDocuments > 0
      ? Math.round((documentsWithGoogleDocs / totalDocuments) * 100)
      : 0;

  // Documents by type
  const documentsByType: Record<string, number> = {};
  documents?.forEach((doc) => {
    documentsByType[doc.doc_type] = (documentsByType[doc.doc_type] || 0) + 1;
  });

  // Completion stats
  const completionStats = {
    draft:
      documents?.filter(
        (d) => d.metadata && (d.metadata as any).status === 'draft'
      ).length || 0,
    in_review:
      documents?.filter(
        (d) => d.metadata && (d.metadata as any).status === 'in_review'
      ).length || 0,
    completed:
      documents?.filter(
        (d) => d.metadata && (d.metadata as any).status === 'completed'
      ).length || 0
  };

  // Recent activity (documents created this month)
  const recentActivityCount =
    documents?.filter((d) => new Date(d.created_at) > oneMonthAgo).length || 0;

  // Get application statistics (simplified)
  const { data: applications } = await supabaseAdmin
    .from('application_status')
    .select('id, status');

  const totalApplications = applications?.length || 0;
  const activeApplications =
    applications?.filter((a) => a.status === 'in_progress').length || 0;
  const submittedApplications =
    applications?.filter((a) => a.status === 'submitted').length || 0;

  // Get activity statistics
  const { data: activities } = await supabaseAdmin
    .from('activities')
    .select('id, hours_per_week, weeks_per_year, category, position');

  const totalActivities = activities?.length || 0;
  const totalHours =
    activities?.reduce(
      (sum, activity) =>
        sum + (activity.hours_per_week || 0) * (activity.weeks_per_year || 0),
      0
    ) || 0;

  const categories = new Set(
    activities?.map((a) => a.category).filter(Boolean)
  );
  const categoriesCount = categories.size;

  const leadershipRoles =
    activities?.filter(
      (activity) =>
        activity.position &&
        (activity.position.toLowerCase().includes('president') ||
          activity.position.toLowerCase().includes('captain') ||
          activity.position.toLowerCase().includes('leader'))
    ).length || 0;

  return {
    user: {
      totalUsers,
      totalStudents,
      totalConsultants,
      activeUsers,
      newUsersThisMonth
    },
    documents: {
      totalDocuments,
      documentsWithGoogleDocs,
      googleDocsIntegrationPercentage,
      documentsByType,
      completionStats,
      recentActivityCount
    },
    applications: {
      totalApplications,
      activeApplications,
      submittedApplications,
      averageProgress: 65 // Simplified calculation
    },
    activity: {
      totalActivities,
      totalHours,
      categoriesCount,
      leadershipRoles
    }
  };
}
