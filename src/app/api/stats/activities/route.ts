import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  AuthorizationError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

interface ActivityStats {
  overview: {
    totalActivities: number;
    totalHours: number;
    averageHoursPerActivity: number;
    totalStudentsWithActivities: number;
  };
  categories: {
    [category: string]: {
      count: number;
      totalHours: number;
      averageHours: number;
      percentage: number;
    };
  };
  leadership: {
    totalLeadershipRoles: number;
    leadershipPercentage: number;
    topLeadershipPositions: Array<{
      position: string;
      count: number;
    }>;
  };
  timeCommitment: {
    lowCommitment: number; // < 5 hours/week
    mediumCommitment: number; // 5-15 hours/week
    highCommitment: number; // > 15 hours/week
  };
  achievements: {
    totalAchievements: number;
    averageAchievementsPerActivity: number;
    topAchievementTypes: Array<{
      type: string;
      count: number;
    }>;
  };
}

interface StudentActivityStats {
  personal: {
    totalActivities: number;
    totalHours: number;
    leadershipRoles: number;
    categories: string[];
  };
  breakdown: Array<{
    id: string;
    name: string;
    category: string;
    position: string;
    hoursPerWeek: number;
    weeksPerYear: number;
    totalHours: number;
    achievements: string[];
  }>;
  comparison: {
    averageActivitiesPerStudent: number;
    averageHoursPerStudent: number;
    percentileRank: number;
  };
}

// GET /api/stats/activities - Get activity statistics
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const url = new URL(request.url);
      const studentId = url.searchParams.get('student_id');

      if (studentId) {
        // Get specific student's activity stats
        if (currentUser.role === 'student') {
          // Students can only see their own stats
          const { data: studentProfile } = await supabaseAdmin
            .from('student_profiles')
            .select('id')
            .eq('user_id', currentUser.id)
            .single();

          if (!studentProfile || studentProfile.id !== studentId) {
            throw new AuthorizationError(
              'You can only access your own activity statistics'
            );
          }
        } else if (currentUser.role === 'consultant') {
          // Consultants can only see their assigned students' stats
          const { data: consultantProfile } = await supabaseAdmin
            .from('consultants')
            .select('id')
            .eq('user_id', currentUser.id)
            .single();

          if (consultantProfile) {
            const { data: relation } = await supabaseAdmin
              .from('student_consultant_relations')
              .select('id')
              .eq('consultant_id', consultantProfile.id)
              .eq('student_id', studentId)
              .eq('status', 'active')
              .single();

            if (!relation) {
              throw new AuthorizationError(
                'You can only access activity statistics for your assigned students'
              );
            }
          }
        }
        // Admins can access any student's stats

        const studentStats = await getStudentActivityStats(studentId);
        return createStandardSuccessResponse(
          studentStats,
          'Student activity statistics retrieved successfully',
          200,
          {
            user_role: currentUser.role,
            stats_type: 'student_activities',
            student_id: studentId,
            user_id: currentUser.id
          }
        );
      } else {
        // Get overall activity statistics
        if (currentUser.role === 'student') {
          throw new AuthorizationError(
            'Students cannot access overall activity statistics'
          );
        }

        const overallStats = await getOverallActivityStats(currentUser);
        return createStandardSuccessResponse(
          overallStats,
          'Overall activity statistics retrieved successfully',
          200,
          {
            user_role: currentUser.role,
            stats_type: 'overall_activities',
            user_id: currentUser.id
          }
        );
      }
    });
  }
);

async function getStudentActivityStats(
  studentId: string
): Promise<StudentActivityStats> {
  // Get student's activities
  const { data: activities } = await supabaseAdmin
    .from('activities')
    .select('*')
    .eq('student_id', studentId);

  if (!activities || activities.length === 0) {
    return {
      personal: {
        totalActivities: 0,
        totalHours: 0,
        leadershipRoles: 0,
        categories: []
      },
      breakdown: [],
      comparison: {
        averageActivitiesPerStudent: 0,
        averageHoursPerStudent: 0,
        percentileRank: 0
      }
    };
  }

  // Calculate personal stats
  const totalActivities = activities.length;
  const totalHours = activities.reduce(
    (sum, activity) =>
      sum + (activity.hours_per_week || 0) * (activity.weeks_per_year || 0),
    0
  );

  const leadershipRoles = activities.filter(
    (activity) =>
      activity.position &&
      (activity.position.toLowerCase().includes('president') ||
        activity.position.toLowerCase().includes('captain') ||
        activity.position.toLowerCase().includes('leader') ||
        activity.position.toLowerCase().includes('chair') ||
        activity.position.toLowerCase().includes('head'))
  ).length;

  const categories = Array.from(
    new Set(activities.map((a) => a.category).filter(Boolean))
  );

  // Create breakdown
  const breakdown = activities.map((activity) => ({
    id: activity.id,
    name: activity.name || 'Unnamed Activity',
    category: activity.category || 'Other',
    position: activity.position || 'Member',
    hoursPerWeek: activity.hours_per_week || 0,
    weeksPerYear: activity.weeks_per_year || 0,
    totalHours: (activity.hours_per_week || 0) * (activity.weeks_per_year || 0),
    achievements: activity.achievements || []
  }));

  // Get comparison data (all students' activity stats)
  const { data: allActivities } = await supabaseAdmin
    .from('activities')
    .select('student_id, hours_per_week, weeks_per_year');

  // Calculate averages
  const studentActivityCounts = new Map<string, number>();
  const studentHourTotals = new Map<string, number>();

  allActivities?.forEach((activity) => {
    const studentId = activity.student_id;
    const hours =
      (activity.hours_per_week || 0) * (activity.weeks_per_year || 0);

    studentActivityCounts.set(
      studentId,
      (studentActivityCounts.get(studentId) || 0) + 1
    );
    studentHourTotals.set(
      studentId,
      (studentHourTotals.get(studentId) || 0) + hours
    );
  });

  const activityCounts = Array.from(studentActivityCounts.values());
  const hourTotals = Array.from(studentHourTotals.values());

  const averageActivitiesPerStudent =
    activityCounts.length > 0
      ? Math.round(
          (activityCounts.reduce((sum, count) => sum + count, 0) /
            activityCounts.length) *
            10
        ) / 10
      : 0;

  const averageHoursPerStudent =
    hourTotals.length > 0
      ? Math.round(
          (hourTotals.reduce((sum, hours) => sum + hours, 0) /
            hourTotals.length) *
            10
        ) / 10
      : 0;

  // Calculate percentile rank for total hours
  const studentsWithFewerHours = hourTotals.filter(
    (hours) => hours < totalHours
  ).length;
  const percentileRank =
    hourTotals.length > 0
      ? Math.round((studentsWithFewerHours / hourTotals.length) * 100)
      : 0;

  return {
    personal: {
      totalActivities,
      totalHours,
      leadershipRoles,
      categories
    },
    breakdown,
    comparison: {
      averageActivitiesPerStudent,
      averageHoursPerStudent,
      percentileRank
    }
  };
}

async function getOverallActivityStats(
  currentUser: User
): Promise<ActivityStats> {
  let activitiesQuery = supabaseAdmin.from('activities').select('*');

  // If consultant, only get activities for their assigned students
  if (currentUser.role === 'consultant') {
    const { data: consultantProfile } = await supabaseAdmin
      .from('consultants')
      .select('id')
      .eq('user_id', currentUser.id)
      .single();

    if (consultantProfile) {
      const { data: relations } = await supabaseAdmin
        .from('student_consultant_relations')
        .select('student_id')
        .eq('consultant_id', consultantProfile.id)
        .eq('status', 'active');

      const assignedStudentIds = relations?.map((r) => r.student_id) || [];
      if (assignedStudentIds.length > 0) {
        activitiesQuery = activitiesQuery.in('student_id', assignedStudentIds);
      } else {
        // No assigned students
        return {
          overview: {
            totalActivities: 0,
            totalHours: 0,
            averageHoursPerActivity: 0,
            totalStudentsWithActivities: 0
          },
          categories: {},
          leadership: {
            totalLeadershipRoles: 0,
            leadershipPercentage: 0,
            topLeadershipPositions: []
          },
          timeCommitment: {
            lowCommitment: 0,
            mediumCommitment: 0,
            highCommitment: 0
          },
          achievements: {
            totalAchievements: 0,
            averageAchievementsPerActivity: 0,
            topAchievementTypes: []
          }
        };
      }
    }
  }

  const { data: activities } = await activitiesQuery;

  if (!activities || activities.length === 0) {
    return {
      overview: {
        totalActivities: 0,
        totalHours: 0,
        averageHoursPerActivity: 0,
        totalStudentsWithActivities: 0
      },
      categories: {},
      leadership: {
        totalLeadershipRoles: 0,
        leadershipPercentage: 0,
        topLeadershipPositions: []
      },
      timeCommitment: {
        lowCommitment: 0,
        mediumCommitment: 0,
        highCommitment: 0
      },
      achievements: {
        totalAchievements: 0,
        averageAchievementsPerActivity: 0,
        topAchievementTypes: []
      }
    };
  }

  // Overview statistics
  const totalActivities = activities.length;
  const totalHours = activities.reduce(
    (sum, activity) =>
      sum + (activity.hours_per_week || 0) * (activity.weeks_per_year || 0),
    0
  );
  const averageHoursPerActivity =
    totalActivities > 0
      ? Math.round((totalHours / totalActivities) * 10) / 10
      : 0;
  const totalStudentsWithActivities = new Set(
    activities.map((a) => a.student_id)
  ).size;

  // Categories analysis
  const categoryStats: {
    [category: string]: {
      count: number;
      totalHours: number;
      averageHours: number;
      percentage: number;
    };
  } = {};

  activities.forEach((activity) => {
    const category = activity.category || 'Other';
    const hours =
      (activity.hours_per_week || 0) * (activity.weeks_per_year || 0);

    if (!categoryStats[category]) {
      categoryStats[category] = {
        count: 0,
        totalHours: 0,
        averageHours: 0,
        percentage: 0
      };
    }

    categoryStats[category].count++;
    categoryStats[category].totalHours += hours;
  });

  // Calculate averages and percentages for categories
  Object.keys(categoryStats).forEach((category) => {
    const stats = categoryStats[category];
    stats.averageHours =
      stats.count > 0
        ? Math.round((stats.totalHours / stats.count) * 10) / 10
        : 0;
    stats.percentage =
      totalActivities > 0
        ? Math.round((stats.count / totalActivities) * 100)
        : 0;
  });

  // Leadership analysis
  const leadershipActivities = activities.filter(
    (activity) =>
      activity.position &&
      (activity.position.toLowerCase().includes('president') ||
        activity.position.toLowerCase().includes('captain') ||
        activity.position.toLowerCase().includes('leader') ||
        activity.position.toLowerCase().includes('chair') ||
        activity.position.toLowerCase().includes('head'))
  );

  const totalLeadershipRoles = leadershipActivities.length;
  const leadershipPercentage =
    totalActivities > 0
      ? Math.round((totalLeadershipRoles / totalActivities) * 100)
      : 0;

  // Top leadership positions
  const positionCounts = new Map<string, number>();
  leadershipActivities.forEach((activity) => {
    const position = activity.position || 'Unknown';
    positionCounts.set(position, (positionCounts.get(position) || 0) + 1);
  });

  const topLeadershipPositions = Array.from(positionCounts.entries())
    .map(([position, count]) => ({ position, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);

  // Time commitment analysis
  let lowCommitment = 0;
  let mediumCommitment = 0;
  let highCommitment = 0;

  activities.forEach((activity) => {
    const hoursPerWeek = activity.hours_per_week || 0;
    if (hoursPerWeek < 5) {
      lowCommitment++;
    } else if (hoursPerWeek <= 15) {
      mediumCommitment++;
    } else {
      highCommitment++;
    }
  });

  // Achievements analysis
  const allAchievements = activities.flatMap(
    (activity) => activity.achievements || []
  );
  const totalAchievements = allAchievements.length;
  const averageAchievementsPerActivity =
    totalActivities > 0
      ? Math.round((totalAchievements / totalActivities) * 10) / 10
      : 0;

  // Top achievement types (simplified categorization)
  const achievementTypes = new Map<string, number>();
  allAchievements.forEach((achievement) => {
    const type = categorizeAchievement(achievement);
    achievementTypes.set(type, (achievementTypes.get(type) || 0) + 1);
  });

  const topAchievementTypes = Array.from(achievementTypes.entries())
    .map(([type, count]) => ({ type, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);

  return {
    overview: {
      totalActivities,
      totalHours,
      averageHoursPerActivity,
      totalStudentsWithActivities
    },
    categories: categoryStats,
    leadership: {
      totalLeadershipRoles,
      leadershipPercentage,
      topLeadershipPositions
    },
    timeCommitment: {
      lowCommitment,
      mediumCommitment,
      highCommitment
    },
    achievements: {
      totalAchievements,
      averageAchievementsPerActivity,
      topAchievementTypes
    }
  };
}

function categorizeAchievement(achievement: string): string {
  const lower = achievement.toLowerCase();

  if (
    lower.includes('award') ||
    lower.includes('prize') ||
    lower.includes('honor')
  ) {
    return 'Awards & Honors';
  } else if (
    lower.includes('champion') ||
    lower.includes('winner') ||
    lower.includes('first')
  ) {
    return 'Competition Wins';
  } else if (lower.includes('scholarship') || lower.includes('grant')) {
    return 'Scholarships';
  } else if (lower.includes('publish') || lower.includes('research')) {
    return 'Publications & Research';
  } else if (lower.includes('volunteer') || lower.includes('service')) {
    return 'Community Service';
  } else {
    return 'Other Achievements';
  }
}
