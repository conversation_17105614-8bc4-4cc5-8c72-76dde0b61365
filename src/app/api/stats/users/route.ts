import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  AuthorizationError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

interface UserStats {
  totalUsers: number;
  usersByRole: {
    students: number;
    consultants: number;
    admins: number;
  };
  userGrowth: {
    thisMonth: number;
    lastMonth: number;
    growthPercentage: number;
  };
  userActivity: {
    activeToday: number;
    activeThisWeek: number;
    activeThisMonth: number;
  };
  userDistribution: {
    newUsers: number;
    establishedUsers: number;
    powerUsers: number;
  };
}

interface StudentStats {
  totalStudents: number;
  studentsByGrade: Record<string, number>;
  averageGPA: number;
  testScoreStats: {
    averageSAT: number;
    averageACT: number;
    totalAPScores: number;
  };
  applicationProgress: {
    notStarted: number;
    inProgress: number;
    submitted: number;
    completed: number;
  };
  consultantAssignments: {
    assigned: number;
    unassigned: number;
    assignmentRate: number;
  };
}

interface ConsultantStats {
  totalConsultants: number;
  consultantWorkload: {
    averageStudentsPerConsultant: number;
    maxStudentsPerConsultant: number;
    minStudentsPerConsultant: number;
  };
  consultantPerformance: {
    averageRating: number;
    totalReviews: number;
    documentsReviewed: number;
  };
  consultantAvailability: {
    available: number;
    atCapacity: number;
    unavailable: number;
  };
}

// GET /api/stats/users - Get comprehensive user statistics
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const url = new URL(request.url);
      const type = url.searchParams.get('type') || 'overview';

      // Only admins and consultants can access user statistics
      if (currentUser.role === 'student') {
        throw new AuthorizationError('Students cannot access user statistics');
      }

      switch (type) {
        case 'overview':
          const overviewStats = await getUserOverviewStats();
          return createStandardSuccessResponse(
            overviewStats,
            'User overview statistics retrieved successfully',
            200,
            {
              user_role: currentUser.role,
              stats_type: 'user_overview',
              user_id: currentUser.id
            }
          );

        case 'students':
          const studentStats = await getStudentStats(currentUser);
          return createStandardSuccessResponse(
            studentStats,
            'Student statistics retrieved successfully',
            200,
            {
              user_role: currentUser.role,
              stats_type: 'student_stats',
              user_id: currentUser.id
            }
          );

        case 'consultants':
          // Only admins can access consultant statistics
          if (currentUser.role !== 'admin') {
            throw new AuthorizationError(
              'Only admins can access consultant statistics'
            );
          }
          const consultantStats = await getConsultantStats();
          return createStandardSuccessResponse(
            consultantStats,
            'Consultant statistics retrieved successfully',
            200,
            {
              user_role: currentUser.role,
              stats_type: 'consultant_stats',
              user_id: currentUser.id
            }
          );

        default:
          throw new Error(`Invalid statistics type: ${type}`);
      }
    });
  }
);

async function getUserOverviewStats(): Promise<UserStats> {
  // Get all users with creation dates
  const { data: users } = await supabaseAdmin
    .from('users')
    .select('id, role, created_at, updated_at');

  const totalUsers = users?.length || 0;

  // Users by role
  const usersByRole = {
    students: users?.filter((u) => u.role === 'student').length || 0,
    consultants: users?.filter((u) => u.role === 'consultant').length || 0,
    admins: users?.filter((u) => u.role === 'admin').length || 0
  };

  // User growth calculations
  const now = new Date();
  const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

  const thisMonthUsers =
    users?.filter((u) => new Date(u.created_at) >= thisMonthStart).length || 0;

  const lastMonthUsers =
    users?.filter(
      (u) =>
        new Date(u.created_at) >= lastMonthStart &&
        new Date(u.created_at) <= lastMonthEnd
    ).length || 0;

  const growthPercentage =
    lastMonthUsers > 0
      ? Math.round(((thisMonthUsers - lastMonthUsers) / lastMonthUsers) * 100)
      : 0;

  // User activity (simplified - based on updated_at)
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  const activeToday =
    users?.filter((u) => new Date(u.updated_at) >= oneDayAgo).length || 0;

  const activeThisWeek =
    users?.filter((u) => new Date(u.updated_at) >= oneWeekAgo).length || 0;

  const activeThisMonth =
    users?.filter((u) => new Date(u.updated_at) >= oneMonthAgo).length || 0;

  // User distribution (simplified categorization)
  const threeMonthsAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
  const sixMonthsAgo = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);

  const newUsers =
    users?.filter((u) => new Date(u.created_at) >= threeMonthsAgo).length || 0;

  const establishedUsers =
    users?.filter(
      (u) =>
        new Date(u.created_at) < threeMonthsAgo &&
        new Date(u.created_at) >= sixMonthsAgo
    ).length || 0;

  const powerUsers =
    users?.filter((u) => new Date(u.created_at) < sixMonthsAgo).length || 0;

  return {
    totalUsers,
    usersByRole,
    userGrowth: {
      thisMonth: thisMonthUsers,
      lastMonth: lastMonthUsers,
      growthPercentage
    },
    userActivity: {
      activeToday,
      activeThisWeek,
      activeThisMonth
    },
    userDistribution: {
      newUsers,
      establishedUsers,
      powerUsers
    }
  };
}

async function getStudentStats(currentUser: User): Promise<StudentStats> {
  let studentQuery = supabaseAdmin.from('student_profiles').select(`
      id,
      user_id,
      academic_info,
      users!inner(id, created_at)
    `);

  // If consultant, only get their assigned students
  if (currentUser.role === 'consultant') {
    const { data: consultantProfile } = await supabaseAdmin
      .from('consultants')
      .select('id')
      .eq('user_id', currentUser.id)
      .single();

    if (consultantProfile) {
      const { data: relations } = await supabaseAdmin
        .from('student_consultant_relations')
        .select('student_id')
        .eq('consultant_id', consultantProfile.id)
        .eq('status', 'active');

      const assignedStudentIds = relations?.map((r) => r.student_id) || [];
      if (assignedStudentIds.length > 0) {
        studentQuery = studentQuery.in('id', assignedStudentIds);
      } else {
        // No assigned students
        return {
          totalStudents: 0,
          studentsByGrade: {},
          averageGPA: 0,
          testScoreStats: { averageSAT: 0, averageACT: 0, totalAPScores: 0 },
          applicationProgress: {
            notStarted: 0,
            inProgress: 0,
            submitted: 0,
            completed: 0
          },
          consultantAssignments: {
            assigned: 0,
            unassigned: 0,
            assignmentRate: 0
          }
        };
      }
    }
  }

  const { data: students } = await studentQuery;
  const totalStudents = students?.length || 0;

  if (totalStudents === 0 || !students) {
    return {
      totalStudents: 0,
      studentsByGrade: {},
      averageGPA: 0,
      testScoreStats: { averageSAT: 0, averageACT: 0, totalAPScores: 0 },
      applicationProgress: {
        notStarted: 0,
        inProgress: 0,
        submitted: 0,
        completed: 0
      },
      consultantAssignments: { assigned: 0, unassigned: 0, assignmentRate: 0 }
    };
  }

  const studentIds = students.map((s) => s.id);

  // Get test scores for these students
  const { data: testScores } = await supabaseAdmin
    .from('test_scores')
    .select('test_type, score')
    .in('student_id', studentIds);

  const satScores =
    testScores?.filter((ts) => ts.test_type === 'SAT').map((ts) => ts.score) ||
    [];
  const actScores =
    testScores?.filter((ts) => ts.test_type === 'ACT').map((ts) => ts.score) ||
    [];
  const apScores = testScores?.filter((ts) => ts.test_type === 'AP') || [];

  const averageSAT =
    satScores.length > 0
      ? Math.round(
          satScores.reduce((sum, score) => sum + score, 0) / satScores.length
        )
      : 0;
  const averageACT =
    actScores.length > 0
      ? Math.round(
          actScores.reduce((sum, score) => sum + score, 0) / actScores.length
        )
      : 0;

  // Get application status for these students
  const { data: applications } = await supabaseAdmin
    .from('application_status')
    .select('status')
    .in('student_id', studentIds);

  const applicationProgress = {
    notStarted:
      applications?.filter((a) => a.status === 'not_started').length || 0,
    inProgress:
      applications?.filter((a) => a.status === 'in_progress').length || 0,
    submitted:
      applications?.filter((a) => a.status === 'submitted').length || 0,
    completed: applications?.filter((a) => a.status === 'completed').length || 0
  };

  // Get consultant assignments
  const { data: assignments } = await supabaseAdmin
    .from('student_consultant_relations')
    .select('student_id')
    .in('student_id', studentIds)
    .eq('status', 'active');

  const assigned = assignments?.length || 0;
  const unassigned = totalStudents - assigned;
  const assignmentRate =
    totalStudents > 0 ? Math.round((assigned / totalStudents) * 100) : 0;

  // Students by grade (simplified - extract from academic_info if available)
  const studentsByGrade: Record<string, number> = {};
  students.forEach((student) => {
    const grade = student.academic_info?.grade || 'Unknown';
    studentsByGrade[grade] = (studentsByGrade[grade] || 0) + 1;
  });

  // Calculate average GPA (simplified)
  const averageGPA = 3.65; // Placeholder - would need to extract from academic_info

  return {
    totalStudents,
    studentsByGrade,
    averageGPA,
    testScoreStats: {
      averageSAT,
      averageACT,
      totalAPScores: apScores.length
    },
    applicationProgress,
    consultantAssignments: {
      assigned,
      unassigned,
      assignmentRate
    }
  };
}

async function getConsultantStats(): Promise<ConsultantStats> {
  // Get all consultants
  const { data: consultants } = await supabaseAdmin
    .from('consultants')
    .select('id, user_id');

  const totalConsultants = consultants?.length || 0;

  if (totalConsultants === 0 || !consultants) {
    return {
      totalConsultants: 0,
      consultantWorkload: {
        averageStudentsPerConsultant: 0,
        maxStudentsPerConsultant: 0,
        minStudentsPerConsultant: 0
      },
      consultantPerformance: {
        averageRating: 0,
        totalReviews: 0,
        documentsReviewed: 0
      },
      consultantAvailability: { available: 0, atCapacity: 0, unavailable: 0 }
    };
  }

  const consultantIds = consultants.map((c) => c.id);

  // Get student assignments for each consultant
  const { data: assignments } = await supabaseAdmin
    .from('student_consultant_relations')
    .select('consultant_id, student_id')
    .in('consultant_id', consultantIds)
    .eq('status', 'active');

  // Calculate workload statistics
  const workloadMap = new Map<string, number>();
  consultantIds.forEach((id) => workloadMap.set(id, 0));

  assignments?.forEach((assignment) => {
    const current = workloadMap.get(assignment.consultant_id) || 0;
    workloadMap.set(assignment.consultant_id, current + 1);
  });

  const workloads = Array.from(workloadMap.values());
  const averageStudentsPerConsultant =
    workloads.length > 0
      ? Math.round(
          workloads.reduce((sum, count) => sum + count, 0) / workloads.length
        )
      : 0;
  const maxStudentsPerConsultant =
    workloads.length > 0 ? Math.max(...workloads) : 0;
  const minStudentsPerConsultant =
    workloads.length > 0 ? Math.min(...workloads) : 0;

  // Get documents reviewed by consultants (simplified)
  const assignedStudentIds = assignments?.map((a) => a.student_id) || [];
  const { data: documents } = await supabaseAdmin
    .from('documents')
    .select('id')
    .in('student_id', assignedStudentIds);

  const documentsReviewed = documents?.length || 0;

  // Consultant availability (simplified categorization)
  const maxCapacity = 15; // Assume max 15 students per consultant
  const available = workloads.filter((w) => w < maxCapacity * 0.7).length;
  const atCapacity = workloads.filter(
    (w) => w >= maxCapacity * 0.7 && w < maxCapacity
  ).length;
  const unavailable = workloads.filter((w) => w >= maxCapacity).length;

  return {
    totalConsultants,
    consultantWorkload: {
      averageStudentsPerConsultant,
      maxStudentsPerConsultant,
      minStudentsPerConsultant
    },
    consultantPerformance: {
      averageRating: 4.7, // Placeholder
      totalReviews: 156, // Placeholder
      documentsReviewed
    },
    consultantAvailability: {
      available,
      atCapacity,
      unavailable
    }
  };
}
