import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  withStandard<PERSON>ole,
  createStandardSuccessResponse,
  parseRequestBody,
  validateRequiredFields,
  handleAsyncOperation,
  AuthorizationError
} from '@/lib/api-utils';
import { AdvancedPermissionManager } from '@/lib/permission-manager';
import { supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

// GET /api/permissions - Get permission summary (Admin only)
export const GET = withStandardRole(
  ['admin'],
  async (_request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const summary = await AdvancedPermissionManager.getPermissionSummary();

      return createStandardSuccessResponse(
        summary,
        'Permission summary retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'permissions_summary',
          admin_operation: true
        }
      );
    }, 'Failed to retrieve permission summary');
  }
);

// POST /api/permissions - Handle permission operations
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        action:
          | 'audit'
          | 'repair'
          | 'consultant_change'
          | 'consultant_deactivation';
        student_id?: string;
        consultant_id?: string;
        old_consultant_id?: string;
        new_consultant_id?: string;
      }>(request);

      validateRequiredFields(body, ['action']);

      switch (body.action) {
        case 'audit':
          if (!body.student_id) {
            throw new Error('student_id is required for audit action');
          }

          // Check permissions
          if (currentUser.role === 'student') {
            // Students can only audit their own permissions
            const { data: studentProfile } = await supabaseAdmin
              .from('student_profiles')
              .select('user_id')
              .eq('id', body.student_id)
              .single();

            if (!studentProfile || studentProfile.user_id !== currentUser.id) {
              throw new AuthorizationError(
                'You can only audit your own permissions'
              );
            }
          } else if (currentUser.role === 'consultant') {
            // Consultants can audit permissions for their assigned students
            const { data: consultant } = await supabaseAdmin
              .from('consultants')
              .select('id')
              .eq('user_id', currentUser.id)
              .single();

            if (consultant) {
              const { data: relation } = await supabaseAdmin
                .from('student_consultant_relations')
                .select('id')
                .eq('student_id', body.student_id)
                .eq('consultant_id', consultant.id)
                .eq('status', 'active')
                .single();

              if (!relation) {
                throw new AuthorizationError(
                  'You can only audit permissions for your assigned students'
                );
              }
            } else {
              throw new AuthorizationError('Consultant profile not found');
            }
          }
          // Admins can audit any student's permissions

          const auditResult =
            await AdvancedPermissionManager.auditStudentPermissions(
              body.student_id
            );
          return createStandardSuccessResponse(
            auditResult,
            'Permission audit completed successfully',
            200,
            {
              user_role: currentUser.role,
              operation_type: 'audit',
              resource_type: 'student_permissions',
              student_id: body.student_id,
              action: body.action
            }
          );

        case 'repair':
          if (!body.student_id) {
            throw new Error('student_id is required for repair action');
          }

          // Only admins and the student themselves can repair permissions
          if (currentUser.role === 'student') {
            const { data: studentProfile } = await supabaseAdmin
              .from('student_profiles')
              .select('user_id')
              .eq('id', body.student_id)
              .single();

            if (!studentProfile || studentProfile.user_id !== currentUser.id) {
              throw new AuthorizationError(
                'You can only repair your own permissions'
              );
            }
          } else if (currentUser.role === 'consultant') {
            throw new AuthorizationError(
              'Consultants cannot repair permissions'
            );
          }

          await AdvancedPermissionManager.repairStudentPermissions(
            body.student_id
          );
          return createStandardSuccessResponse(
            null,
            'Permissions repaired successfully',
            200,
            {
              user_role: currentUser.role,
              operation_type: 'repair',
              resource_type: 'student_permissions',
              student_id: body.student_id,
              action: body.action
            }
          );

        case 'consultant_change':
          // Only admins can change consultant assignments
          if (currentUser.role !== 'admin') {
            throw new AuthorizationError(
              'Only admins can change consultant assignments'
            );
          }

          if (!body.student_id) {
            throw new Error(
              'student_id is required for consultant_change action'
            );
          }

          await AdvancedPermissionManager.handleConsultantAssignmentChange(
            body.student_id,
            body.old_consultant_id,
            body.new_consultant_id
          );

          return createStandardSuccessResponse(
            null,
            'Consultant assignment permissions updated successfully',
            200,
            {
              user_role: currentUser.role,
              operation_type: 'consultant_change',
              resource_type: 'consultant_assignment',
              student_id: body.student_id,
              old_consultant_id: body.old_consultant_id,
              new_consultant_id: body.new_consultant_id,
              action: body.action,
              admin_operation: true
            }
          );

        case 'consultant_deactivation':
          // Only admins can deactivate consultants
          if (currentUser.role !== 'admin') {
            throw new AuthorizationError(
              'Only admins can deactivate consultants'
            );
          }

          if (!body.consultant_id) {
            throw new Error(
              'consultant_id is required for consultant_deactivation action'
            );
          }

          await AdvancedPermissionManager.handleConsultantDeactivation(
            body.consultant_id
          );
          return createStandardSuccessResponse(
            null,
            'Consultant deactivation permissions updated successfully',
            200,
            {
              user_role: currentUser.role,
              operation_type: 'consultant_deactivation',
              resource_type: 'consultant_permissions',
              consultant_id: body.consultant_id,
              action: body.action,
              admin_operation: true
            }
          );

        default:
          throw new Error(`Unknown action: ${body.action}`);
      }
    }, 'Failed to handle permission operation');
  }
);
