import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation
} from '@/lib/api-utils';
import type { User } from '@/types/application';

// Test endpoint to validate standardized response format
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const url = new URL(request.url);
      const testType = url.searchParams.get('type') || 'basic';

      // Test different response scenarios
      switch (testType) {
        case 'basic':
          return createStandardSuccessResponse(
            { message: 'Basic test successful' },
            'Test completed successfully',
            200,
            {
              user_role: currentUser.role,
              operation_type: 'test',
              resource_type: 'response_format',
              test_type: 'basic'
            }
          );

        case 'with_metadata':
          return createStandardSuccessResponse(
            {
              test_data: 'Sample data',
              items: [1, 2, 3],
              nested: { key: 'value' }
            },
            'Metadata test successful',
            200,
            {
              user_role: currentUser.role,
              operation_type: 'test',
              resource_type: 'response_format',
              test_type: 'with_metadata',
              data_structure: 'complex',
              item_count: 3,
              has_nested_data: true
            }
          );

        case 'empty_data':
          return createStandardSuccessResponse(
            null,
            'Empty data test successful',
            200,
            {
              user_role: currentUser.role,
              operation_type: 'test',
              resource_type: 'response_format',
              test_type: 'empty_data'
            }
          );

        case 'array_data':
          return createStandardSuccessResponse(
            [
              { id: 1, name: 'Item 1' },
              { id: 2, name: 'Item 2' },
              { id: 3, name: 'Item 3' }
            ],
            'Array data test successful',
            200,
            {
              user_role: currentUser.role,
              operation_type: 'test',
              resource_type: 'response_format',
              test_type: 'array_data',
              item_count: 3
            }
          );

        case 'error_simulation':
          throw new Error('Simulated error for testing error response format');

        default:
          throw new Error(`Unknown test type: ${testType}`);
      }
    }, 'Failed to execute response format test');
  }
);

// POST endpoint to test request/response cycle
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await request.json();

      return createStandardSuccessResponse(
        {
          received_data: body,
          processed_at: new Date().toISOString(),
          user_info: {
            id: currentUser.id,
            role: currentUser.role
          }
        },
        'POST test completed successfully',
        201,
        {
          user_role: currentUser.role,
          operation_type: 'create',
          resource_type: 'test_data',
          request_body_size: JSON.stringify(body).length,
          has_request_data: Object.keys(body).length > 0
        }
      );
    }, 'Failed to process POST test request');
  }
);
