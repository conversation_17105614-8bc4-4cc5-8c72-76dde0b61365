import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing Google APIs with Domain-Wide Delegation...');

    // Test 1: Without subject (service account only)
    console.log('📝 Test 1: Service Account Only (no subject)...');
    const jwtClient1 = new google.auth.JWT({
      email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
      key: process.env.GOOGLE_PRIVATE_KEY!.replace(/\\n/g, '\n'),
      scopes: [
        'https://www.googleapis.com/auth/documents',
        'https://www.googleapis.com/auth/drive'
      ]
    });

    await jwtClient1.authorize();
    const docs1 = google.docs({ version: 'v1', auth: jwtClient1 });

    try {
      const response1 = await docs1.documents.create({
        requestBody: { title: 'Test Without Subject' }
      });
      console.log('✅ Test 1 SUCCESS: Document created without subject');

      // Clean up
      const drive1 = google.drive({ version: 'v3', auth: jwtClient1 });
      await drive1.files.delete({ fileId: response1.data.documentId! });

      return NextResponse.json({
        success: true,
        message: 'Google Docs API working without subject impersonation',
        test_type: 'service_account_only',
        document_id: response1.data.documentId
      });
    } catch (error1: any) {
      console.log('❌ Test 1 FAILED:', error1.message);

      // Test 2: With subject (domain-wide delegation)
      console.log('📝 Test 2: With Subject Impersonation...');

      // Try with a common admin email pattern
      const possibleAdminEmails = [
        '<EMAIL>',
        '<EMAIL>',
        process.env.GOOGLE_ADMIN_EMAIL,
        '<EMAIL>' // Your email from the testing
      ].filter(Boolean);

      for (const adminEmail of possibleAdminEmails) {
        try {
          console.log(`🔄 Trying with subject: ${adminEmail}`);

          const jwtClient2 = new google.auth.JWT({
            email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
            key: process.env.GOOGLE_PRIVATE_KEY!.replace(/\\n/g, '\n'),
            scopes: [
              'https://www.googleapis.com/auth/documents',
              'https://www.googleapis.com/auth/drive'
            ],
            subject: adminEmail // This is key for domain-wide delegation
          });

          await jwtClient2.authorize();
          const docs2 = google.docs({ version: 'v1', auth: jwtClient2 });

          const response2 = await docs2.documents.create({
            requestBody: { title: `Test With Subject ${adminEmail}` }
          });

          console.log(`✅ Test 2 SUCCESS with ${adminEmail}: Document created`);

          // Clean up
          const drive2 = google.drive({ version: 'v3', auth: jwtClient2 });
          await drive2.files.delete({ fileId: response2.data.documentId! });

          return NextResponse.json({
            success: true,
            message: 'Google Docs API working with domain-wide delegation',
            test_type: 'domain_wide_delegation',
            subject_email: adminEmail,
            document_id: response2.data.documentId
          });
        } catch (subjectError: any) {
          console.log(
            `❌ Failed with subject ${adminEmail}:`,
            subjectError.message
          );
          continue;
        }
      }

      // If all tests failed
      return NextResponse.json(
        {
          success: false,
          error: 'All authentication methods failed',
          details: {
            service_account_error: error1.message,
            service_account_code: error1.code,
            tested_subjects: possibleAdminEmails,
            recommendations: [
              'Verify Google Docs API is enabled in Google Cloud Console',
              'Check service account has proper IAM roles',
              'Ensure domain-wide delegation is configured in Google Workspace Admin Console',
              'Verify the correct OAuth scopes are authorized',
              'Check if you need to specify a subject email for domain-wide delegation',
              'Wait 5-10 minutes for domain-wide delegation changes to propagate'
            ]
          }
        },
        { status: 403 }
      );
    }
  } catch (error: any) {
    console.error('❌ Test failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Authentication test failed',
        details: {
          message: error.message,
          code: error.code,
          status: error.status
        }
      },
      { status: 500 }
    );
  }
}
