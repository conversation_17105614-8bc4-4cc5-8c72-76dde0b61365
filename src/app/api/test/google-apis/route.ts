import { NextRequest } from 'next/server';
import {
  withAuth,
  createSuccessResponse,
  createErrorResponse
} from '@/lib/api-utils';
import type { User } from '@/types/application';

// GET /api/test/google-apis - Test Google APIs connectivity
export const GET = withAuth(async (request: NextRequest, currentUser: User) => {
  try {
    const testResults: any = {
      environment_variables: {
        GOOGLE_SERVICE_ACCOUNT_EMAIL:
          !!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        GOOGLE_PRIVATE_KEY: !!process.env.GOOGLE_PRIVATE_KEY,
        GOOGLE_PROJECT_ID: !!process.env.GOOGLE_PROJECT_ID
      },
      test_timestamp: new Date().toISOString()
    };

    // Test Google APIs import
    try {
      const { GoogleDriveService } = await import('@/lib/google-apis');
      testResults.google_apis_import = 'success';

      // Test basic Google Drive functionality
      try {
        const mainFolderId = await GoogleDriveService.getOrCreateMainFolder();
        testResults.google_drive_test = {
          status: 'success',
          main_folder_id: mainFolderId
        };
      } catch (driveError) {
        testResults.google_drive_test = {
          status: 'error',
          error:
            driveError instanceof Error ? driveError.message : 'Unknown error'
        };
      }

      // Test templates folder
      try {
        const templatesFolderId =
          await GoogleDriveService.getOrCreateTemplatesFolder();
        testResults.templates_folder_test = {
          status: 'success',
          templates_folder_id: templatesFolderId
        };
      } catch (templatesError) {
        testResults.templates_folder_test = {
          status: 'error',
          error:
            templatesError instanceof Error
              ? templatesError.message
              : 'Unknown error'
        };
      }

      // Test getting templates
      try {
        const templates = await GoogleDriveService.getTemplates();
        testResults.get_templates_test = {
          status: 'success',
          templates_count: templates.length,
          templates: templates.slice(0, 3) // First 3 templates for testing
        };
      } catch (getTemplatesError) {
        testResults.get_templates_test = {
          status: 'error',
          error:
            getTemplatesError instanceof Error
              ? getTemplatesError.message
              : 'Unknown error'
        };
      }
    } catch (importError) {
      testResults.google_apis_import = {
        status: 'error',
        error:
          importError instanceof Error ? importError.message : 'Unknown error'
      };
    }

    return createSuccessResponse(testResults, 'Google APIs test completed');
  } catch (error) {
    console.error('Google APIs test error:', error);
    return createErrorResponse(
      `Google APIs test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
});
