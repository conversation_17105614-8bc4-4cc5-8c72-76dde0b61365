import { NextRequest } from 'next/server';
import {
  withAuth,
  createSuccessResponse,
  createErrorResponse
} from '@/lib/api-utils';
import type { User } from '@/types/application';

// GET /api/test/operations - Test document operations API functionality
export const GET = withAuth(async (request: NextRequest, currentUser: User) => {
  try {
    // Test a simple sync operation
    const testPayload = {
      operation: 'sync_content',
      document_ids: [] // Empty array for testing
    };

    const response = await fetch(
      `${request.nextUrl.origin}/api/documents/operations`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: request.headers.get('Authorization') || '',
          Cookie: request.headers.get('Cookie') || ''
        },
        body: JSON.stringify(testPayload)
      }
    );

    const result = await response.json();

    return createSuccessResponse(
      {
        operations_api_status: response.ok ? 'working' : 'error',
        operations_api_response: result,
        status_code: response.status,
        test_payload: testPayload,
        test_timestamp: new Date().toISOString()
      },
      'Document operations API test completed'
    );
  } catch (error) {
    console.error('Document operations API test error:', error);
    return createErrorResponse(
      `Document operations API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
});
