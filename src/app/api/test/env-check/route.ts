import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  return NextResponse.json({
    environment_variables: {
      GOOGLE_SERVICE_ACCOUNT_EMAIL:
        process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || '[MISSING]',
      GOOGLE_PROJECT_ID: process.env.GOOGLE_PROJECT_ID || '[MISSING]',
      GOOGLE_PRIVATE_KEY_LENGTH: process.env.GOOGLE_PRIVATE_KEY?.length || 0,
      GOOGLE_ADMIN_EMAIL: process.env.GOOGLE_ADMIN_EMAIL || '[MISSING]',
      NODE_ENV: process.env.NODE_ENV
    }
  });
}
