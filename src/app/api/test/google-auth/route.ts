import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';
import { log } from '@/lib/logger';

// Test Google APIs authentication
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing Google APIs Authentication...');

    // Check environment variables
    const requiredEnvVars = {
      GOOGLE_SERVICE_ACCOUNT_EMAIL: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      GOOGLE_PRIVATE_KEY: process.env.GOOGLE_PRIVATE_KEY
        ? '[PRESENT]'
        : '[MISSING]',
      GOOGLE_PROJECT_ID: process.env.GOOGLE_PROJECT_ID
    };

    console.log('📋 Environment Variables:', requiredEnvVars);

    // Check for missing environment variables
    const missingVars = Object.entries(requiredEnvVars)
      .filter(([key, value]) => !value || value === '[MISSING]')
      .map(([key]) => key);

    if (missingVars.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing environment variables',
          missing_variables: missingVars,
          environment_check: requiredEnvVars
        },
        { status: 500 }
      );
    }

    // Create JWT client
    const GOOGLE_PRIVATE_KEY = process.env.GOOGLE_PRIVATE_KEY!.replace(
      /\\n/g,
      '\n'
    );

    console.log('🔑 Creating JWT client...');
    const jwtClient = new google.auth.JWT({
      email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
      key: GOOGLE_PRIVATE_KEY,
      scopes: [
        'https://www.googleapis.com/auth/drive',
        'https://www.googleapis.com/auth/documents'
      ],
      subject: process.env.GOOGLE_ADMIN_EMAIL // 新增这一行
    });

    // Test authentication
    console.log('🔐 Testing JWT authentication...');
    await jwtClient.authorize();
    console.log('✅ JWT authentication successful');

    // Test Google Drive API access
    console.log('📁 Testing Google Drive API access...');
    const drive = google.drive({ version: 'v3', auth: jwtClient });

    // Try to list files (this should work if authentication is successful)
    const driveResponse = await drive.files.list({
      pageSize: 1,
      fields: 'files(id, name)'
    });

    console.log('✅ Google Drive API access successful');

    // Test Google Docs API access
    console.log('📄 Testing Google Docs API access...');
    const docs = google.docs({ version: 'v1', auth: jwtClient });

    // Try to create a test document
    console.log('📝 Attempting to create test document...');
    const docResponse = await docs.documents.create({
      requestBody: {
        title: 'Lighten Counsel - Auth Test Document'
      }
    });

    const documentId = docResponse.data.documentId;
    console.log('✅ Test document created successfully:', documentId);

    // Clean up - delete the test document
    if (documentId) {
      console.log('🗑️ Cleaning up test document...');
      await drive.files.delete({
        fileId: documentId
      });
      console.log('✅ Test document deleted');
    }

    return NextResponse.json({
      success: true,
      message: 'Google APIs authentication test successful',
      details: {
        jwt_auth: 'successful',
        drive_api: 'accessible',
        docs_api: 'accessible',
        test_document_created: true,
        service_account: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        project_id: process.env.GOOGLE_PROJECT_ID
      }
    });
  } catch (error: any) {
    console.error('❌ Google APIs authentication test failed:', error);

    // Provide detailed error information
    const errorDetails = {
      message: error.message,
      code: error.code,
      status: error.status,
      details: error.details,
      stack: error.stack?.split('\n').slice(0, 5) // First 5 lines of stack trace
    };

    return NextResponse.json(
      {
        success: false,
        error: 'Google APIs authentication test failed',
        error_details: errorDetails,
        environment_check: {
          GOOGLE_SERVICE_ACCOUNT_EMAIL:
            process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || '[MISSING]',
          GOOGLE_PRIVATE_KEY: process.env.GOOGLE_PRIVATE_KEY
            ? '[PRESENT]'
            : '[MISSING]',
          GOOGLE_PROJECT_ID: process.env.GOOGLE_PROJECT_ID || '[MISSING]'
        }
      },
      { status: 500 }
    );
  }
}
