import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';

// Detailed Google APIs diagnostic test
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Running Detailed Google APIs Diagnostic...');

    // Environment check
    const envVars = {
      GOOGLE_SERVICE_ACCOUNT_EMAIL: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      GOOGLE_PROJECT_ID: process.env.GOOGLE_PROJECT_ID,
      GOOGLE_PRIVATE_KEY_LENGTH: process.env.GOOGLE_PRIVATE_KEY?.length || 0
    };

    console.log('📋 Environment Variables:', envVars);

    // Create JWT client
    const GOOGLE_PRIVATE_KEY = process.env.GOOGLE_PRIVATE_KEY!.replace(
      /\\n/g,
      '\n'
    );

    const jwtClient = new google.auth.JWT({
      email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
      key: GOOGLE_PRIVATE_KEY,
      scopes: [
        'https://www.googleapis.com/auth/drive',
        'https://www.googleapis.com/auth/documents'
      ]
    });

    // Test 1: JWT Authorization
    console.log('🔐 Test 1: JWT Authorization...');
    await jwtClient.authorize();
    console.log('✅ JWT authorization successful');

    // Test 2: Get access token info
    console.log('🎫 Test 2: Access Token Info...');
    const tokenInfo = await jwtClient.getAccessToken();
    console.log('✅ Access token obtained');

    // Test 3: Google Drive API - List files
    console.log('📁 Test 3: Google Drive API - List Files...');
    const drive = google.drive({ version: 'v3', auth: jwtClient });

    const driveListResponse = await drive.files.list({
      pageSize: 5,
      fields: 'files(id, name, mimeType, createdTime)'
    });

    console.log(
      '✅ Drive files list successful:',
      driveListResponse.data.files?.length || 0,
      'files'
    );

    // Test 4: Google Drive API - Create folder (less restrictive than docs)
    console.log('📂 Test 4: Google Drive API - Create Test Folder...');
    const folderResponse = await drive.files.create({
      requestBody: {
        name: 'Lighten Test Folder - ' + Date.now(),
        mimeType: 'application/vnd.google-apps.folder'
      }
    });

    const folderId = folderResponse.data.id;
    console.log('✅ Test folder created:', folderId);

    // Test 5: Google Docs API - Check quota/limits
    console.log('📄 Test 5: Google Docs API - Service Info...');
    const docs = google.docs({ version: 'v1', auth: jwtClient });

    // Test 6: Try to create document with minimal request
    console.log('📝 Test 6: Google Docs API - Create Document (Minimal)...');
    try {
      const docResponse = await docs.documents.create({
        requestBody: {
          title: 'Test Doc - ' + Date.now()
        }
      });

      const documentId = docResponse.data.documentId;
      console.log('✅ Document created successfully:', documentId);

      // Test 7: Try to get the document we just created
      console.log('📖 Test 7: Get Document Content...');
      const getDocResponse = await docs.documents.get({
        documentId: documentId!
      });
      console.log('✅ Document retrieved successfully');

      // Clean up - delete test document and folder
      console.log('🗑️ Cleaning up test resources...');
      if (documentId) {
        await drive.files.delete({ fileId: documentId });
        console.log('✅ Test document deleted');
      }
      if (folderId) {
        await drive.files.delete({ fileId: folderId });
        console.log('✅ Test folder deleted');
      }

      return NextResponse.json({
        success: true,
        message: 'All Google APIs tests passed successfully',
        test_results: {
          jwt_auth: 'passed',
          access_token: 'obtained',
          drive_list: 'passed',
          drive_create_folder: 'passed',
          docs_create: 'passed',
          docs_get: 'passed',
          cleanup: 'completed'
        },
        service_account_info: {
          email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
          project_id: process.env.GOOGLE_PROJECT_ID,
          scopes: ['drive', 'documents']
        }
      });
    } catch (docsError: any) {
      console.error('❌ Google Docs API Error:', docsError);

      // Clean up folder if it was created
      if (folderId) {
        try {
          await drive.files.delete({ fileId: folderId });
          console.log('✅ Test folder cleaned up after error');
        } catch (cleanupError) {
          console.error('⚠️ Failed to clean up test folder:', cleanupError);
        }
      }

      return NextResponse.json(
        {
          success: false,
          error: 'Google Docs API test failed',
          docs_error: {
            message: docsError.message,
            code: docsError.code,
            status: docsError.status,
            details: docsError.details,
            response_data: docsError.response?.data
          },
          successful_tests: {
            jwt_auth: 'passed',
            access_token: 'obtained',
            drive_list: 'passed',
            drive_create_folder: 'passed'
          },
          service_account_info: {
            email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
            project_id: process.env.GOOGLE_PROJECT_ID
          }
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('❌ Detailed diagnostic test failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Detailed diagnostic test failed',
        error_details: {
          message: error.message,
          code: error.code,
          status: error.status,
          stack: error.stack?.split('\n').slice(0, 10)
        },
        environment_info: {
          GOOGLE_SERVICE_ACCOUNT_EMAIL:
            process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || '[MISSING]',
          GOOGLE_PROJECT_ID: process.env.GOOGLE_PROJECT_ID || '[MISSING]',
          GOOGLE_PRIVATE_KEY_LENGTH: process.env.GOOGLE_PRIVATE_KEY?.length || 0
        }
      },
      { status: 500 }
    );
  }
}
