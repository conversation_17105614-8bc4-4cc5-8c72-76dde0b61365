import { NextRequest, NextResponse } from 'next/server';
import { GoogleDocsService } from '@/lib/google-apis';

// Test the updated GoogleDocsService with authentication fallback
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing Updated GoogleDocsService...');

    // Test document creation using the updated service
    const result = await GoogleDocsService.createDocument(
      'Test Document - ' + Date.now(),
      'Test Student',
      'essay',
      'This is a test document created by the updated Google APIs service.'
    );

    console.log('✅ Document created successfully:', result);

    // Test document content retrieval
    const content = await GoogleDocsService.getDocumentContent(
      result.documentId
    );
    console.log(
      '✅ Document content retrieved:',
      content.substring(0, 100) + '...'
    );

    // Clean up - delete the test document
    // Note: We'll use the Google Drive API to delete it
    const { google } = require('googleapis');
    const GOOGLE_PRIVATE_KEY = process.env.GOOGLE_PRIVATE_KEY!.replace(
      /\\n/g,
      '\n'
    );

    const jwtClient = new google.auth.JWT({
      email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
      key: GOOGLE_PRIVATE_KEY,
      scopes: ['https://www.googleapis.com/auth/drive'],
      subject: process.env.GOOGLE_ADMIN_EMAIL
    });

    await jwtClient.authorize();
    const drive = google.drive({ version: 'v3', auth: jwtClient });

    await drive.files.delete({
      fileId: result.documentId
    });

    console.log('✅ Test document cleaned up');

    return NextResponse.json({
      success: true,
      message: 'GoogleDocsService test completed successfully',
      test_results: {
        document_created: true,
        document_id: result.documentId,
        document_url: result.documentUrl,
        folder_id: result.folderId,
        content_retrieved: true,
        content_length: content.length,
        cleanup_completed: true
      }
    });
  } catch (error: any) {
    console.error('❌ GoogleDocsService test failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'GoogleDocsService test failed',
        error_details: {
          message: error.message,
          stack: error.stack?.split('\n').slice(0, 5)
        },
        recommendations: [
          'Check if Google Docs API is enabled in Google Cloud Console',
          'Verify service account has proper IAM roles (Editor)',
          'Ensure domain-wide delegation is configured in Google Workspace Admin Console',
          'Verify OAuth scopes are authorized: https://www.googleapis.com/auth/documents, https://www.googleapis.com/auth/drive',
          'Check if GOOGLE_ADMIN_EMAIL is set correctly for domain-wide delegation',
          'Wait 5-10 minutes for domain-wide delegation changes to propagate'
        ]
      },
      { status: 500 }
    );
  }
}
