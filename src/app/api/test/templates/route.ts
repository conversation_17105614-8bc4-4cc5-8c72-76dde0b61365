import { NextRequest } from 'next/server';
import {
  withAuth,
  createSuccessResponse,
  createErrorResponse
} from '@/lib/api-utils';
import type { User } from '@/types/application';

// GET /api/test/templates - Test templates API functionality
export const GET = withAuth(async (request: NextRequest, currentUser: User) => {
  try {
    // Test the templates API by calling it internally
    const response = await fetch(`${request.nextUrl.origin}/api/templates`, {
      headers: {
        Authorization: request.headers.get('Authorization') || '',
        Cookie: request.headers.get('Cookie') || ''
      }
    });

    const result = await response.json();

    return createSuccessResponse(
      {
        templates_api_status: response.ok ? 'working' : 'error',
        templates_api_response: result,
        status_code: response.status,
        test_timestamp: new Date().toISOString()
      },
      'Templates API test completed'
    );
  } catch (error) {
    console.error('Templates API test error:', error);
    return createErrorResponse(
      `Templates API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
});
