import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation
} from '@/lib/api-utils';
import type { User } from '@/types/application';

interface EndpointTest {
  endpoint: string;
  method: string;
  status: 'migrated' | 'pending' | 'not_applicable';
  tested: boolean;
  response_format_valid?: boolean;
  error?: string;
}

// Test endpoint to validate migration status of all API endpoints
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const url = new URL(request.url);
      const testMode = url.searchParams.get('mode') || 'summary';

      // Define all endpoints and their migration status
      const endpoints: EndpointTest[] = [
        // ✅ Completed migrations
        {
          endpoint: '/api/users/me',
          method: 'GET',
          status: 'migrated',
          tested: true
        },
        {
          endpoint: '/api/templates',
          method: 'GET',
          status: 'migrated',
          tested: true
        },
        {
          endpoint: '/api/documents',
          method: 'GET',
          status: 'migrated',
          tested: true
        },
        {
          endpoint: '/api/documents',
          method: 'POST',
          status: 'migrated',
          tested: true
        },
        {
          endpoint: '/api/documents/[id]',
          method: 'GET',
          status: 'migrated',
          tested: true
        },
        {
          endpoint: '/api/documents/[id]',
          method: 'PUT',
          status: 'migrated',
          tested: true
        },
        {
          endpoint: '/api/documents/[id]',
          method: 'DELETE',
          status: 'migrated',
          tested: true
        },
        {
          endpoint: '/api/documents/stats',
          method: 'GET',
          status: 'migrated',
          tested: true
        },
        {
          endpoint: '/api/documents/operations',
          method: 'POST',
          status: 'migrated',
          tested: true
        },
        {
          endpoint: '/api/students',
          method: 'GET',
          status: 'migrated',
          tested: true
        },
        {
          endpoint: '/api/students',
          method: 'POST',
          status: 'migrated',
          tested: true
        },
        {
          endpoint: '/api/students/[id]',
          method: 'GET',
          status: 'migrated',
          tested: true
        },
        {
          endpoint: '/api/students/[id]',
          method: 'PUT',
          status: 'migrated',
          tested: true
        },

        // ✅ Recently migrated (this session)
        {
          endpoint: '/api/permissions',
          method: 'GET',
          status: 'migrated',
          tested: false
        },
        {
          endpoint: '/api/permissions',
          method: 'POST',
          status: 'migrated',
          tested: false
        },
        {
          endpoint: '/api/schools',
          method: 'GET',
          status: 'migrated',
          tested: false
        },
        {
          endpoint: '/api/schools',
          method: 'POST',
          status: 'migrated',
          tested: false
        },
        {
          endpoint: '/api/schools/[id]',
          method: 'GET',
          status: 'migrated',
          tested: false
        },
        {
          endpoint: '/api/schools/[id]',
          method: 'PUT',
          status: 'migrated',
          tested: false
        },
        {
          endpoint: '/api/schools/[id]',
          method: 'DELETE',
          status: 'migrated',
          tested: false
        },
        {
          endpoint: '/api/consultants',
          method: 'GET',
          status: 'migrated',
          tested: false
        },
        {
          endpoint: '/api/consultants/[id]',
          method: 'GET',
          status: 'migrated',
          tested: false
        },
        {
          endpoint: '/api/consultants/[id]',
          method: 'PUT',
          status: 'migrated',
          tested: false
        },

        // 🔄 Still pending migration
        {
          endpoint: '/api/users',
          method: 'GET',
          status: 'pending',
          tested: false
        },
        {
          endpoint: '/api/users/[id]',
          method: 'GET',
          status: 'pending',
          tested: false
        },
        {
          endpoint: '/api/users/[id]',
          method: 'PUT',
          status: 'pending',
          tested: false
        },
        {
          endpoint: '/api/users/[id]',
          method: 'DELETE',
          status: 'pending',
          tested: false
        },
        {
          endpoint: '/api/templates/recommendations',
          method: 'GET',
          status: 'pending',
          tested: false
        },
        {
          endpoint: '/api/templates/analytics',
          method: 'GET',
          status: 'pending',
          tested: false
        },
        {
          endpoint: '/api/webhooks/clerk',
          method: 'POST',
          status: 'pending',
          tested: false
        },

        // 🔧 Test endpoints (not applicable for migration)
        {
          endpoint: '/api/test/*',
          method: 'GET',
          status: 'not_applicable',
          tested: false
        }
      ];

      // Calculate migration statistics
      const stats = {
        total_endpoints: endpoints.length,
        migrated: endpoints.filter((e) => e.status === 'migrated').length,
        pending: endpoints.filter((e) => e.status === 'pending').length,
        not_applicable: endpoints.filter((e) => e.status === 'not_applicable')
          .length,
        tested: endpoints.filter((e) => e.tested).length,
        migration_progress: 0,
        testing_progress: 0
      };

      const applicableEndpoints = endpoints.filter(
        (e) => e.status !== 'not_applicable'
      );
      stats.migration_progress = Math.round(
        (stats.migrated / applicableEndpoints.length) * 100
      );
      stats.testing_progress = Math.round(
        (stats.tested / stats.migrated) * 100
      );

      let responseData;
      if (testMode === 'detailed') {
        responseData = {
          statistics: stats,
          endpoints: endpoints,
          migration_summary: {
            high_priority_complete: true,
            medium_priority_complete: true,
            low_priority_remaining: stats.pending
          }
        };
      } else {
        responseData = {
          statistics: stats,
          migration_summary: {
            status:
              stats.migration_progress === 100 ? 'complete' : 'in_progress',
            progress_percentage: stats.migration_progress,
            remaining_endpoints: stats.pending
          }
        };
      }

      return createStandardSuccessResponse(
        responseData,
        'Migration validation completed successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'validation',
          resource_type: 'migration_status',
          test_mode: testMode,
          migration_progress: stats.migration_progress,
          testing_progress: stats.testing_progress,
          validation_timestamp: new Date().toISOString()
        }
      );
    }, 'Failed to validate migration status');
  }
);
