import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation
} from '@/lib/api-utils';
import type { User } from '@/types/application';

interface ValidationResult {
  endpoint: string;
  method: string;
  valid: boolean;
  issues: string[];
  response_structure?: {
    has_success_field: boolean;
    has_timestamp: boolean;
    has_request_id: boolean;
    has_data_or_error: boolean;
    has_meta: boolean;
  };
}

// Validate that a response follows the standardized format
function validateResponseFormat(
  response: any
): ValidationResult['response_structure'] & { issues: string[] } {
  const issues: string[] = [];
  const structure = {
    has_success_field: false,
    has_timestamp: false,
    has_request_id: false,
    has_data_or_error: false,
    has_meta: false
  };

  // Check required fields
  if (typeof response.success === 'boolean') {
    structure.has_success_field = true;
  } else {
    issues.push('Missing or invalid "success" field (must be boolean)');
  }

  if (typeof response.timestamp === 'string') {
    structure.has_timestamp = true;
    // Validate ISO timestamp format
    if (isNaN(Date.parse(response.timestamp))) {
      issues.push('Invalid timestamp format (must be ISO string)');
    }
  } else {
    issues.push('Missing or invalid "timestamp" field (must be ISO string)');
  }

  if (
    typeof response.request_id === 'string' &&
    response.request_id.length > 0
  ) {
    structure.has_request_id = true;
  } else {
    issues.push(
      'Missing or invalid "request_id" field (must be non-empty string)'
    );
  }

  // Check success/error specific fields
  if (response.success === true) {
    if (response.hasOwnProperty('data')) {
      structure.has_data_or_error = true;
    } else {
      issues.push('Success response missing "data" field');
    }

    if (response.message && typeof response.message !== 'string') {
      issues.push('Optional "message" field must be string if present');
    }
  } else if (response.success === false) {
    if (response.error && typeof response.error === 'object') {
      structure.has_data_or_error = true;
      if (
        !response.error.message ||
        typeof response.error.message !== 'string'
      ) {
        issues.push('Error response missing or invalid "error.message" field');
      }
    } else {
      issues.push('Error response missing or invalid "error" object');
    }
  }

  // Check optional meta field
  if (response.meta !== undefined) {
    if (typeof response.meta === 'object' && response.meta !== null) {
      structure.has_meta = true;
    } else {
      issues.push('Optional "meta" field must be object if present');
    }
  }

  return { ...structure, issues };
}

// Test endpoint to validate response format of specific endpoints
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await request.json();
      const { endpoints_to_test = [] } = body;

      if (!Array.isArray(endpoints_to_test) || endpoints_to_test.length === 0) {
        throw new Error(
          'Please provide an array of endpoints to test in the request body'
        );
      }

      const results: ValidationResult[] = [];
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';

      for (const endpointConfig of endpoints_to_test) {
        const { endpoint, method = 'GET', params = {} } = endpointConfig;

        try {
          // Build URL with parameters
          const url = new URL(endpoint, baseUrl);
          Object.entries(params).forEach(([key, value]) => {
            url.searchParams.append(key, String(value));
          });

          // Make request to the endpoint
          const response = await fetch(url.toString(), {
            method,
            headers: {
              'Content-Type': 'application/json'
              // Note: In a real test environment, you'd need proper authentication headers
            },
            ...(method !== 'GET' && { body: JSON.stringify({}) })
          });

          const responseData = await response.json();
          const validation = validateResponseFormat(responseData);

          results.push({
            endpoint,
            method,
            valid: validation.issues.length === 0,
            issues: validation.issues,
            response_structure: {
              has_success_field: validation.has_success_field,
              has_timestamp: validation.has_timestamp,
              has_request_id: validation.has_request_id,
              has_data_or_error: validation.has_data_or_error,
              has_meta: validation.has_meta
            }
          });
        } catch (error) {
          results.push({
            endpoint,
            method,
            valid: false,
            issues: [
              `Failed to test endpoint: ${error instanceof Error ? error.message : 'Unknown error'}`
            ]
          });
        }
      }

      // Calculate summary statistics
      const summary = {
        total_tested: results.length,
        valid_responses: results.filter((r) => r.valid).length,
        invalid_responses: results.filter((r) => !r.valid).length,
        success_rate: Math.round(
          (results.filter((r) => r.valid).length / results.length) * 100
        )
      };

      return createStandardSuccessResponse(
        {
          summary,
          results,
          validation_timestamp: new Date().toISOString()
        },
        'Endpoint validation completed',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'validation',
          resource_type: 'endpoint_responses',
          endpoints_tested: results.length,
          success_rate: summary.success_rate,
          validation_method: 'automated'
        }
      );
    }, 'Failed to validate endpoint responses');
  }
);
