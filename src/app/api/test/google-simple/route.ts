import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Simple Google Docs Test...');

    // Create JWT client with explicit scopes
    const jwtClient = new google.auth.JWT({
      email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
      key: process.env.GOOGLE_PRIVATE_KEY!.replace(/\\n/g, '\n'),
      scopes: [
        'https://www.googleapis.com/auth/documents',
        'https://www.googleapis.com/auth/drive.file'
      ]
    });

    console.log('🔐 Authorizing JWT client...');
    await jwtClient.authorize();

    console.log('📄 Testing Google Docs API...');
    const docs = google.docs({ version: 'v1', auth: jwtClient });

    // Try the simplest possible document creation
    const response = await docs.documents.create({
      requestBody: {
        title: 'Simple Test'
      }
    });

    console.log('✅ Document created:', response.data.documentId);

    // Clean up
    const drive = google.drive({ version: 'v3', auth: jwtClient });
    await drive.files.delete({
      fileId: response.data.documentId!
    });

    return NextResponse.json({
      success: true,
      message: 'Google Docs API working correctly',
      document_id: response.data.documentId
    });
  } catch (error: any) {
    console.error('❌ Error:', error);

    // Check if it's a specific Google API error
    if (error.code === 403) {
      return NextResponse.json(
        {
          success: false,
          error: 'Permission denied - 403',
          message:
            'This suggests the service account lacks proper permissions or domain-wide delegation is not configured',
          details: {
            error_message: error.message,
            possible_causes: [
              'Google Docs API not enabled in the project',
              'Service account lacks Editor role or docs.documents.create permission',
              'Domain-wide delegation not configured in Google Workspace Admin Console',
              'Service account not authorized with proper scopes in Admin Console',
              'Service account key expired or invalid'
            ],
            next_steps: [
              'Verify Google Docs API is enabled in Google Cloud Console',
              'Check service account has Editor role in IAM',
              'Configure domain-wide delegation in Google Workspace Admin Console',
              'Add required scopes: https://www.googleapis.com/auth/documents, https://www.googleapis.com/auth/drive'
            ]
          }
        },
        { status: 403 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: error.message,
        code: error.code,
        status: error.status
      },
      { status: 500 }
    );
  }
}
