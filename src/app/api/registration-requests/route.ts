import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  withStandardRole,
  createStandardSuccessResponse,
  createStandardPaginatedResponse,
  parsePaginationParams,
  handleAsyncOperation,
  parseRequestBody,
  validateRequiredFields,
  ValidationError,
  NotFoundError
} from '@/lib/api-utils';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

// GET /api/registration-requests - List registration requests (Admin only)
export const GET = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const pagination = parsePaginationParams(request);
      const url = new URL(request.url);
      const status = url.searchParams.get('status');
      const role = url.searchParams.get('role');

      let query = supabase
        .from('registration_requests')
        .select(
          `
          *,
          invitation:invitations!registration_requests_invitation_code_fkey(*),
          approved_by_user:users!registration_requests_approved_by_fkey(id, email, profile_data)
        `,
          { count: 'exact' }
        )
        .range(pagination.offset, pagination.offset + pagination.limit - 1)
        .order('created_at', { ascending: false });

      // Apply filters
      if (status) {
        query = query.eq('status', status);
      }
      if (role) {
        query = query.eq('role_requested', role);
      }

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      return createStandardPaginatedResponse(
        data || [],
        count || 0,
        pagination,
        'Registration requests retrieved successfully',
        {
          total_requests: count || 0,
          filters: { status, role },
          operation_type: 'list'
        }
      );
    }, 'Failed to retrieve registration requests');
  }
);

// POST /api/registration-requests - Create registration request (Public)
export const POST = async (request: NextRequest) => {
  return handleAsyncOperation(async () => {
    const body = await parseRequestBody<{
      email: string;
      invitation_code: string;
      role_requested: 'student' | 'consultant';
      profile_data: {
        first_name: string;
        last_name: string;
        [key: string]: any;
      };
    }>(request);

    validateRequiredFields(body, ['email', 'invitation_code', 'role_requested', 'profile_data']);

    // Validate invitation code first
    const { data: invitation, error: invitationError } = await supabase
      .from('invitations')
      .select('*')
      .eq('code', body.invitation_code.toUpperCase())
      .single();

    if (invitationError || !invitation) {
      throw new ValidationError('Invalid invitation code');
    }

    // Check if invitation is expired
    const now = new Date();
    const expiresAt = new Date(invitation.expires_at);
    if (now > expiresAt) {
      throw new ValidationError('Invitation code has expired');
    }

    // Check if invitation is already used up
    if (invitation.current_uses >= invitation.max_uses) {
      throw new ValidationError('Invitation code has reached maximum usage limit');
    }

    // Check if invitation status is valid
    if (invitation.status !== 'pending') {
      throw new ValidationError('Invitation code is no longer valid');
    }

    // If invitation is for a specific email, validate it
    if (invitation.email && invitation.email.toLowerCase() !== body.email.toLowerCase()) {
      throw new ValidationError('This invitation is for a different email address');
    }

    // Check if role matches invitation
    if (invitation.role !== body.role_requested) {
      throw new ValidationError(`This invitation is for ${invitation.role} role, not ${body.role_requested}`);
    }

    // Check if user already has a registration request
    const { data: existingRequest } = await supabase
      .from('registration_requests')
      .select('id')
      .eq('email', body.email.toLowerCase())
      .eq('status', 'pending_approval')
      .single();

    if (existingRequest) {
      throw new ValidationError('A registration request already exists for this email');
    }

    // Create registration request
    const requestData = {
      email: body.email.toLowerCase(),
      invitation_code: body.invitation_code.toUpperCase(),
      role_requested: body.role_requested,
      profile_data: body.profile_data,
      status: 'pending_approval' as const
    };

    const { data: registrationRequest, error } = await supabaseAdmin
      .from('registration_requests')
      .insert(requestData)
      .select(`
        *,
        invitation:invitations!registration_requests_invitation_code_fkey(*)
      `)
      .single();

    if (error) {
      throw error;
    }

    return createStandardSuccessResponse(
      registrationRequest,
      'Registration request submitted successfully',
      201,
      {
        operation_type: 'create',
        next_step: 'admin_approval'
      }
    );
  }, 'Failed to create registration request');
};
