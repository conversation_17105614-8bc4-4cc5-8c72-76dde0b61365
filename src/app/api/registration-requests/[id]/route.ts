import { NextRequest } from 'next/server';
import {
  withStandardRole,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  validateRequiredFields,
  NotFoundError,
  ValidationError
} from '@/lib/api-utils';
import { supabaseAdmin, upsertUser } from '@/lib/supabase';
import type { User } from '@/types/application';

interface RouteContext {
  params: { id: string };
}

// PATCH /api/registration-requests/[id] - Approve/Reject registration request (Admin only)
export const PATCH = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;
      const body = await parseRequestBody<{
        action: 'approve' | 'reject';
        admin_notes?: string;
        clerk_id?: string; // Required for approval
      }>(request);

      validateRequiredFields(body, ['action']);

      // Get registration request
      const { data: registrationRequest, error: requestError } = await supabaseAdmin
        .from('registration_requests')
        .select(`
          *,
          invitation:invitations!registration_requests_invitation_code_fkey(*)
        `)
        .eq('id', id)
        .single();

      if (requestError || !registrationRequest) {
        throw new NotFoundError('Registration request not found');
      }

      if (registrationRequest.status !== 'pending_approval') {
        throw new ValidationError('Registration request has already been processed');
      }

      if (body.action === 'approve') {
        if (!body.clerk_id) {
          throw new ValidationError('clerk_id is required for approval');
        }

        // Create user in database
        const userData = {
          clerk_id: body.clerk_id,
          email: registrationRequest.email,
          role: registrationRequest.role_requested,
          profile_data: registrationRequest.profile_data
        };

        const user = await upsertUser(userData);

        // Create role-specific profile
        if (registrationRequest.role_requested === 'student') {
          const { error: profileError } = await supabaseAdmin
            .from('student_profiles')
            .upsert(
              {
                user_id: user.id,
                academic_info: {},
                target_schools: [],
                application_status: {}
              },
              {
                onConflict: 'user_id'
              }
            );

          if (profileError) {
            throw profileError;
          }
        } else if (registrationRequest.role_requested === 'consultant') {
          const { error: profileError } = await supabaseAdmin
            .from('consultants')
            .upsert(
              {
                user_id: user.id,
                specialties: [],
                availability: {}
              },
              {
                onConflict: 'user_id'
              }
            );

          if (profileError) {
            throw profileError;
          }
        }

        // Update invitation usage
        const { error: invitationError } = await supabaseAdmin
          .from('invitations')
          .update({
            current_uses: registrationRequest.invitation.current_uses + 1,
            used_at: new Date().toISOString(),
            used_by: user.id,
            status: registrationRequest.invitation.current_uses + 1 >= registrationRequest.invitation.max_uses ? 'used' : 'pending'
          })
          .eq('id', registrationRequest.invitation.id);

        if (invitationError) {
          console.error('Failed to update invitation usage:', invitationError);
          // Don't throw error to avoid blocking user creation
        }
      }

      // Update registration request
      const updateData = {
        status: body.action === 'approve' ? 'approved' : 'rejected',
        admin_notes: body.admin_notes || null,
        approved_by: currentUser.id
      };

      const { data: updatedRequest, error: updateError } = await supabaseAdmin
        .from('registration_requests')
        .update(updateData)
        .eq('id', id)
        .select(`
          *,
          invitation:invitations!registration_requests_invitation_code_fkey(*),
          approved_by_user:users!registration_requests_approved_by_fkey(id, email, profile_data)
        `)
        .single();

      if (updateError) {
        throw updateError;
      }

      return createStandardSuccessResponse(
        updatedRequest,
        `Registration request ${body.action}d successfully`,
        200,
        {
          operation_type: body.action,
          user_created: body.action === 'approve'
        }
      );
    }, 'Failed to process registration request');
  }
);
