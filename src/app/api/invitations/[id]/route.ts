import { NextRequest } from 'next/server';
import {
  withStandardRole,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  NotFoundError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

interface RouteContext {
  params: { id: string };
}

// GET /api/invitations/[id] - Get invitation details (Admin only)
export const GET = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      const { data: invitation, error } = await supabaseAdmin
        .from('invitations')
        .select(`
          *,
          created_by_user:users!invitations_created_by_fkey(id, email, profile_data),
          used_by_user:users!invitations_used_by_fkey(id, email, profile_data)
        `)
        .eq('id', id)
        .single();

      if (error || !invitation) {
        throw new NotFoundError('Invitation not found');
      }

      return createStandardSuccessResponse(
        invitation,
        'Invitation retrieved successfully',
        200,
        {
          operation_type: 'get'
        }
      );
    }, 'Failed to retrieve invitation');
  }
);

// PATCH /api/invitations/[id] - Update invitation (Admin only)
export const PATCH = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;
      const body = await parseRequestBody<{
        email?: string;
        expires_at?: string;
        max_uses?: number;
        notes?: string;
        status?: 'pending' | 'expired';
      }>(request);

      // Check if invitation exists
      const { data: existing } = await supabaseAdmin
        .from('invitations')
        .select('id')
        .eq('id', id)
        .single();

      if (!existing) {
        throw new NotFoundError('Invitation not found');
      }

      const updateData: any = {};
      if (body.email !== undefined) updateData.email = body.email;
      if (body.expires_at !== undefined) updateData.expires_at = body.expires_at;
      if (body.max_uses !== undefined) updateData.max_uses = body.max_uses;
      if (body.notes !== undefined) updateData.notes = body.notes;
      if (body.status !== undefined) updateData.status = body.status;

      const { data: invitation, error } = await supabaseAdmin
        .from('invitations')
        .update(updateData)
        .eq('id', id)
        .select(`
          *,
          created_by_user:users!invitations_created_by_fkey(id, email, profile_data),
          used_by_user:users!invitations_used_by_fkey(id, email, profile_data)
        `)
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        invitation,
        'Invitation updated successfully',
        200,
        {
          operation_type: 'update',
          updated_fields: Object.keys(updateData)
        }
      );
    }, 'Failed to update invitation');
  }
);

// DELETE /api/invitations/[id] - Delete invitation (Admin only)
export const DELETE = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      // Check if invitation exists
      const { data: existing } = await supabaseAdmin
        .from('invitations')
        .select('id, code')
        .eq('id', id)
        .single();

      if (!existing) {
        throw new NotFoundError('Invitation not found');
      }

      const { error } = await supabaseAdmin
        .from('invitations')
        .delete()
        .eq('id', id);

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        { id, code: existing.code },
        'Invitation deleted successfully',
        200,
        {
          operation_type: 'delete'
        }
      );
    }, 'Failed to delete invitation');
  }
);
