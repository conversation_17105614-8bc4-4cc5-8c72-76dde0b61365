import { NextRequest } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { supabaseAdmin } from '@/lib/supabase';
import { createSuccessResponse, createErrorResponse } from '@/lib/api-utils';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return createErrorResponse('No authenticated user', 401);
    }

    // Get all users with this clerk_id to see what's happening
    const { data: users, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('clerk_id', userId);

    if (error) {
      return createSuccessResponse(
        {
          clerkId: userId,
          exists: false,
          error: error.message,
          code: error.code
        },
        'Database error'
      );
    }

    if (!users || users.length === 0) {
      return createSuccessResponse(
        {
          clerkId: userId,
          exists: false,
          count: 0,
          message: 'No users found with this clerk_id'
        },
        'User not found in database'
      );
    }

    if (users.length > 1) {
      return createSuccessResponse(
        {
          clerkId: userId,
          exists: true,
          count: users.length,
          users: users,
          message:
            'Multiple users found with same clerk_id - this is a problem!'
        },
        'Multiple users found'
      );
    }

    return createSuccessResponse(
      {
        clerkId: userId,
        exists: true,
        count: 1,
        user: users[0]
      },
      'User found in database'
    );
  } catch (error) {
    console.error('Test auth error:', error);
    return createErrorResponse('Test endpoint error', 500);
  }
}

// POST endpoint to create user if it doesn't exist
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return createErrorResponse('No authenticated user', 401);
    }

    // Get Clerk user information using REST API
    const CLERK_SECRET_KEY = process.env.CLERK_SECRET_KEY;
    if (!CLERK_SECRET_KEY) {
      return createErrorResponse('Missing Clerk secret key', 500);
    }

    const res = await fetch(`https://api.clerk.dev/v1/users/${userId}`, {
      headers: {
        Authorization: `Bearer ${CLERK_SECRET_KEY}`
      }
    });

    if (!res.ok) {
      return createErrorResponse('Failed to fetch user from Clerk', 500);
    }

    const user = await res.json();

    // Check if user already exists in database
    const { data: existingUsers } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('clerk_id', userId);

    if (existingUsers && existingUsers.length > 0) {
      return createSuccessResponse({
        users: existingUsers,
        message: 'User(s) already exist',
        action: 'no_action_needed'
      });
    }

    // Create user data from Clerk information
    const userData = {
      clerk_id: userId,
      email: user.email_addresses[0]?.email_address || '<EMAIL>',
      role: 'student' as const,
      profile_data: {
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim()
      }
    };

    console.log('Creating new user in Supabase:', userData);

    // Create user in database
    const { data: newUser, error } = await supabaseAdmin
      .from('users')
      .insert(userData)
      .select()
      .single();

    if (error) {
      console.error('Database error creating user:', error);
      return createErrorResponse(`Database error: ${error.message}`, 500);
    }

    // 创建 student profile
    const { error: profileError } = await supabaseAdmin
      .from('student_profiles')
      .insert({
        user_id: newUser.id,
        academic_info: {},
        target_schools: [],
        application_status: {}
      });

    if (profileError) {
      return createErrorResponse(
        `Profile creation error: ${profileError.message}`,
        500
      );
    }

    return createSuccessResponse({
      user: newUser,
      message: 'User and profile created successfully'
    });
  } catch (error) {
    return createErrorResponse(
      `Failed to create user: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}

// DELETE endpoint to clean up duplicate users
export async function DELETE(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return createErrorResponse('No authenticated user', 401);
    }

    // Delete all users with this clerk_id
    const { error } = await supabaseAdmin
      .from('users')
      .delete()
      .eq('clerk_id', userId);

    if (error) {
      return createErrorResponse(`Delete error: ${error.message}`, 500);
    }

    return createSuccessResponse({
      message: 'All users with this clerk_id have been deleted'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    return createErrorResponse('Failed to delete users', 500);
  }
}
