import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  parseRequestBody,
  validateRequiredFields,
  handleAsyncOperation,
  AuthorizationError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { DocumentManagementService } from '@/lib/document-management';
import type { User } from '@/types/application';

interface EssayCopyRequest {
  source_essay_id: string;
  target_school_id: string;
  new_title: string;
  modifications_needed?: string;
}

// POST /api/essays/copy - One-click copy essay for different school
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<EssayCopyRequest>(request);
      
      validateRequiredFields(body, ['source_essay_id', 'target_school_id', 'new_title']);

      // Get the source essay
      const { data: sourceEssay, error: sourceError } = await supabaseAdmin
        .from('documents')
        .select('*')
        .eq('id', body.source_essay_id)
        .single();

      if (sourceError || !sourceEssay) {
        throw new Error('Source essay not found');
      }

      // Check if user can access this essay
      if (currentUser.role === 'student') {
        // Students can only copy their own essays
        const { data: studentProfile } = await supabaseAdmin
          .from('student_profiles')
          .select('id')
          .eq('user_id', currentUser.id)
          .single();

        if (!studentProfile || sourceEssay.student_id !== studentProfile.id) {
          throw new AuthorizationError('You can only copy your own essays');
        }
      }

      // Get target school information
      const { data: targetSchool, error: schoolError } = await supabaseAdmin
        .from('schools')
        .select('name')
        .eq('id', body.target_school_id)
        .single();

      if (schoolError || !targetSchool) {
        throw new Error('Target school not found');
      }

      // Create new essay metadata
      const newMetadata = {
        ...sourceEssay.metadata,
        title: body.new_title,
        school_id: body.target_school_id,
        status: 'draft', // Reset status for new copy
        modifications_needed: body.modifications_needed
      };

      // Copy the Google Doc if it exists
      let newGoogleDocId = null;
      if (sourceEssay.google_doc_id) {
        try {
          // Use DocumentManagementService to copy the Google Doc
          const copyResult = await DocumentManagementService.copyDocument(
            sourceEssay.google_doc_id,
            body.new_title,
            sourceEssay.student_id
          );
          newGoogleDocId = copyResult.google_doc_id;
        } catch (error) {
          console.error('Failed to copy Google Doc:', error);
          // Continue without Google Doc - user can create one later
        }
      }

      // Create the new essay document
      const { data: newEssay, error: createError } = await supabaseAdmin
        .from('documents')
        .insert({
          student_id: sourceEssay.student_id,
          google_doc_id: newGoogleDocId,
          doc_type: sourceEssay.doc_type,
          metadata: newMetadata,
          essay_type: 'supplemental', // Copied essays are typically supplemental
          schools: [body.target_school_id], // Single school for supplemental
          school_id: body.target_school_id,
          source_essay_id: body.source_essay_id
        })
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      return createStandardSuccessResponse(
        {
          ...newEssay,
          source_essay_title: sourceEssay.metadata?.title || 'Untitled',
          target_school_name: targetSchool.name
        },
        'Essay copied successfully',
        201,
        {
          user_role: currentUser.role,
          operation_type: 'copy',
          resource_type: 'essay',
          source_essay_id: body.source_essay_id,
          new_essay_id: newEssay.id,
          target_school_id: body.target_school_id
        }
      );
    }, 'Failed to copy essay');
  }
);
