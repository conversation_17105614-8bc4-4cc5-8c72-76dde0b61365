import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  parseRequestBody,
  validateRequiredFields,
  handleAsyncOperation,
  AuthorizationError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { DocumentManagementService, type CreateDocumentRequest, type DocumentMetadata } from '@/lib/document-management';
import type { User } from '@/types/application';

interface Essay {
  id: string;
  type: 'main' | 'supplemental';
  title: string;
  content?: string;
  schools: string[]; // For main essays: multiple schools
  school_id?: string; // For supplemental: specific school
  source_essay_id?: string; // For copied essays
  word_limit: number;
  word_count?: number;
  recommended_format: 'PDF' | 'Word' | 'Plain Text';
  status: 'draft' | 'in_review' | 'completed';
  google_doc_id?: string;
  google_doc_url?: string;
  created_at: string;
  updated_at: string;
  student_id: string;
}

// GET /api/essays - List essays with main/supplemental categorization
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const url = new URL(request.url);
      const essayType = url.searchParams.get('type'); // 'main' or 'supplemental'
      const studentId = url.searchParams.get('student_id');

      let query = supabaseAdmin
        .from('documents')
        .select(`
          *,
          schools:school_id(name)
        `)
        .in('doc_type', ['essay', 'personal_statement']);

      // Filter by student based on user role
      if (currentUser.role === 'student') {
        // Students can only see their own essays
        const { data: studentProfile } = await supabaseAdmin
          .from('student_profiles')
          .select('id')
          .eq('user_id', currentUser.id)
          .single();

        if (!studentProfile) {
          throw new Error('Student profile not found');
        }

        query = query.eq('student_id', studentProfile.id);
      } else if (currentUser.role === 'consultant') {
        // Consultants can see essays from their assigned students
        if (studentId) {
          query = query.eq('student_id', studentId);
        } else {
          // Get all assigned students
          const { data: relations } = await supabaseAdmin
            .from('student_consultant_relations')
            .select('student_id')
            .eq('consultant_id', currentUser.id)
            .eq('status', 'active');

          if (relations && relations.length > 0) {
            const studentIds = relations.map(r => r.student_id);
            query = query.in('student_id', studentIds);
          } else {
            // No assigned students
            return createStandardSuccessResponse([], 'No essays found');
          }
        }
      } else if (currentUser.role === 'admin') {
        // Admins can see all essays, optionally filtered by student
        if (studentId) {
          query = query.eq('student_id', studentId);
        }
      }

      // Filter by essay type if specified
      if (essayType) {
        query = query.eq('essay_type', essayType);
      }

      const { data: documents, error } = await query.order('updated_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Transform documents to Essay format
      const essays: Essay[] = documents.map(doc => ({
        id: doc.id,
        type: doc.essay_type || (doc.doc_type === 'personal_statement' ? 'main' : 'supplemental'),
        title: doc.metadata?.title || 'Untitled Essay',
        schools: doc.schools || [],
        school_id: doc.school_id,
        source_essay_id: doc.source_essay_id,
        word_limit: doc.metadata?.word_limit || 650,
        word_count: doc.metadata?.word_count || 0,
        recommended_format: doc.metadata?.recommended_format || 'Word',
        status: doc.metadata?.status || 'draft',
        google_doc_id: doc.google_doc_id,
        google_doc_url: doc.metadata?.google_doc_url,
        created_at: doc.created_at,
        updated_at: doc.updated_at,
        student_id: doc.student_id
      }));

      return createStandardSuccessResponse(
        essays,
        'Essays retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          essay_count: essays.length,
          filter_type: essayType || 'all'
        }
      );
    }, 'Failed to retrieve essays');
  }
);

// POST /api/essays - Create new essay
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        type: 'main' | 'supplemental';
        title: string;
        school_ids?: string[]; // For main essays: multiple schools
        school_id?: string; // For supplemental: specific school
        word_limit?: number;
        recommended_format?: 'PDF' | 'Word' | 'Plain Text';
        template_id?: string;
        initial_content?: string;
      }>(request);

      validateRequiredFields(body, ['type', 'title']);

      // Get student profile ID
      let studentId: string;
      if (currentUser.role === 'student') {
        const { data: studentProfile } = await supabaseAdmin
          .from('student_profiles')
          .select('id')
          .eq('user_id', currentUser.id)
          .single();

        if (!studentProfile) {
          throw new Error('Student profile not found');
        }
        studentId = studentProfile.id;
      } else {
        throw new AuthorizationError('Only students can create essays');
      }

      // Validate school requirements
      if (body.type === 'main' && (!body.school_ids || body.school_ids.length === 0)) {
        throw new Error('Main essays must be associated with at least one school');
      }
      if (body.type === 'supplemental' && !body.school_id) {
        throw new Error('Supplemental essays must be associated with a specific school');
      }

      const metadata: DocumentMetadata = {
        title: body.title,
        word_limit: body.word_limit || 650,
        status: 'draft' as const,
        school_id: body.school_id
      };

      const createRequest: CreateDocumentRequest = {
        student_id: studentId,
        doc_type: body.type === 'main' ? 'personal_statement' : 'essay',
        metadata,
        template_id: body.template_id,
        initial_content: body.initial_content
      };

      const document = await DocumentManagementService.createDocument(createRequest);

      // Update with essay-specific fields
      const { data: updatedDocument, error: updateError } = await supabaseAdmin
        .from('documents')
        .update({
          essay_type: body.type,
          schools: body.type === 'main' ? body.school_ids : [body.school_id],
          school_id: body.school_id
        })
        .eq('id', document.id)
        .select()
        .single();

      if (updateError) {
        throw updateError;
      }

      return createStandardSuccessResponse(
        updatedDocument,
        'Essay created successfully',
        201,
        {
          user_role: currentUser.role,
          operation_type: 'create',
          resource_type: 'essay',
          essay_type: body.type,
          essay_id: document.id
        }
      );
    }, 'Failed to create essay');
  }
);
