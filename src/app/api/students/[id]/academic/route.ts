import { NextRequest } from 'next/server';
import {
  withAuth,
  createSuccessResponse,
  parseRequestBody,
  NotFoundError,
  AuthorizationError,
  validateRequiredFields
} from '@/lib/api-utils';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import type {
  User,
  AcademicInfo,
  TestScores,
  APCourse,
  Activity
} from '@/types/application';

interface RouteContext {
  params: { id: string };
}

// Helper function to check if user can access student profile
async function canAccessStudent(
  currentUser: User,
  studentId: string
): Promise<boolean> {
  if (currentUser.role === 'admin') return true;

  if (currentUser.role === 'student') {
    const { data: studentProfile } = await supabase
      .from('student_profiles')
      .select('user_id')
      .eq('id', studentId)
      .single();
    return studentProfile?.user_id === currentUser.id;
  }

  if (currentUser.role === 'consultant') {
    const { data: consultant } = await supabase
      .from('consultants')
      .select('id')
      .eq('user_id', currentUser.id)
      .single();

    if (!consultant) return false;

    const { data: relation } = await supabase
      .from('student_consultant_relations')
      .select('id')
      .eq('student_id', studentId)
      .eq('consultant_id', consultant.id)
      .eq('status', 'active')
      .single();

    return !!relation;
  }

  return false;
}

// GET /api/students/[id]/academic - Get academic information
export const GET = withAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    const { id } = context.params;

    if (!(await canAccessStudent(currentUser, id))) {
      throw new AuthorizationError(
        'You do not have access to this student profile'
      );
    }

    const { data: student, error } = await supabase
      .from('student_profiles')
      .select('academic_info')
      .eq('id', id)
      .single();

    if (error || !student) {
      throw new NotFoundError('Student profile not found');
    }

    return createSuccessResponse(
      student.academic_info || {},
      'Academic information retrieved successfully'
    );
  }
);

// PUT /api/students/[id]/academic - Update academic information
export const PUT = withAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    const { id } = context.params;

    if (!(await canAccessStudent(currentUser, id))) {
      throw new AuthorizationError(
        'You do not have access to this student profile'
      );
    }

    const body = await parseRequestBody<AcademicInfo>(request);

    // Get current academic info
    const { data: currentStudent } = await supabase
      .from('student_profiles')
      .select('academic_info')
      .eq('id', id)
      .single();

    const currentAcademicInfo =
      (currentStudent?.academic_info as AcademicInfo) || {};

    // Merge with existing data
    const updatedAcademicInfo: AcademicInfo = {
      ...currentAcademicInfo,
      ...body
    };

    const { data: student, error } = await supabaseAdmin
      .from('student_profiles')
      .update({
        academic_info: updatedAcademicInfo,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!student) {
      throw new NotFoundError('Student profile not found');
    }

    return createSuccessResponse(
      student.academic_info,
      'Academic information updated successfully'
    );
  }
);
