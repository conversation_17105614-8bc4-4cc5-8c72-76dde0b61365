import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  validateRequiredFields,
  AuthorizationError,
  NotFoundError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { User } from '@/types/application';
import { canAccessStudent } from '@/lib/permissions';

interface APCourse {
  id?: string;
  student_id: string;
  subject: string;
  score: number;
  year: string;
  exam_date?: string;
  is_estimated?: boolean;
  created_at?: string;
  updated_at?: string;
}

interface RouteContext {
  params: { id: string; courseId: string };
}

// GET /api/students/[id]/ap-courses/[courseId] - Get specific AP course
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id, courseId } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const { data: apCourse, error } = await supabaseAdmin
        .from('ap_courses')
        .select('*')
        .eq('id', courseId)
        .eq('student_id', id)
        .single();

      if (error || !apCourse) {
        throw new NotFoundError('AP course not found');
      }

      return createStandardSuccessResponse(
        apCourse,
        'AP course retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'ap_course',
          student_id: id,
          course_id: courseId
        }
      );
    }, 'Failed to retrieve AP course');
  }
);

// PUT /api/students/[id]/ap-courses/[courseId] - Update specific AP course
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id, courseId } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const body = await parseRequestBody<{
        subject?: string;
        score?: number;
        year?: string;
        exam_date?: string;
        is_estimated?: boolean;
      }>(request);

      // Validate score if provided
      if (body.score !== undefined && (body.score < 1 || body.score > 5)) {
        throw new Error('AP score must be between 1 and 5');
      }

      const { data: apCourse, error } = await supabaseAdmin
        .from('ap_courses')
        .update({
          ...body,
          updated_at: new Date().toISOString()
        })
        .eq('id', courseId)
        .eq('student_id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      if (!apCourse) {
        throw new NotFoundError('AP course not found');
      }

      return createStandardSuccessResponse(
        apCourse,
        'AP course updated successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'update',
          resource_type: 'ap_course',
          student_id: id,
          course_id: courseId
        }
      );
    }, 'Failed to update AP course');
  }
);

// DELETE /api/students/[id]/ap-courses/[courseId] - Delete specific AP course
export const DELETE = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id, courseId } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const { error } = await supabaseAdmin
        .from('ap_courses')
        .delete()
        .eq('id', courseId)
        .eq('student_id', id);

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        null,
        'AP course deleted successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'delete',
          resource_type: 'ap_course',
          student_id: id,
          course_id: courseId
        }
      );
    }, 'Failed to delete AP course');
  }
);
