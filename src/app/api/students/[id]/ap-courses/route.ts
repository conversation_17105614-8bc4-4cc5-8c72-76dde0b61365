import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  validateRequiredFields,
  AuthorizationError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { User } from '@/types/application';
import { canAccessStudent } from '@/lib/permissions';

interface APCourse {
  id?: string;
  student_id: string;
  subject: string;
  score: number;
  year: string;
  exam_date?: string;
  is_estimated?: boolean;
  created_at?: string;
  updated_at?: string;
}

interface RouteContext {
  params: { id: string };
}

// GET /api/students/[id]/ap-courses - Get AP courses for student
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const { data: apCourses, error } = await supabaseAdmin
        .from('ap_courses')
        .select('*')
        .eq('student_id', id)
        .order('year', { ascending: false });

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        apCourses || [],
        'AP courses retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'ap_courses',
          student_id: id,
          count: apCourses?.length || 0
        }
      );
    }, 'Failed to retrieve AP courses');
  }
);

// POST /api/students/[id]/ap-courses - Add new AP course
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const body = await parseRequestBody<{
        subject: string;
        score: number;
        year: string;
        exam_date?: string;
        is_estimated?: boolean;
      }>(request);

      validateRequiredFields(body, ['subject', 'score', 'year']);

      // Validate score range
      if (body.score < 1 || body.score > 5) {
        throw new Error('AP score must be between 1 and 5');
      }

      const apCourseData: Omit<APCourse, 'id' | 'created_at' | 'updated_at'> = {
        student_id: id,
        subject: body.subject,
        score: body.score,
        year: body.year,
        exam_date: body.exam_date,
        is_estimated: body.is_estimated || false
      };

      const { data: apCourse, error } = await supabaseAdmin
        .from('ap_courses')
        .insert(apCourseData)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        apCourse,
        'AP course added successfully',
        201,
        {
          user_role: currentUser.role,
          operation_type: 'create',
          resource_type: 'ap_course',
          student_id: id,
          course_id: apCourse.id
        }
      );
    }, 'Failed to add AP course');
  }
);

// PUT /api/students/[id]/ap-courses - Update all AP courses
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const body = await parseRequestBody<APCourse[]>(request);

      // Validate each course
      for (const course of body) {
        if (!course.subject || !course.score || !course.year) {
          throw new Error('Each AP course must have subject, score, and year');
        }
        if (course.score < 1 || course.score > 5) {
          throw new Error('AP scores must be between 1 and 5');
        }
      }

      // Delete existing courses and insert new ones
      const { error: deleteError } = await supabaseAdmin
        .from('ap_courses')
        .delete()
        .eq('student_id', id);

      if (deleteError) {
        throw deleteError;
      }

      if (body.length > 0) {
        const coursesData = body.map(course => ({
          student_id: id,
          subject: course.subject,
          score: course.score,
          year: course.year,
          exam_date: course.exam_date,
          is_estimated: course.is_estimated || false
        }));

        const { data: apCourses, error: insertError } = await supabaseAdmin
          .from('ap_courses')
          .insert(coursesData)
          .select();

        if (insertError) {
          throw insertError;
        }

        return createStandardSuccessResponse(
          apCourses,
          'AP courses updated successfully',
          200,
          {
            user_role: currentUser.role,
            operation_type: 'update',
            resource_type: 'ap_courses',
            student_id: id,
            count: apCourses.length
          }
        );
      }

      return createStandardSuccessResponse(
        [],
        'AP courses updated successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'update',
          resource_type: 'ap_courses',
          student_id: id,
          count: 0
        }
      );
    }, 'Failed to update AP courses');
  }
);
