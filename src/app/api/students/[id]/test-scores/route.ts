import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  validateRequiredFields,
  AuthorizationError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { User } from '@/types/application';
import { canAccessStudent } from '@/lib/permissions';

interface TestScore {
  id?: string;
  student_id: string;
  test_type: 'SAT' | 'ACT' | 'SAT_SUBJECT' | 'TOEFL' | 'IELTS';
  subject?: string;
  score: number;
  max_score: number;
  test_date: string;
  is_superscore?: boolean;
  notes?: string;
  subject_scores?: any;
  created_at?: string;
  updated_at?: string;
}

interface RouteContext {
  params: { id: string };
}

// GET /api/students/[id]/test-scores - Get test scores
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const { data: testScores, error } = await supabaseAdmin
        .from('test_scores')
        .select('*')
        .eq('student_id', id)
        .order('test_date', { ascending: false });

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        testScores || [],
        'Test scores retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'test_scores',
          student_id: id,
          count: testScores?.length || 0
        }
      );
    }, 'Failed to retrieve test scores');
  }
);

// POST /api/students/[id]/test-scores - Add new test score
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const body = await parseRequestBody<{
        test_type: 'SAT' | 'ACT' | 'SAT_SUBJECT' | 'TOEFL' | 'IELTS';
        subject?: string;
        score: number;
        max_score: number;
        test_date: string;
        notes?: string;
        subject_scores?: any;
      }>(request);

      validateRequiredFields(body, ['test_type', 'score', 'max_score', 'test_date']);

      const testScoreData: Omit<TestScore, 'id' | 'created_at' | 'updated_at'> = {
        student_id: id,
        test_type: body.test_type,
        subject: body.subject,
        score: body.score,
        max_score: body.max_score,
        test_date: body.test_date,
        notes: body.notes,
        subject_scores: body.subject_scores
      };

      const { data: testScore, error } = await supabaseAdmin
        .from('test_scores')
        .insert(testScoreData)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        testScore,
        'Test score added successfully',
        201,
        {
          user_role: currentUser.role,
          operation_type: 'create',
          resource_type: 'test_score',
          student_id: id,
          score_id: testScore.id
        }
      );
    }, 'Failed to add test score');
  }
);

// PUT /api/students/[id]/test-scores - Update all test scores
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const body = await parseRequestBody<TestScore[]>(request);

      // Validate each test score
      for (const score of body) {
        if (!score.test_type || !score.score || !score.test_date) {
          throw new Error('Each test score must have test_type, score, and test_date');
        }
      }

      // Delete existing test scores and insert new ones
      const { error: deleteError } = await supabaseAdmin
        .from('test_scores')
        .delete()
        .eq('student_id', id);

      if (deleteError) {
        throw deleteError;
      }

      if (body.length > 0) {
        const scoresData = body.map(score => ({
          student_id: id,
          test_type: score.test_type,
          subject: score.subject,
          score: score.score,
          max_score: score.max_score,
          test_date: score.test_date,
          notes: score.notes,
          subject_scores: score.subject_scores
        }));

        const { data: testScores, error: insertError } = await supabaseAdmin
          .from('test_scores')
          .insert(scoresData)
          .select();

        if (insertError) {
          throw insertError;
        }

        return createStandardSuccessResponse(
          testScores,
          'Test scores updated successfully',
          200,
          {
            user_role: currentUser.role,
            operation_type: 'update',
            resource_type: 'test_scores',
            student_id: id,
            count: testScores.length
          }
        );
      }

      return createStandardSuccessResponse(
        [],
        'Test scores updated successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'update',
          resource_type: 'test_scores',
          student_id: id,
          count: 0
        }
      );
    }, 'Failed to update test scores');
  }
);
