import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  AuthorizationError,
  NotFoundError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { User } from '@/types/application';
import { canAccessStudent } from '@/lib/permissions';

interface TestScore {
  id?: string;
  student_id: string;
  test_type: 'SAT' | 'ACT' | 'SAT_SUBJECT' | 'TOEFL' | 'IELTS';
  subject?: string;
  score: number;
  max_score: number;
  test_date: string;
  is_superscore?: boolean;
  notes?: string;
  subject_scores?: any;
  created_at?: string;
  updated_at?: string;
}

interface RouteContext {
  params: { id: string; scoreId: string };
}

// GET /api/students/[id]/test-scores/[scoreId] - Get specific test score
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id, scoreId } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const { data: testScore, error } = await supabaseAdmin
        .from('test_scores')
        .select('*')
        .eq('id', scoreId)
        .eq('student_id', id)
        .single();

      if (error || !testScore) {
        throw new NotFoundError('Test score not found');
      }

      return createStandardSuccessResponse(
        testScore,
        'Test score retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'test_score',
          student_id: id,
          score_id: scoreId
        }
      );
    }, 'Failed to retrieve test score');
  }
);

// PUT /api/students/[id]/test-scores/[scoreId] - Update specific test score
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id, scoreId } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const body = await parseRequestBody<{
        test_type?: 'SAT' | 'ACT' | 'SAT_SUBJECT' | 'TOEFL' | 'IELTS';
        subject?: string;
        score?: number;
        max_score?: number;
        test_date?: string;
        notes?: string;
        subject_scores?: any;
      }>(request);

      const { data: testScore, error } = await supabaseAdmin
        .from('test_scores')
        .update({
          ...body,
          updated_at: new Date().toISOString()
        })
        .eq('id', scoreId)
        .eq('student_id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      if (!testScore) {
        throw new NotFoundError('Test score not found');
      }

      return createStandardSuccessResponse(
        testScore,
        'Test score updated successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'update',
          resource_type: 'test_score',
          student_id: id,
          score_id: scoreId
        }
      );
    }, 'Failed to update test score');
  }
);

// DELETE /api/students/[id]/test-scores/[scoreId] - Delete specific test score
export const DELETE = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id, scoreId } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const { error } = await supabaseAdmin
        .from('test_scores')
        .delete()
        .eq('id', scoreId)
        .eq('student_id', id);

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        null,
        'Test score deleted successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'delete',
          resource_type: 'test_score',
          student_id: id,
          score_id: scoreId
        }
      );
    }, 'Failed to delete test score');
  }
);
