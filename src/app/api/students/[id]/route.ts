import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  parseRequestBody,
  handleAsyncOperation,
  NotFoundError,
  AuthorizationError
} from '@/lib/api-utils';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import type {
  User,
  AcademicInfo,
  TargetSchool,
  ApplicationStatus
} from '@/types/application';

interface RouteContext {
  params: { id: string };
}

// Helper function to check if user can access student profile
async function canAccessStudent(
  currentUser: User,
  studentId: string
): Promise<boolean> {
  if (currentUser.role === 'admin') {
    return true;
  }

  if (currentUser.role === 'student') {
    // Check if this is their own profile
    const { data: studentProfile } = await supabase
      .from('student_profiles')
      .select('user_id')
      .eq('id', studentId)
      .single();

    return studentProfile?.user_id === currentUser.id;
  }

  if (currentUser.role === 'consultant') {
    // Check if student is assigned to this consultant
    const { data: consultant } = await supabase
      .from('consultants')
      .select('id')
      .eq('user_id', currentUser.id)
      .single();

    if (!consultant) return false;

    const { data: relation } = await supabase
      .from('student_consultant_relations')
      .select('id')
      .eq('student_id', studentId)
      .eq('consultant_id', consultant.id)
      .eq('status', 'active')
      .single();

    return !!relation;
  }

  return false;
}

// GET /api/students/[id] - Get student profile
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const { data: student, error } = await supabase
        .from('student_profiles')
        .select(
          `
        *,
        users!inner(id, email, profile_data, created_at),
        documents(id, doc_type, metadata, created_at, updated_at)
      `
        )
        .eq('id', id)
        .single();

      if (error || !student) {
        throw new NotFoundError('Student profile not found');
      }

      return createStandardSuccessResponse(
        student,
        'Student profile retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          student_id: id,
          documents_count: student.documents?.length || 0,
          operation_type: 'read'
        }
      );
    }, 'Failed to retrieve student profile');
  }
);

// PUT /api/students/[id] - Update student profile
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const body = await parseRequestBody<{
        academic_info?: AcademicInfo;
        target_schools?: TargetSchool[];
        application_status?: ApplicationStatus;
      }>(request);

      const { data: student, error } = await supabaseAdmin
        .from('student_profiles')
        .update({
          ...body,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      if (!student) {
        throw new NotFoundError('Student profile not found');
      }

      return createStandardSuccessResponse(
        student,
        'Student profile updated successfully',
        200,
        {
          user_role: currentUser.role,
          student_id: id,
          updated_fields: Object.keys(body),
          operation_type: 'update'
        }
      );
    }, 'Failed to update student profile');
  }
);
