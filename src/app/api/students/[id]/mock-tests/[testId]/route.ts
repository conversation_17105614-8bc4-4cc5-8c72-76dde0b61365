import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  AuthorizationError,
  NotFoundError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { User } from '@/types/application';
import { canAccessStudent } from '@/lib/permissions';

interface MockTestScore {
  id?: string;
  student_id: string;
  exam_type: 'SAT' | 'ACT' | 'SAT_SUBJECT' | 'TOEFL' | 'IELTS' | 'AP' | 'Other';
  test_date: string;
  score: number;
  max_score: number;
  source: 'practice_test' | 'tutor_feedback' | 'official_practice' | 'mock_exam' | 'prep_course';
  notes?: string;
  document_url?: string;
  created_by: string;
  tutor_feedback?: string;
  improvement_areas?: string[];
  created_at?: string;
  updated_at?: string;
}

interface RouteContext {
  params: { id: string; testId: string };
}

// GET /api/students/[id]/mock-tests/[testId] - Get specific mock test score
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id, testId } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const { data: mockTest, error } = await supabaseAdmin
        .from('mock_test_scores')
        .select(`
          *,
          created_by_user:users!created_by(id, email, profile_data)
        `)
        .eq('id', testId)
        .eq('student_id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          throw new NotFoundError('Mock test score not found');
        }
        throw error;
      }

      return createStandardSuccessResponse(
        mockTest,
        'Mock test score retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          student_id: id,
          mock_test_id: testId
        }
      );
    }, 'Failed to retrieve mock test score');
  }
);

// PUT /api/students/[id]/mock-tests/[testId] - Update specific mock test score
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id, testId } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const body = await parseRequestBody<{
        exam_type?: 'SAT' | 'ACT' | 'SAT_SUBJECT' | 'TOEFL' | 'IELTS' | 'AP' | 'Other';
        test_date?: string;
        score?: number;
        max_score?: number;
        source?: 'practice_test' | 'tutor_feedback' | 'official_practice' | 'mock_exam' | 'prep_course';
        notes?: string;
        document_url?: string;
        tutor_feedback?: string;
        improvement_areas?: string[];
      }>(request);

      // Check if mock test exists and belongs to the student
      const { data: existingTest, error: checkError } = await supabaseAdmin
        .from('mock_test_scores')
        .select('id')
        .eq('id', testId)
        .eq('student_id', id)
        .single();

      if (checkError) {
        if (checkError.code === 'PGRST116') {
          throw new NotFoundError('Mock test score not found');
        }
        throw checkError;
      }

      const updateData: any = {
        ...body,
        updated_at: new Date().toISOString()
      };

      const { data: mockTest, error } = await supabaseAdmin
        .from('mock_test_scores')
        .update(updateData)
        .eq('id', testId)
        .eq('student_id', id)
        .select(`
          *,
          created_by_user:users!created_by(id, email, profile_data)
        `)
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        mockTest,
        'Mock test score updated successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'update',
          resource_type: 'mock_test_score',
          student_id: id,
          mock_test_id: testId
        }
      );
    }, 'Failed to update mock test score');
  }
);

// DELETE /api/students/[id]/mock-tests/[testId] - Delete specific mock test score
export const DELETE = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id, testId } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      // Check if mock test exists and belongs to the student
      const { data: existingTest, error: checkError } = await supabaseAdmin
        .from('mock_test_scores')
        .select('id')
        .eq('id', testId)
        .eq('student_id', id)
        .single();

      if (checkError) {
        if (checkError.code === 'PGRST116') {
          throw new NotFoundError('Mock test score not found');
        }
        throw checkError;
      }

      const { error } = await supabaseAdmin
        .from('mock_test_scores')
        .delete()
        .eq('id', testId)
        .eq('student_id', id);

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        { id: testId },
        'Mock test score deleted successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'delete',
          resource_type: 'mock_test_score',
          student_id: id,
          mock_test_id: testId
        }
      );
    }, 'Failed to delete mock test score');
  }
);
