import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  validateRequiredFields,
  AuthorizationError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { User } from '@/types/application';
import { canAccessStudent } from '@/lib/permissions';

interface MockTestScore {
  id?: string;
  student_id: string;
  exam_type: 'SAT' | 'ACT' | 'SAT_SUBJECT' | 'TOEFL' | 'IELTS' | 'AP' | 'Other';
  test_date: string;
  score: number;
  max_score: number;
  source: 'practice_test' | 'tutor_feedback' | 'official_practice' | 'mock_exam' | 'prep_course';
  notes?: string;
  document_url?: string;
  created_by: string;
  tutor_feedback?: string;
  improvement_areas?: string[];
  created_at?: string;
  updated_at?: string;
}

interface RouteContext {
  params: { id: string };
}

// GET /api/students/[id]/mock-tests - Get all mock test scores for a student
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const { data: mockTests, error } = await supabaseAdmin
        .from('mock_test_scores')
        .select(`
          *,
          created_by_user:users!created_by(id, email, profile_data)
        `)
        .eq('student_id', id)
        .order('test_date', { ascending: false });

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        mockTests || [],
        'Mock test scores retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          student_id: id,
          count: mockTests?.length || 0
        }
      );
    }, 'Failed to retrieve mock test scores');
  }
);

// POST /api/students/[id]/mock-tests - Add new mock test score
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const body = await parseRequestBody<{
        exam_type: 'SAT' | 'ACT' | 'SAT_SUBJECT' | 'TOEFL' | 'IELTS' | 'AP' | 'Other';
        test_date: string;
        score: number;
        max_score: number;
        source: 'practice_test' | 'tutor_feedback' | 'official_practice' | 'mock_exam' | 'prep_course';
        notes?: string;
        document_url?: string;
        tutor_feedback?: string;
        improvement_areas?: string[];
      }>(request);

      validateRequiredFields(body, ['exam_type', 'test_date', 'score', 'max_score', 'source']);

      const mockTestData: Omit<MockTestScore, 'id' | 'created_at' | 'updated_at'> = {
        student_id: id,
        exam_type: body.exam_type,
        test_date: body.test_date,
        score: body.score,
        max_score: body.max_score,
        source: body.source,
        notes: body.notes,
        document_url: body.document_url,
        created_by: currentUser.id,
        tutor_feedback: body.tutor_feedback,
        improvement_areas: body.improvement_areas
      };

      const { data: mockTest, error } = await supabaseAdmin
        .from('mock_test_scores')
        .insert(mockTestData)
        .select(`
          *,
          created_by_user:users!created_by(id, email, profile_data)
        `)
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        mockTest,
        'Mock test score added successfully',
        201,
        {
          user_role: currentUser.role,
          operation_type: 'create',
          resource_type: 'mock_test_score',
          student_id: id,
          mock_test_id: mockTest.id
        }
      );
    }, 'Failed to add mock test score');
  }
);

// PUT /api/students/[id]/mock-tests - Bulk update mock test scores
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = context.params;

      if (!(await canAccessStudent(currentUser, id))) {
        throw new AuthorizationError(
          'You do not have access to this student profile'
        );
      }

      const body = await parseRequestBody<MockTestScore[]>(request);

      // Validate each mock test score
      for (const test of body) {
        if (!test.exam_type || !test.test_date || !test.score || !test.source) {
          throw new Error('Each mock test score must have exam_type, test_date, score, and source');
        }
      }

      // Delete existing mock test scores for this student
      const { error: deleteError } = await supabaseAdmin
        .from('mock_test_scores')
        .delete()
        .eq('student_id', id);

      if (deleteError) {
        throw deleteError;
      }

      if (body.length > 0) {
        const testsData = body.map(test => ({
          student_id: id,
          exam_type: test.exam_type,
          test_date: test.test_date,
          score: test.score,
          max_score: test.max_score,
          source: test.source,
          notes: test.notes,
          document_url: test.document_url,
          created_by: currentUser.id,
          tutor_feedback: test.tutor_feedback,
          improvement_areas: test.improvement_areas
        }));

        const { data: mockTests, error: insertError } = await supabaseAdmin
          .from('mock_test_scores')
          .insert(testsData)
          .select(`
            *,
            created_by_user:users!created_by(id, email, profile_data)
          `);

        if (insertError) {
          throw insertError;
        }

        return createStandardSuccessResponse(
          mockTests,
          'Mock test scores updated successfully',
          200,
          {
            user_role: currentUser.role,
            operation_type: 'bulk_update',
            resource_type: 'mock_test_scores',
            student_id: id,
            count: mockTests.length
          }
        );
      }

      return createStandardSuccessResponse(
        [],
        'Mock test scores cleared successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'clear',
          resource_type: 'mock_test_scores',
          student_id: id
        }
      );
    }, 'Failed to update mock test scores');
  }
);
