import { NextRequest } from 'next/server';
import {
  withAuth,
  createSuccessResponse,
  parseRequestBody,
  NotFoundError,
  AuthorizationError,
  validateRequiredFields
} from '@/lib/api-utils';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import { DocumentPermissionService } from '@/lib/google-apis';
import { AdvancedPermissionManager } from '@/lib/permission-manager';
import type { User } from '@/types/application';

interface RouteContext {
  params: { id: string };
}

// Helper function to check if user can access student profile
async function canAccessStudent(
  currentUser: User,
  studentId: string
): Promise<boolean> {
  if (currentUser.role === 'admin') return true;

  if (currentUser.role === 'student') {
    const { data: studentProfile } = await supabase
      .from('student_profiles')
      .select('user_id')
      .eq('id', studentId)
      .single();
    return studentProfile?.user_id === currentUser.id;
  }

  if (currentUser.role === 'consultant') {
    const { data: consultant } = await supabase
      .from('consultants')
      .select('id')
      .eq('user_id', currentUser.id)
      .single();

    if (!consultant) return false;

    const { data: relation } = await supabase
      .from('student_consultant_relations')
      .select('id')
      .eq('student_id', studentId)
      .eq('consultant_id', consultant.id)
      .eq('status', 'active')
      .single();

    return !!relation;
  }

  return false;
}

// GET /api/students/[id]/assignments - Get student's consultant assignments
export const GET = withAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    const { id } = context.params;

    if (!(await canAccessStudent(currentUser, id))) {
      throw new AuthorizationError(
        'You do not have access to this student profile'
      );
    }

    const { data: assignments, error } = await supabase
      .from('student_consultant_relations')
      .select(
        `
      *,
      consultants!inner(
        *,
        users!inner(id, email, profile_data)
      )
    `
      )
      .eq('student_id', id)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return createSuccessResponse(
      assignments || [],
      'Student assignments retrieved successfully'
    );
  }
);

// POST /api/students/[id]/assignments - Assign consultant to student (Admin only)
export const POST = withAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    const { id } = context.params;

    // Only admins can create assignments
    if (currentUser.role !== 'admin') {
      throw new AuthorizationError(
        'Only admins can assign consultants to students'
      );
    }

    const body = await parseRequestBody<{
      consultant_id: string;
      role_type?: 'main_advisor' | 'essay_teacher' | 'planning_assistant' | 'subject_specialist';
      start_date?: string;
      notes?: string;
    }>(request);

    validateRequiredFields(body, ['consultant_id']);

    // Check if student exists
    const { data: student, error: studentError } = await supabase
      .from('student_profiles')
      .select(
        `
      id,
      users!inner(id, email)
    `
      )
      .eq('id', id)
      .single();

    if (studentError || !student) {
      throw new NotFoundError('Student not found');
    }

    // Check if consultant exists
    const { data: consultant, error: consultantError } = await supabase
      .from('consultants')
      .select(
        `
      id,
      users!inner(id, email)
    `
      )
      .eq('id', body.consultant_id)
      .single();

    if (consultantError || !consultant) {
      throw new NotFoundError('Consultant not found');
    }

    const roleType = body.role_type || 'main_advisor';

    // Check if trying to assign main advisor when one already exists
    if (roleType === 'main_advisor') {
      const { data: existingMainAdvisor } = await supabase
        .from('student_consultant_relations')
        .select('id, consultant_id')
        .eq('student_id', id)
        .eq('role_type', 'main_advisor')
        .eq('status', 'active')
        .single();

      if (existingMainAdvisor && existingMainAdvisor.consultant_id !== body.consultant_id) {
        throw new Error('Student already has an active main advisor. Please remove the current main advisor first or assign a different role.');
      }
    }

    // Check if assignment already exists for this role
    const { data: existingAssignment } = await supabase
      .from('student_consultant_relations')
      .select('id, status, role_type')
      .eq('student_id', id)
      .eq('consultant_id', body.consultant_id)
      .eq('role_type', roleType)
      .single();

    if (existingAssignment) {
      if (existingAssignment.status === 'active') {
        throw new Error(`Student is already assigned to this consultant as ${existingAssignment.role_type}`);
      } else {
        // Reactivate existing assignment
        const { data: assignment, error } = await supabaseAdmin
          .from('student_consultant_relations')
          .update({
            status: 'active',
            role_type: roleType,
            start_date:
              body.start_date || new Date().toISOString().split('T')[0],
            notes: body.notes || null,
            assigned_by: currentUser.id,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingAssignment.id)
          .select(
            `
          *,
          consultants!inner(
            *,
            users!inner(id, email, profile_data)
          )
        `
          )
          .single();

        if (error) {
          throw error;
        }

        // Update Google Docs permissions for existing documents
        try {
          await AdvancedPermissionManager.handleConsultantAssignmentChange(
            id,
            undefined,
            body.consultant_id
          );
        } catch (permissionError) {
          console.error(
            'Error updating document permissions:',
            permissionError
          );
        }

        return createSuccessResponse(
          assignment,
          'Student assignment reactivated successfully'
        );
      }
    }

    // Create new assignment
    const { data: assignment, error } = await supabaseAdmin
      .from('student_consultant_relations')
      .insert({
        student_id: id,
        consultant_id: body.consultant_id,
        role_type: roleType,
        start_date: body.start_date || new Date().toISOString().split('T')[0],
        notes: body.notes || null,
        assigned_by: currentUser.id,
        status: 'active'
      })
      .select(
        `
      *,
      consultants!inner(
        *,
        users!inner(id, email, profile_data)
      )
    `
      )
      .single();

    if (error) {
      throw error;
    }

    // Update Google Docs permissions for existing documents
    try {
      await AdvancedPermissionManager.handleConsultantAssignmentChange(
        id,
        undefined,
        body.consultant_id
      );
    } catch (permissionError) {
      console.error('Error updating document permissions:', permissionError);
    }

    return createSuccessResponse(
      assignment,
      'Student assigned to consultant successfully',
      201
    );
  }
);

// DELETE /api/students/[id]/assignments - Remove consultant assignment (Admin only)
export const DELETE = withAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    const { id } = context.params;

    // Only admins can remove assignments
    if (currentUser.role !== 'admin') {
      throw new AuthorizationError(
        'Only admins can remove consultant assignments'
      );
    }

    const url = new URL(request.url);
    const consultantId = url.searchParams.get('consultant_id');

    if (!consultantId) {
      throw new Error('consultant_id query parameter is required');
    }

    // Find and deactivate the assignment
    const { data: assignment, error: findError } = await supabase
      .from('student_consultant_relations')
      .select('id')
      .eq('student_id', id)
      .eq('consultant_id', consultantId)
      .eq('status', 'active')
      .single();

    if (findError || !assignment) {
      throw new NotFoundError('Active assignment not found');
    }

    // Deactivate assignment
    const { error: updateError } = await supabaseAdmin
      .from('student_consultant_relations')
      .update({ status: 'inactive' })
      .eq('id', assignment.id);

    if (updateError) {
      throw updateError;
    }

    // Remove consultant access from all student documents
    try {
      await AdvancedPermissionManager.handleConsultantAssignmentChange(
        id,
        consultantId,
        undefined
      );
    } catch (permissionError) {
      console.error('Error removing document permissions:', permissionError);
    }

    return createSuccessResponse(
      null,
      'Consultant assignment removed successfully and document permissions updated'
    );
  }
);
