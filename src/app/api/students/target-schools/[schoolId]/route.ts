import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  AuthorizationError,
  NotFoundError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { User } from '@/types/application';

interface RouteContext {
  params: { schoolId: string };
}

// GET /api/students/target-schools/[schoolId] - Get specific target school
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { schoolId } = context.params;

      // Get student profile ID for current user
      const { data: studentProfile } = await supabaseAdmin
        .from('student_profiles')
        .select('id')
        .eq('user_id', currentUser.id)
        .single();

      if (!studentProfile) {
        throw new AuthorizationError('Student profile not found');
      }

      const { data: targetSchool, error } = await supabaseAdmin
        .from('target_schools')
        .select(`
          *,
          schools (
            id,
            name,
            details
          )
        `)
        .eq('id', schoolId)
        .eq('student_id', studentProfile.id)
        .single();

      if (error || !targetSchool) {
        throw new NotFoundError('Target school not found');
      }

      return createStandardSuccessResponse(
        targetSchool,
        'Target school retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'target_school',
          school_id: schoolId
        }
      );
    }, 'Failed to retrieve target school');
  }
);

// PUT /api/students/target-schools/[schoolId] - Update target school
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { schoolId } = context.params;

      // Get student profile ID for current user
      const { data: studentProfile } = await supabaseAdmin
        .from('student_profiles')
        .select('id')
        .eq('user_id', currentUser.id)
        .single();

      if (!studentProfile) {
        throw new AuthorizationError('Student profile not found');
      }

      const body = await parseRequestBody<{
        application_type?: 'early_decision' | 'early_action' | 'regular_decision' | 'rolling_admission';
        deadline?: string;
        priority?: 'safety' | 'target' | 'reach';
        notes?: string;
        status?: 'not_started' | 'in_progress' | 'submitted' | 'decision_received';
        decision?: 'accepted' | 'rejected' | 'waitlisted' | 'deferred';
        decision_date?: string;
      }>(request);

      const { data: targetSchool, error } = await supabaseAdmin
        .from('target_schools')
        .update({
          ...body,
          updated_at: new Date().toISOString()
        })
        .eq('id', schoolId)
        .eq('student_id', studentProfile.id)
        .select(`
          *,
          schools (
            id,
            name,
            details
          )
        `)
        .single();

      if (error) {
        throw error;
      }

      if (!targetSchool) {
        throw new NotFoundError('Target school not found');
      }

      return createStandardSuccessResponse(
        targetSchool,
        'Target school updated successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'update',
          resource_type: 'target_school',
          school_id: schoolId
        }
      );
    }, 'Failed to update target school');
  }
);

// DELETE /api/students/target-schools/[schoolId] - Remove target school
export const DELETE = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { schoolId } = context.params;

      // Get student profile ID for current user
      const { data: studentProfile } = await supabaseAdmin
        .from('student_profiles')
        .select('id')
        .eq('user_id', currentUser.id)
        .single();

      if (!studentProfile) {
        throw new AuthorizationError('Student profile not found');
      }

      const { error } = await supabaseAdmin
        .from('target_schools')
        .delete()
        .eq('id', schoolId)
        .eq('student_id', studentProfile.id);

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        null,
        'Target school removed successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'delete',
          resource_type: 'target_school',
          school_id: schoolId
        }
      );
    }, 'Failed to remove target school');
  }
);
