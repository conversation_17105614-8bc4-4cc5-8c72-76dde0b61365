import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  validateRequiredFields
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { User } from '@/types/application';

interface TargetSchool {
  id?: string;
  student_id: string;
  school_id: string;
  application_type: 'early_decision' | 'early_action' | 'regular_decision' | 'rolling_admission';
  deadline: string;
  priority: 'safety' | 'target' | 'reach';
  notes?: string;
  status?: 'not_started' | 'in_progress' | 'submitted' | 'decision_received';
  created_at?: string;
  updated_at?: string;
}

// GET /api/students/target-schools - Get target schools for current user
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // Get student profile ID for current user
      const { data: studentProfile } = await supabaseAdmin
        .from('student_profiles')
        .select('id')
        .eq('user_id', currentUser.id)
        .single();

      if (!studentProfile) {
        return createStandardSuccessResponse(
          [],
          'No student profile found',
          200,
          {
            user_role: currentUser.role,
            operation_type: 'read',
            resource_type: 'target_schools'
          }
        );
      }

      const { data: targetSchools, error } = await supabaseAdmin
        .from('target_schools')
        .select(`
          *,
          schools (
            id,
            name,
            details
          )
        `)
        .eq('student_id', studentProfile.id)
        .order('deadline', { ascending: true });

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        targetSchools || [],
        'Target schools retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'target_schools',
          count: targetSchools?.length || 0
        }
      );
    }, 'Failed to retrieve target schools');
  }
);

// POST /api/students/target-schools - Add new target school
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        school_id: string;
        application_type: 'early_decision' | 'early_action' | 'regular_decision' | 'rolling_admission';
        deadline: string;
        priority: 'safety' | 'target' | 'reach';
        notes?: string;
      }>(request);

      validateRequiredFields(body, ['school_id', 'application_type', 'deadline', 'priority']);

      // Get student profile ID for current user
      const { data: studentProfile } = await supabaseAdmin
        .from('student_profiles')
        .select('id')
        .eq('user_id', currentUser.id)
        .single();

      if (!studentProfile) {
        throw new Error('Student profile not found');
      }

      // Check if school already exists in target list
      const { data: existingTarget } = await supabaseAdmin
        .from('target_schools')
        .select('id')
        .eq('student_id', studentProfile.id)
        .eq('school_id', body.school_id)
        .single();

      if (existingTarget) {
        throw new Error('School is already in your target list');
      }

      const targetSchoolData: Omit<TargetSchool, 'id' | 'created_at' | 'updated_at'> = {
        student_id: studentProfile.id,
        school_id: body.school_id,
        application_type: body.application_type,
        deadline: body.deadline,
        priority: body.priority,
        notes: body.notes,
        status: 'not_started'
      };

      const { data: targetSchool, error } = await supabaseAdmin
        .from('target_schools')
        .insert(targetSchoolData)
        .select(`
          *,
          schools (
            id,
            name,
            details
          )
        `)
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        targetSchool,
        'Target school added successfully',
        201,
        {
          user_role: currentUser.role,
          operation_type: 'create',
          resource_type: 'target_school',
          school_id: body.school_id
        }
      );
    }, 'Failed to add target school');
  }
);
