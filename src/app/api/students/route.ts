import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  withStandardRole,
  createStandardSuccessResponse,
  createStandardPaginatedResponse,
  parsePaginationParams,
  handleAsyncOperation,
  AuthorizationError
} from '@/lib/api-utils';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

// GET /api/students - List student profiles
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const pagination = parsePaginationParams(request);

      let query = supabase
        .from('student_profiles')
        .select(
          `
        *,
        users!inner(id, email, profile_data, created_at)
      `,
          { count: 'exact' }
        )
        .range(pagination.offset, pagination.offset + pagination.limit - 1)
        .order('created_at', { ascending: false });

      // Role-based filtering
      let assignedStudentIds: string[] = [];
      if (currentUser.role === 'student') {
        // Students can only see their own profile
        query = query.eq('user_id', currentUser.id);
      } else if (currentUser.role === 'consultant') {
        // Consultants can only see their assigned students
        const { data: relations } = await supabase
          .from('student_consultant_relations')
          .select('student_id')
          .eq(
            'consultant_id',
            (
              await supabase
                .from('consultants')
                .select('id')
                .eq('user_id', currentUser.id)
                .single()
            ).data?.id
          )
          .eq('status', 'active');

        if (relations && relations.length > 0) {
          assignedStudentIds = relations.map((r) => r.student_id);
          query = query.in('id', assignedStudentIds);
        } else {
          // No assigned students
          return createStandardPaginatedResponse(
            [],
            0,
            pagination,
            'No assigned students found',
            {
              user_role: currentUser.role,
              assigned_students_count: 0,
              operation_type: 'list'
            }
          );
        }
      }
      // Admins can see all students (no additional filtering)

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      return createStandardPaginatedResponse(
        data || [],
        count || 0,
        pagination,
        'Student profiles retrieved successfully',
        {
          user_role: currentUser.role,
          total_students: count || 0,
          assigned_students_count:
            currentUser.role === 'consultant'
              ? assignedStudentIds.length
              : undefined,
          operation_type: 'list'
        }
      );
    }, 'Failed to retrieve student profiles');
  }
);
