import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  withStandardRole,
  createStandardSuccessResponse,
  createStandardPaginatedResponse,
  parseRequestBody,
  validateRequiredFields,
  parsePaginationParams,
  handleAsyncOperation
} from '@/lib/api-utils';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import type {
  User,
  SchoolDetails,
  ApplicationRequirements
} from '@/types/application';

// GET /api/schools - List schools
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const pagination = parsePaginationParams(request);
      const url = new URL(request.url);
      const search = url.searchParams.get('search');
      const state = url.searchParams.get('state');

      let query = supabase
        .from('schools')
        .select('*', { count: 'exact' })
        .range(pagination.offset, pagination.offset + pagination.limit - 1)
        .order('name', { ascending: true });

      // Apply search filter
      if (search) {
        query = query.ilike('name', `%${search}%`);
      }

      // Apply state filter
      if (state) {
        query = query.contains('details', { location: state });
      }

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      return createStandardPaginatedResponse(
        data || [],
        count || 0,
        pagination,
        'Schools retrieved successfully',
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'schools',
          applied_filters: {
            search: search || null,
            state: state || null
          },
          total_results: count || 0
        }
      );
    }, 'Failed to retrieve schools');
  }
);

// POST /api/schools - Create new school (Admin only)
export const POST = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        name: string;
        details?: SchoolDetails;
        application_requirements?: ApplicationRequirements;
      }>(request);

      validateRequiredFields(body, ['name']);

      const { data: school, error } = await supabaseAdmin
        .from('schools')
        .insert({
          name: body.name,
          details: body.details || {},
          application_requirements: body.application_requirements || {}
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        school,
        'School created successfully',
        201,
        {
          user_role: currentUser.role,
          operation_type: 'create',
          resource_type: 'school',
          school_id: school.id,
          admin_operation: true
        }
      );
    }, 'Failed to create school');
  }
);
