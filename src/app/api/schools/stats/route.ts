import { NextRequest } from 'next/server';
import { withStandardRole } from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { createStandardSuccessResponse, handleAsyncOperation } from '@/lib/api-utils';
import type { User } from '@/types/application';

interface SchoolStats {
  totalSchools: number;
  publicSchools: number;
  privateSchools: number;
  averageAdmissionRate: number;
  schoolsByState: Record<string, number>;
  applicationStats: {
    totalApplications: number;
    activeApplications: number;
    submittedApplications: number;
  };
}

// GET /api/schools/stats - Get school statistics (Admin only)
export const GET = withStandardRole(
  ['admin'],
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // Get total schools count
      const { count: totalSchools } = await supabaseAdmin
        .from('schools')
        .select('*', { count: 'exact', head: true });

      // Get schools with details for analysis
      const { data: schools } = await supabaseAdmin
        .from('schools')
        .select('details');

      // Calculate statistics
      let publicSchools = 0;
      let privateSchools = 0;
      let totalAdmissionRate = 0;
      let schoolsWithAdmissionRate = 0;
      const schoolsByState: Record<string, number> = {};

      schools?.forEach((school) => {
        const details = school.details as any;
        
        // Count by type
        if (details?.type?.toLowerCase() === 'public') {
          publicSchools++;
        } else if (details?.type?.toLowerCase() === 'private') {
          privateSchools++;
        }

        // Calculate admission rate average
        if (details?.admissionRate && typeof details.admissionRate === 'number') {
          totalAdmissionRate += details.admissionRate;
          schoolsWithAdmissionRate++;
        }

        // Count by state
        if (details?.location) {
          const location = details.location as string;
          // Extract state from location (assuming format like "City, State")
          const statePart = location.split(',').pop()?.trim();
          if (statePart) {
            schoolsByState[statePart] = (schoolsByState[statePart] || 0) + 1;
          }
        }
      });

      const averageAdmissionRate = schoolsWithAdmissionRate > 0 
        ? totalAdmissionRate / schoolsWithAdmissionRate 
        : 0;

      // Get application statistics
      // Note: This would need to be implemented based on your application tracking system
      // For now, returning placeholder values
      const applicationStats = {
        totalApplications: 0,
        activeApplications: 0,
        submittedApplications: 0
      };

      const stats: SchoolStats = {
        totalSchools: totalSchools || 0,
        publicSchools,
        privateSchools,
        averageAdmissionRate: Math.round(averageAdmissionRate * 100) / 100,
        schoolsByState,
        applicationStats
      };

      return createStandardSuccessResponse(
        stats,
        'School statistics retrieved successfully'
      );
    });
  }
);
