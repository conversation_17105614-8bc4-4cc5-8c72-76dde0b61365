import { withStandardRole } from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { createStandardSuccessResponse, handleAsyncOperation } from '@/lib/api-utils';
import { optimizedQueries } from '@/lib/database-optimization';

interface AnalyticsData {
  overview: {
    totalUsers: number;
    totalStudents: number;
    totalConsultants: number;
    totalAdmins: number;
    activeApplications: number;
    completedApplications: number;
    documentsCreated: number;
    avgApplicationProgress: number;
  };
  userGrowth: Array<{
    month: string;
    students: number;
    consultants: number;
  }>;
  applicationStats: {
    byStatus: Record<string, number>;
    bySchoolType: Record<string, number>;
  };
  consultantPerformance: Array<{
    id: string;
    name: string;
    studentsAssigned: number;
    documentsReviewed: number;
    avgResponseTime: string;
    rating: number;
  }>;
  documentStats: {
    totalDocuments: number;
    byType: Record<string, number>;
    byStatus: Record<string, number>;
    collaborationMetrics: {
      avgCommentsPerDocument: number;
      avgRevisionsPerDocument: number;
      documentsWithGoogleDocs: number;
    };
  };
  systemHealth: {
    uptime: string;
    responseTime: string;
    errorRate: string;
    activeUsers: number;
  };
}

// GET /api/analytics - Get comprehensive analytics data (Admin only)
export const GET = withStandardRole(
  ['admin'],
  async () => {
    return handleAsyncOperation(async () => {
      // Use optimized parallel queries for better performance
      const [
        { count: totalUsers },
        { count: totalStudents },
        { count: totalConsultants },
        { count: totalAdmins }
      ] = await Promise.all([
        supabaseAdmin.from('users').select('*', { count: 'exact', head: true }),
        supabaseAdmin.from('users').select('*', { count: 'exact', head: true }).eq('role', 'student'),
        supabaseAdmin.from('users').select('*', { count: 'exact', head: true }).eq('role', 'consultant'),
        supabaseAdmin.from('users').select('*', { count: 'exact', head: true }).eq('role', 'admin')
      ]);

      // Get document counts with parallel queries
      const [
        { count: totalDocuments },
        { count: documentsWithGoogleDocs },
        { data: documentsByType }
      ] = await Promise.all([
        supabaseAdmin.from('documents').select('*', { count: 'exact', head: true }),
        supabaseAdmin.from('documents').select('*', { count: 'exact', head: true }).not('google_doc_id', 'is', null),
        supabaseAdmin.from('documents').select('doc_type')
      ]);

      const docTypeStats: Record<string, number> = {};
      documentsByType?.forEach((doc) => {
        docTypeStats[doc.doc_type] = (docTypeStats[doc.doc_type] || 0) + 1;
      });

      // Get user growth data (simplified - last 6 months)
      const userGrowth = [];
      const months = ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      for (let i = 0; i < 6; i++) {
        const monthStart = new Date();
        monthStart.setMonth(monthStart.getMonth() - (5 - i));
        monthStart.setDate(1);
        monthStart.setHours(0, 0, 0, 0);

        const monthEnd = new Date(monthStart);
        monthEnd.setMonth(monthEnd.getMonth() + 1);

        const { count: studentsThisMonth } = await supabaseAdmin
          .from('users')
          .select('*', { count: 'exact', head: true })
          .eq('role', 'student')
          .gte('created_at', monthStart.toISOString())
          .lt('created_at', monthEnd.toISOString());

        const { count: consultantsThisMonth } = await supabaseAdmin
          .from('users')
          .select('*', { count: 'exact', head: true })
          .eq('role', 'consultant')
          .gte('created_at', monthStart.toISOString())
          .lt('created_at', monthEnd.toISOString());

        userGrowth.push({
          month: months[i],
          students: studentsThisMonth || 0,
          consultants: consultantsThisMonth || 0
        });
      }

      // Get consultant performance data
      const { data: consultants } = await supabaseAdmin
        .from('consultants')
        .select(`
          id,
          users!inner(email, profile_data)
        `);

      const consultantPerformance = await Promise.all(
        (consultants || []).map(async (consultant) => {
          const { count: studentsAssigned } = await supabaseAdmin
            .from('student_consultant_relations')
            .select('*', { count: 'exact', head: true })
            .eq('consultant_id', consultant.id)
            .eq('status', 'active');

          const userData = consultant.users as any;
          const name = userData?.profile_data?.name || 
                      userData?.email?.split('@')[0] || 
                      'Unknown';

          return {
            id: consultant.id,
            name,
            studentsAssigned: studentsAssigned || 0,
            documentsReviewed: 0, // Would need to implement document review tracking
            avgResponseTime: '2.3 hours', // Placeholder
            rating: 4.5 // Placeholder
          };
        })
      );

      const analyticsData: AnalyticsData = {
        overview: {
          totalUsers: totalUsers || 0,
          totalStudents: totalStudents || 0,
          totalConsultants: totalConsultants || 0,
          totalAdmins: totalAdmins || 0,
          activeApplications: 0, // Would need application tracking system
          completedApplications: 0, // Would need application tracking system
          documentsCreated: totalDocuments || 0,
          avgApplicationProgress: 0 // Would need application progress tracking
        },
        userGrowth,
        applicationStats: {
          byStatus: {
            'In Progress': 0,
            'Submitted': 0,
            'Accepted': 0,
            'Rejected': 0
          },
          bySchoolType: {
            'Reach Schools': 0,
            'Target Schools': 0,
            'Safety Schools': 0
          }
        },
        consultantPerformance,
        documentStats: {
          totalDocuments: totalDocuments || 0,
          byType: docTypeStats,
          byStatus: {
            'Draft': 0,
            'In Review': 0,
            'Completed': 0
          },
          collaborationMetrics: {
            avgCommentsPerDocument: 0,
            avgRevisionsPerDocument: 0,
            documentsWithGoogleDocs: documentsWithGoogleDocs || 0
          }
        },
        systemHealth: {
          uptime: '99.9%',
          responseTime: '245ms',
          errorRate: '0.1%',
          activeUsers: totalUsers || 0
        }
      };

      return createStandardSuccessResponse(
        analyticsData,
        'Analytics data retrieved successfully'
      );
    });
  }
);
