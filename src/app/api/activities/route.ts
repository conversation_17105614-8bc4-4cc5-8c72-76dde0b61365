import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  validateRequiredFields
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { User } from '@/types/application';

interface Activity {
  id?: string;
  student_id: string;

  // Common App structure fields
  activity_type: string;
  organization_name: string;
  position_title?: string;
  description?: string; // Max 150 words
  participation_grade_levels?: string[];
  hours_per_week: number;
  weeks_per_year: number;
  leadership_role?: boolean;
  awards_recognition?: string;
  file_uploads?: {
    certificates: string[];
    videos: string[];
    photos: string[];
  };

  // Legacy fields for backward compatibility
  name?: string;
  category?: string;
  organization?: string;
  position?: string;
  yearsActive?: string;
  hoursPerWeek?: number;
  weeksPerYear?: number;
  achievements?: string[];

  created_at?: string;
  updated_at?: string;
}

// GET /api/activities - Get activities for current user
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // Get student profile ID for current user
      const { data: studentProfile } = await supabaseAdmin
        .from('student_profiles')
        .select('id')
        .eq('user_id', currentUser.id)
        .single();

      if (!studentProfile) {
        return createStandardSuccessResponse(
          [],
          'No student profile found',
          200,
          {
            user_role: currentUser.role,
            operation_type: 'read',
            resource_type: 'activities'
          }
        );
      }

      const { data: activities, error } = await supabaseAdmin
        .from('activities')
        .select('*')
        .eq('student_id', studentProfile.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        activities || [],
        'Activities retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'activities',
          count: activities?.length || 0
        }
      );
    }, 'Failed to retrieve activities');
  }
);

// POST /api/activities - Create new activity
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        // Common App structure fields
        activity_type?: string;
        organization_name?: string;
        position_title?: string;
        description?: string;
        participation_grade_levels?: string[];
        hours_per_week?: number;
        weeks_per_year?: number;
        leadership_role?: boolean;
        awards_recognition?: string;
        file_uploads?: {
          certificates: string[];
          videos: string[];
          photos: string[];
        };

        // Legacy fields for backward compatibility
        name?: string;
        category?: string;
        organization?: string;
        position?: string;
        yearsActive?: string;
        hoursPerWeek?: number;
        weeksPerYear?: number;
        achievements?: string[];
      }>(request);

      // Support both new and legacy field formats
      const activityType = body.activity_type || body.category;
      const organizationName = body.organization_name || body.organization || body.name;
      const hoursPerWeek = body.hours_per_week || body.hoursPerWeek;
      const weeksPerYear = body.weeks_per_year || body.weeksPerYear;

      validateRequiredFields({
        activity_type: activityType,
        organization_name: organizationName,
        hours_per_week: hoursPerWeek,
        weeks_per_year: weeksPerYear
      }, ['activity_type', 'organization_name', 'hours_per_week', 'weeks_per_year']);

      // Get student profile ID for current user
      const { data: studentProfile } = await supabaseAdmin
        .from('student_profiles')
        .select('id')
        .eq('user_id', currentUser.id)
        .single();

      if (!studentProfile) {
        throw new Error('Student profile not found');
      }

      const activityData = {
        student_id: studentProfile.id,

        // Common App structure fields
        activity_type: activityType,
        organization_name: organizationName,
        position_title: body.position_title || body.position,
        description: body.description,
        participation_grade_levels: body.participation_grade_levels || [],
        hours_per_week: hoursPerWeek,
        weeks_per_year: weeksPerYear,
        leadership_role: body.leadership_role || false,
        awards_recognition: body.awards_recognition,
        file_uploads: body.file_uploads || {
          certificates: [],
          videos: [],
          photos: []
        },

        // Legacy fields for backward compatibility
        name: body.name || `${organizationName}${body.position_title || body.position ? ` - ${body.position_title || body.position}` : ''}`,
        category: body.category || activityType,
        organization: body.organization || organizationName,
        position: body.position || body.position_title,
        years_active: body.yearsActive,
        achievements: body.achievements || (body.awards_recognition ? [body.awards_recognition] : [])
      };

      const { data: activity, error } = await supabaseAdmin
        .from('activities')
        .insert(activityData)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        activity,
        'Activity created successfully',
        201,
        {
          user_role: currentUser.role,
          operation_type: 'create',
          resource_type: 'activity',
          activity_id: activity.id
        }
      );
    }, 'Failed to create activity');
  }
);
