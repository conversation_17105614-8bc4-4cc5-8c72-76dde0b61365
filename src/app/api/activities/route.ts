import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  parseRequestBody,
  validateRequiredFields
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { User } from '@/types/application';

interface Activity {
  id?: string;
  student_id: string;
  name: string;
  category: string;
  organization?: string;
  position?: string;
  yearsActive?: string;
  hoursPerWeek: number;
  weeksPerYear: number;
  description?: string;
  achievements?: string[];
  created_at?: string;
  updated_at?: string;
}

// GET /api/activities - Get activities for current user
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // Get student profile ID for current user
      const { data: studentProfile } = await supabaseAdmin
        .from('student_profiles')
        .select('id')
        .eq('user_id', currentUser.id)
        .single();

      if (!studentProfile) {
        return createStandardSuccessResponse(
          [],
          'No student profile found',
          200,
          {
            user_role: currentUser.role,
            operation_type: 'read',
            resource_type: 'activities'
          }
        );
      }

      const { data: activities, error } = await supabaseAdmin
        .from('activities')
        .select('*')
        .eq('student_id', studentProfile.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        activities || [],
        'Activities retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          operation_type: 'read',
          resource_type: 'activities',
          count: activities?.length || 0
        }
      );
    }, 'Failed to retrieve activities');
  }
);

// POST /api/activities - Create new activity
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        name: string;
        category: string;
        organization?: string;
        position?: string;
        yearsActive?: string;
        hoursPerWeek: number;
        weeksPerYear: number;
        description?: string;
        achievements?: string[];
      }>(request);

      validateRequiredFields(body, ['name', 'category', 'hoursPerWeek', 'weeksPerYear']);

      // Get student profile ID for current user
      const { data: studentProfile } = await supabaseAdmin
        .from('student_profiles')
        .select('id')
        .eq('user_id', currentUser.id)
        .single();

      if (!studentProfile) {
        throw new Error('Student profile not found');
      }

      const activityData: Omit<Activity, 'id' | 'created_at' | 'updated_at'> = {
        student_id: studentProfile.id,
        name: body.name,
        category: body.category,
        organization: body.organization,
        position: body.position,
        yearsActive: body.yearsActive,
        hoursPerWeek: body.hoursPerWeek,
        weeksPerYear: body.weeksPerYear,
        description: body.description,
        achievements: body.achievements || []
      };

      const { data: activity, error } = await supabaseAdmin
        .from('activities')
        .insert(activityData)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        activity,
        'Activity created successfully',
        201,
        {
          user_role: currentUser.role,
          operation_type: 'create',
          resource_type: 'activity',
          activity_id: activity.id
        }
      );
    }, 'Failed to create activity');
  }
);
