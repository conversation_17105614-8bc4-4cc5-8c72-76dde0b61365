import { NextRequest } from 'next/server';
import { withRole, createSuccessResponse } from '@/lib/api-utils';
import { TemplateService } from '@/lib/template-service';

// GET /api/templates/analytics - Get template analytics (Admin only)
export const GET = withRole(['admin'], async (request: NextRequest) => {
  try {
    const analytics = await TemplateService.getTemplateAnalytics();

    return createSuccessResponse(
      analytics,
      'Template analytics retrieved successfully'
    );
  } catch (error) {
    console.error('Error getting template analytics:', error);
    throw error;
  }
});
