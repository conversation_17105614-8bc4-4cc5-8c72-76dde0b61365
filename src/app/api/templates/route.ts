import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  withStandardRole,
  createStandardSuccessResponse,
  parseRequestBody,
  validateRequiredFields,
  handleAsyncOperation
} from '@/lib/api-utils';
import { GoogleDriveService, GoogleDocsService } from '@/lib/google-apis';
import { supabase } from '@/lib/supabase';
import type { User } from '@/types/application';

// GET /api/templates - List available templates with enhanced metadata
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const url = new URL(request.url);
      const docType = url.searchParams.get('doc_type');
      const category = url.searchParams.get('category');

      // Get templates from Google Drive
      const googleTemplates = await GoogleDriveService.getTemplates();

      // Get template metadata from database
      const { data: templateMetadata } = await supabase
        .from('template_metadata')
        .select('*')
        .order('usage_count', { ascending: false });

      // Combine Google Drive templates with database metadata
      const enhancedTemplates = googleTemplates.map((template) => {
        const metadata = templateMetadata?.find(
          (meta) => meta.google_doc_id === template.id
        );

        return {
          id: template.id,
          name: template.name,
          type: template.type,
          description: metadata?.description || '',
          category: metadata?.category || 'general',
          doc_type: metadata?.doc_type || 'other',
          usage_count: metadata?.usage_count || 0,
          word_limit: metadata?.word_limit,
          tags: metadata?.tags || [],
          preview_url: `https://docs.google.com/document/d/${template.id}/preview`,
          created_at: metadata?.created_at,
          updated_at: metadata?.updated_at,
          is_featured: metadata?.is_featured || false,
          difficulty_level: metadata?.difficulty_level || 'intermediate'
        };
      });

      // Apply filters
      let filteredTemplates = enhancedTemplates;

      if (docType) {
        filteredTemplates = filteredTemplates.filter(
          (template) => template.doc_type === docType
        );
      }

      if (category) {
        filteredTemplates = filteredTemplates.filter(
          (template) => template.category === category
        );
      }

      // Group templates by category for better organization
      const templatesByCategory = filteredTemplates.reduce(
        (acc, template) => {
          const cat = template.category;
          if (!acc[cat]) {
            acc[cat] = [];
          }
          acc[cat].push(template);
          return acc;
        },
        {} as Record<string, typeof filteredTemplates>
      );

      // Get template statistics
      const stats = {
        total_templates: enhancedTemplates.length,
        filtered_templates: filteredTemplates.length,
        categories: Object.keys(templatesByCategory),
        most_used: enhancedTemplates
          .sort((a, b) => b.usage_count - a.usage_count)
          .slice(0, 5),
        featured: enhancedTemplates.filter((t) => t.is_featured)
      };

      return createStandardSuccessResponse(
        {
          templates: filteredTemplates,
          templates_by_category: templatesByCategory,
          statistics: stats
        },
        'Templates retrieved successfully',
        200,
        {
          total_templates: enhancedTemplates.length,
          filtered_count: filteredTemplates.length,
          applied_filters: {
            doc_type: docType,
            category: category
          },
          user_role: currentUser.role
        }
      );
    }, 'Failed to retrieve templates');
  }
);

// POST /api/templates - Create new template (Admin only)
export const POST = withStandardRole(
  ['admin'],
  async (request: NextRequest) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        name: string;
        content: string;
        doc_type:
          | 'essay'
          | 'personal_statement'
          | 'transcript'
          | 'activity_resume'
          | 'other';
        category: string;
        description?: string;
        word_limit?: number;
        tags?: string[];
        difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
        is_featured?: boolean;
      }>(request);

      validateRequiredFields(body, ['name', 'content', 'doc_type', 'category']);

      // Create Google Doc template
      const { documentId, documentUrl } =
        await GoogleDocsService.createDocument(
          body.name,
          'Template',
          body.doc_type,
          body.content
        );

      // Move to templates folder
      const templatesFolderId =
        await GoogleDriveService.getOrCreateTemplatesFolder();

      // Move the document to the templates folder
      await GoogleDriveService.moveToFolder(documentId, templatesFolderId);

      // Store metadata in database
      const { data: templateMetadata, error } = await supabase
        .from('template_metadata')
        .insert({
          google_doc_id: documentId,
          name: body.name,
          doc_type: body.doc_type,
          category: body.category,
          description: body.description || '',
          word_limit: body.word_limit,
          tags: body.tags || [],
          difficulty_level: body.difficulty_level || 'intermediate',
          is_featured: body.is_featured || false,
          usage_count: 0
        })
        .select()
        .single();

      if (error) {
        console.error('Error storing template metadata:', error);
        // Continue even if metadata storage fails - the Google Doc was created successfully
      }

      return createStandardSuccessResponse(
        {
          template_id: documentId,
          template_url: documentUrl,
          metadata: templateMetadata
        },
        'Template created successfully',
        201,
        {
          google_doc_created: true,
          moved_to_templates_folder: true,
          metadata_stored: !error
        }
      );
    }, 'Failed to create template');
  }
);
