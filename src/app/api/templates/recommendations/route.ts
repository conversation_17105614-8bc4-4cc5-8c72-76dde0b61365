import { NextRequest } from 'next/server';
import {
  withAuth,
  createSuccessResponse,
  AuthorizationError
} from '@/lib/api-utils';
import { TemplateService } from '@/lib/template-service';
import { supabase, supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

// GET /api/templates/recommendations - Get template recommendations for a student
export const GET = withAuth(async (request: NextRequest, currentUser: User) => {
  try {
    const url = new URL(request.url);
    const studentId = url.searchParams.get('student_id');
    const docType = url.searchParams.get('doc_type') as
      | 'essay'
      | 'personal_statement'
      | 'transcript'
      | 'activity_resume'
      | 'other';
    const limit = parseInt(url.searchParams.get('limit') || '5');

    if (!studentId) {
      throw new Error('student_id query parameter is required');
    }

    if (!docType) {
      throw new Error('doc_type query parameter is required');
    }

    // Permission check
    if (currentUser.role === 'student') {
      // Students can only get recommendations for themselves
      const { data: studentProfile } = await supabaseAdmin
        .from('student_profiles')
        .select('user_id')
        .eq('id', studentId)
        .single();

      if (!studentProfile || studentProfile.user_id !== currentUser.id) {
        throw new AuthorizationError(
          'You can only get recommendations for yourself'
        );
      }
    } else if (currentUser.role === 'consultant') {
      // Consultants can get recommendations for their assigned students
      const { data: consultant } = await supabaseAdmin
        .from('consultants')
        .select('id')
        .eq('user_id', currentUser.id)
        .single();

      if (consultant) {
        const { data: relation } = await supabaseAdmin
          .from('student_consultant_relations')
          .select('id')
          .eq('student_id', studentId)
          .eq('consultant_id', consultant.id)
          .eq('status', 'active')
          .single();

        if (!relation) {
          throw new AuthorizationError(
            'You can only get recommendations for your assigned students'
          );
        }
      } else {
        throw new AuthorizationError('Consultant profile not found');
      }
    }
    // Admins can get recommendations for any student

    const recommendations = await TemplateService.getTemplateRecommendations(
      studentId,
      docType,
      limit
    );

    // Get additional context for the recommendations
    const { data: studentProfile } = await supabase
      .from('student_profiles')
      .select(
        `
        academic_info,
        target_schools,
        users!inner(profile_data)
      `
      )
      .eq('id', studentId)
      .single();

    // Get student's document history for this type
    const { data: existingDocuments } = await supabase
      .from('documents')
      .select('id, metadata, created_at')
      .eq('student_id', studentId)
      .eq('doc_type', docType)
      .order('created_at', { ascending: false });

    const context = {
      student_name: studentProfile?.users[0]?.profile_data?.name || 'Student',
      academic_level: studentProfile?.academic_info?.grade_level || 'Unknown',
      target_school_count: Array.isArray(studentProfile?.target_schools)
        ? studentProfile.target_schools.length
        : 0,
      existing_documents_count: existingDocuments?.length || 0,
      last_document_created: existingDocuments?.[0]?.created_at || null
    };

    return createSuccessResponse(
      {
        recommendations,
        context,
        request_params: {
          student_id: studentId,
          doc_type: docType,
          limit
        }
      },
      'Template recommendations retrieved successfully'
    );
  } catch (error) {
    console.error('Error getting template recommendations:', error);
    throw error;
  }
});
