import { NextRequest } from 'next/server';
import { withAuth, createSuccessResponse } from '@/lib/api-utils';
import { supabase } from '@/lib/supabase';
import type { User } from '@/types/application';

// GET /api/debug/profile - Debug current user profile setup
export const GET = withAuth(async (request: NextRequest, currentUser: User) => {
  try {
    // Get user with all related data
    const { data: userWithProfile, error: userError } = await supabase
      .from('users')
      .select(
        `
        *,
        student_profiles(*),
        consultants(*)
      `
      )
      .eq('id', currentUser.id)
      .single();

    if (userError) {
      throw userError;
    }

    // Get student profile separately if user is a student
    let studentProfile = null;
    if (currentUser.role === 'student') {
      const { data: profile, error: profileError } = await supabase
        .from('student_profiles')
        .select('*')
        .eq('user_id', currentUser.id)
        .single();

      studentProfile = profile;
      if (profileError) {
        console.log('Student profile error:', profileError);
      }
    }

    const debugInfo = {
      currentUser,
      userWithProfile,
      studentProfile,
      hasStudentProfile: !!studentProfile,
      studentProfileId: studentProfile?.id,
      profileSetupComplete:
        currentUser.role === 'student' ? !!studentProfile?.id : true,
      timestamp: new Date().toISOString()
    };

    return createSuccessResponse(
      debugInfo,
      'Debug information retrieved successfully'
    );
  } catch (error) {
    console.error('Debug profile error:', error);
    throw error;
  }
});
