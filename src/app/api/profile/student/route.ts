import { NextRequest } from 'next/server';
import {
  withStandardAuth,
  createStandardSuccessResponse,
  handleAsyncOperation,
  AuthorizationError
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

// GET /api/profile/student - Get current user's student profile
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      // Only students can access their own profile through this endpoint
      if (currentUser.role !== 'student') {
        throw new AuthorizationError(
          'Only students can access student profile endpoint'
        );
      }

      // Get student profile
      const { data: studentProfile, error } = await supabaseAdmin
        .from('student_profiles')
        .select('id, user_id, academic_info, created_at, updated_at')
        .eq('user_id', currentUser.id)
        .single();

      if (error || !studentProfile) {
        throw new Error('Student profile not found');
      }

      return createStandardSuccessResponse(
        studentProfile,
        'Student profile retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          user_id: currentUser.id,
          student_id: studentProfile.id
        }
      );
    });
  }
);
