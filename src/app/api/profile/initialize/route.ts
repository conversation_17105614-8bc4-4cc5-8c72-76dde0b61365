import { NextRequest } from 'next/server';
import {
  withAuth,
  createSuccessResponse,
  createErrorResponse
} from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import type { User } from '@/types/application';

// POST /api/profile/initialize - Initialize user profile if missing
export const POST = withAuth(
  async (request: NextRequest, currentUser: User) => {
    try {
      // Check if profile already exists
      if (currentUser.role === 'student') {
        const { data: existingProfile } = await supabaseAdmin
          .from('student_profiles')
          .select('id')
          .eq('user_id', currentUser.id)
          .single();

        if (existingProfile) {
          return createSuccessResponse(
            { profile_id: existingProfile.id, already_exists: true },
            'Student profile already exists'
          );
        }

        // Create student profile
        const { data: newProfile, error: profileError } = await supabaseAdmin
          .from('student_profiles')
          .insert({
            user_id: currentUser.id,
            academic_info: {},
            target_schools: [],
            application_status: {}
          })
          .select()
          .single();

        if (profileError) {
          console.error('Error creating student profile:', profileError);
          throw new Error(
            `Failed to create student profile: ${profileError.message}`
          );
        }

        return createSuccessResponse(
          { profile_id: newProfile.id, created: true },
          'Student profile created successfully'
        );
      } else if (currentUser.role === 'consultant') {
        const { data: existingProfile } = await supabaseAdmin
          .from('consultants')
          .select('id')
          .eq('user_id', currentUser.id)
          .single();

        if (existingProfile) {
          return createSuccessResponse(
            { profile_id: existingProfile.id, already_exists: true },
            'Consultant profile already exists'
          );
        }

        // Create consultant profile
        const { data: newProfile, error: profileError } = await supabaseAdmin
          .from('consultants')
          .insert({
            user_id: currentUser.id,
            specialties: [],
            availability: {}
          })
          .select()
          .single();

        if (profileError) {
          console.error('Error creating consultant profile:', profileError);
          throw new Error(
            `Failed to create consultant profile: ${profileError.message}`
          );
        }

        return createSuccessResponse(
          { profile_id: newProfile.id, created: true },
          'Consultant profile created successfully'
        );
      } else {
        // Admin users don't need additional profiles
        return createSuccessResponse(
          { no_profile_needed: true },
          'Admin users do not require additional profiles'
        );
      }
    } catch (error) {
      console.error('Profile initialization error:', error);
      return createErrorResponse(
        error instanceof Error ? error.message : 'Unknown error occurred',
        500
      );
    }
  }
);
