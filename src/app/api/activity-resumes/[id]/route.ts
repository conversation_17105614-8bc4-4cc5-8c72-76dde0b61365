import { NextRequest } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { withStandardAuth } from '@/lib/auth-middleware';
import { handleAsyncOperation, createStandardSuccessResponse } from '@/lib/api-utils';
import { parseRequestBody, validateRequiredFields } from '@/lib/request-utils';
import { User } from '@/types/auth';

interface RouteContext {
  params: Promise<{ id: string }>;
}

// Helper function to check if user can access this resume
async function canAccessResume(currentUser: User, resumeId: string): Promise<boolean> {
  if (currentUser.role === 'admin') {
    return true;
  }

  const { data: resume } = await supabaseAdmin
    .from('activity_resumes')
    .select(`
      student_id,
      student_profiles!inner(
        user_id,
        student_consultant_relations(
          consultant_id,
          status
        )
      )
    `)
    .eq('id', resumeId)
    .single();

  if (!resume) {
    return false;
  }

  if (currentUser.role === 'student') {
    return resume.student_profiles.user_id === currentUser.id;
  }

  if (currentUser.role === 'consultant') {
    const relations = resume.student_profiles.student_consultant_relations || [];
    return relations.some(
      (rel: any) => rel.consultant_id === currentUser.profile?.id && rel.status === 'active'
    );
  }

  return false;
}

// GET /api/activity-resumes/[id] - Get specific activity resume
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = await context.params;

      if (!(await canAccessResume(currentUser, id))) {
        throw new Error('You do not have access to this activity resume');
      }

      const { data: resume, error } = await supabaseAdmin
        .from('activity_resumes')
        .select(`
          *,
          student_profiles!inner(
            id,
            first_name,
            last_name,
            users!inner(email)
          )
        `)
        .eq('id', id)
        .single();

      if (error) {
        throw error;
      }

      // Get the actual activity details for selected activities
      if (resume.selected_activities && resume.selected_activities.length > 0) {
        const { data: activities } = await supabaseAdmin
          .from('activities')
          .select('*')
          .in('id', resume.selected_activities)
          .order('created_at', { ascending: false });

        resume.activities = activities || [];
      } else {
        resume.activities = [];
      }

      return createStandardSuccessResponse(
        resume,
        'Activity resume retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          activity_count: resume.activities?.length || 0
        }
      );
    }, 'Failed to retrieve activity resume');
  }
);

// PUT /api/activity-resumes/[id] - Update activity resume
export const PUT = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = await context.params;

      if (!(await canAccessResume(currentUser, id))) {
        throw new Error('You do not have access to this activity resume');
      }

      const body = await parseRequestBody<{
        title?: string;
        description?: string;
        focus_area?: string;
        selected_activities?: string[];
        template_style?: string;
        is_default?: boolean;
        file_url?: string;
        google_doc_id?: string;
        google_doc_url?: string;
      }>(request);

      // Get current resume to validate student_id for activity validation
      const { data: currentResume } = await supabaseAdmin
        .from('activity_resumes')
        .select('student_id')
        .eq('id', id)
        .single();

      if (!currentResume) {
        throw new Error('Activity resume not found');
      }

      // Validate selected activities if provided
      if (body.selected_activities && body.selected_activities.length > 0) {
        const { data: activities } = await supabaseAdmin
          .from('activities')
          .select('id')
          .eq('student_id', currentResume.student_id)
          .in('id', body.selected_activities);

        const validActivityIds = activities?.map(a => a.id) || [];
        const invalidIds = body.selected_activities.filter(id => !validActivityIds.includes(id));

        if (invalidIds.length > 0) {
          throw new Error(`Invalid activity IDs: ${invalidIds.join(', ')}`);
        }
      }

      // If setting as default, unset other defaults for this student
      if (body.is_default) {
        await supabaseAdmin
          .from('activity_resumes')
          .update({ is_default: false })
          .eq('student_id', currentResume.student_id)
          .neq('id', id);
      }

      const { data: resume, error } = await supabaseAdmin
        .from('activity_resumes')
        .update({
          ...body,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select(`
          *,
          student_profiles!inner(
            id,
            first_name,
            last_name,
            users!inner(email)
          )
        `)
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        resume,
        'Activity resume updated successfully',
        200,
        {
          user_role: currentUser.role,
          is_default: body.is_default || false
        }
      );
    }, 'Failed to update activity resume');
  }
);

// DELETE /api/activity-resumes/[id] - Delete activity resume
export const DELETE = withStandardAuth(
  async (request: NextRequest, currentUser: User, context: RouteContext) => {
    return handleAsyncOperation(async () => {
      const { id } = await context.params;

      if (!(await canAccessResume(currentUser, id))) {
        throw new Error('You do not have access to this activity resume');
      }

      const { error } = await supabaseAdmin
        .from('activity_resumes')
        .delete()
        .eq('id', id);

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        { id },
        'Activity resume deleted successfully',
        200,
        {
          user_role: currentUser.role
        }
      );
    }, 'Failed to delete activity resume');
  }
);
