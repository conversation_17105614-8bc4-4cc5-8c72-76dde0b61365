import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { withStandardAuth } from '@/lib/auth-middleware';
import { handleAsyncOperation, createStandardSuccessResponse } from '@/lib/api-utils';
import { parseRequestBody, validateRequiredFields } from '@/lib/request-utils';
import { User } from '@/types/auth';

interface ActivityResume {
  id: string;
  student_id: string;
  title: string;
  description?: string;
  focus_area: string;
  selected_activities: string[];
  template_style: string;
  file_url?: string;
  google_doc_id?: string;
  google_doc_url?: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

// GET /api/activity-resumes - Get activity resumes for current user or specified student
export const GET = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const url = new URL(request.url);
      const studentId = url.searchParams.get('student_id');

      let query = supabaseAdmin
        .from('activity_resumes')
        .select(`
          *,
          student_profiles!inner(
            id,
            first_name,
            last_name,
            users!inner(email)
          )
        `)
        .order('created_at', { ascending: false });

      // Apply filters based on user role and student_id parameter
      if (currentUser.role === 'student') {
        // Students can only see their own resumes
        const { data: studentProfile } = await supabaseAdmin
          .from('student_profiles')
          .select('id')
          .eq('user_id', currentUser.id)
          .single();

        if (!studentProfile) {
          return createStandardSuccessResponse([], 'No student profile found', 200);
        }

        query = query.eq('student_id', studentProfile.id);
      } else if (currentUser.role === 'consultant') {
        // Consultants can see resumes of their assigned students
        if (studentId) {
          // Check if consultant has access to this student
          const { data: relation } = await supabaseAdmin
            .from('student_consultant_relations')
            .select('id')
            .eq('student_id', studentId)
            .eq('consultant_id', currentUser.profile?.id)
            .eq('status', 'active')
            .single();

          if (!relation) {
            return createStandardSuccessResponse([], 'No access to this student', 403);
          }

          query = query.eq('student_id', studentId);
        } else {
          // Get all students assigned to this consultant
          const { data: relations } = await supabaseAdmin
            .from('student_consultant_relations')
            .select('student_id')
            .eq('consultant_id', currentUser.profile?.id)
            .eq('status', 'active');

          const studentIds = relations?.map(r => r.student_id) || [];
          if (studentIds.length === 0) {
            return createStandardSuccessResponse([], 'No assigned students', 200);
          }

          query = query.in('student_id', studentIds);
        }
      } else if (currentUser.role === 'admin') {
        // Admins can see all resumes, optionally filtered by student_id
        if (studentId) {
          query = query.eq('student_id', studentId);
        }
      }

      const { data: resumes, error } = await query;

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        resumes || [],
        'Activity resumes retrieved successfully',
        200,
        {
          user_role: currentUser.role,
          resume_count: resumes?.length || 0,
          student_filter: studentId || 'all'
        }
      );
    }, 'Failed to retrieve activity resumes');
  }
);

// POST /api/activity-resumes - Create new activity resume
export const POST = withStandardAuth(
  async (request: NextRequest, currentUser: User) => {
    return handleAsyncOperation(async () => {
      const body = await parseRequestBody<{
        title: string;
        description?: string;
        focus_area: string;
        selected_activities: string[];
        template_style?: string;
        template_metadata?: any; // Enhanced template data
        is_default?: boolean;
        student_id?: string; // For consultants/admins creating for students
      }>(request);

      validateRequiredFields(body, ['title', 'focus_area', 'selected_activities']);

      // Determine the student_id
      let effectiveStudentId: string;

      if (currentUser.role === 'student') {
        // Students can only create for themselves
        const { data: studentProfile } = await supabaseAdmin
          .from('student_profiles')
          .select('id')
          .eq('user_id', currentUser.id)
          .single();

        if (!studentProfile) {
          throw new Error('Student profile not found');
        }

        effectiveStudentId = studentProfile.id;
      } else if (currentUser.role === 'consultant' || currentUser.role === 'admin') {
        // Consultants and admins can create for specified students
        if (!body.student_id) {
          throw new Error('student_id is required for consultants and admins');
        }

        if (currentUser.role === 'consultant') {
          // Verify consultant has access to this student
          const { data: relation } = await supabaseAdmin
            .from('student_consultant_relations')
            .select('id')
            .eq('student_id', body.student_id)
            .eq('consultant_id', currentUser.profile?.id)
            .eq('status', 'active')
            .single();

          if (!relation) {
            throw new Error('You do not have access to create resumes for this student');
          }
        }

        effectiveStudentId = body.student_id;
      } else {
        throw new Error('Invalid user role');
      }

      // Validate that selected activities belong to the student
      if (body.selected_activities.length > 0) {
        const { data: activities } = await supabaseAdmin
          .from('activities')
          .select('id')
          .eq('student_id', effectiveStudentId)
          .in('id', body.selected_activities);

        const validActivityIds = activities?.map(a => a.id) || [];
        const invalidIds = body.selected_activities.filter(id => !validActivityIds.includes(id));

        if (invalidIds.length > 0) {
          throw new Error(`Invalid activity IDs: ${invalidIds.join(', ')}`);
        }
      }

      // If this is set as default, unset other defaults for this student
      if (body.is_default) {
        await supabaseAdmin
          .from('activity_resumes')
          .update({ is_default: false })
          .eq('student_id', effectiveStudentId);
      }

      const { data: resume, error } = await supabaseAdmin
        .from('activity_resumes')
        .insert({
          student_id: effectiveStudentId,
          title: body.title,
          description: body.description,
          focus_area: body.focus_area,
          selected_activities: body.selected_activities,
          template_style: body.template_style || 'standard',
          template_metadata: body.template_metadata || null,
          is_default: body.is_default || false
        })
        .select(`
          *,
          student_profiles!inner(
            id,
            first_name,
            last_name,
            users!inner(email)
          )
        `)
        .single();

      if (error) {
        throw error;
      }

      return createStandardSuccessResponse(
        resume,
        'Activity resume created successfully',
        201,
        {
          user_role: currentUser.role,
          student_id: effectiveStudentId,
          is_default: body.is_default || false
        }
      );
    }, 'Failed to create activity resume');
  }
);
