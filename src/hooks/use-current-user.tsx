'use client';

import { useUser } from '@clerk/nextjs';
import { useEffect, useState, useCallback, useRef } from 'react';
import type { User } from '@/types/application';
import { log } from '@/lib/logger';

interface CurrentUserData extends User {
  profile?: any;
}

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 5000 // 5 seconds
};

export function useCurrentUser() {
  const { user: clerkUser, isLoaded } = useUser();
  const [userData, setUserData] = useState<CurrentUserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [lastKnownRole, setLastKnownRole] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Health check to test API connectivity
  const checkApiHealth = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/health', {
        method: 'GET',
        headers: { 'Cache-Control': 'no-cache' }
      });

      const result = await response.json();
      return result.success && result.status === 'healthy';
    } catch (error) {
      log.warn('Health check failed', 'AUTH', error);
      return false;
    }
  }, []);

  // Enhanced fetch with retry logic and timeout
  const fetchUserDataWithRetry = useCallback(
    async (attempt: number = 0): Promise<void> => {
      // Cancel any previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller for this request
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      try {
        // Add timeout to prevent hanging requests
        const timeoutId = setTimeout(() => {
          abortController.abort();
        }, 10000); // 10 second timeout

        const response = await fetch('/api/users/me', {
          signal: abortController.signal,
          headers: {
            'Cache-Control': 'no-cache'
          }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success && result.data) {
          setUserData(result.data);
          setLastKnownRole(result.data.role); // Track the last known role
          setError(null);
          setRetryCount(0);
          return;
        } else {
          throw new Error(
            result.error?.message || result.error || 'Invalid response format'
          );
        }
      } catch (err) {
        // Don't retry if request was aborted
        if (err instanceof Error && err.name === 'AbortError') {
          return;
        }

        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error';
        log.warn(
          `User data fetch attempt ${attempt + 1} failed: ${errorMessage}`,
          'AUTH'
        );

        // On first failure, check API health to provide better error context
        if (attempt === 0) {
          const isHealthy = await checkApiHealth();
          if (!isHealthy) {
            setError(
              'Service temporarily unavailable. Please try again in a moment.'
            );
            setRetryCount(0);
            return;
          }
        }

        // Retry logic
        if (attempt < RETRY_CONFIG.maxRetries) {
          const delay = Math.min(
            RETRY_CONFIG.baseDelay * Math.pow(2, attempt),
            RETRY_CONFIG.maxDelay
          );

          setRetryCount(attempt + 1);

          timeoutRef.current = setTimeout(() => {
            fetchUserDataWithRetry(attempt + 1);
          }, delay);

          return;
        }

        // All retries exhausted
        setError(
          `Failed to load user data after ${RETRY_CONFIG.maxRetries + 1} attempts: ${errorMessage}`
        );
        setRetryCount(0);
      }
    },
    [checkApiHealth]
  );

  const fetchUserData = useCallback(async () => {
    // Reset state
    setError(null);
    setRetryCount(0);

    // Clear any existing timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    if (!isLoaded) {
      setLoading(true);
      return;
    }

    if (!clerkUser) {
      setLoading(false);
      setUserData(null);
      setLastKnownRole(null); // Clear last known role when user logs out
      return;
    }

    setLoading(true);
    await fetchUserDataWithRetry(0);
    setLoading(false);
  }, [clerkUser, isLoaded, fetchUserDataWithRetry]);

  useEffect(() => {
    fetchUserData();

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [fetchUserData]);

  const refetch = useCallback(() => {
    setLoading(true);
    setError(null);
    setUserData(null);
    fetchUserData();
  }, [fetchUserData]);

  return {
    user: userData,
    loading: loading || !isLoaded,
    error,
    retryCount,
    lastKnownRole, // Expose last known role for navigation consistency
    isStudent: userData?.role === 'student',
    isConsultant: userData?.role === 'consultant',
    isAdmin: userData?.role === 'admin',
    refetch
  };
}
