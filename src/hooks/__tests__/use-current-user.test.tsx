import { renderHook, waitFor } from '@testing-library/react';
import { useUser } from '@clerk/nextjs';
import { useCurrentUser } from '../use-current-user';

// Mock the Clerk hook
jest.mock('@clerk/nextjs');
const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;

// Mock fetch globally
global.fetch = jest.fn();
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

// Mock logger
jest.mock('@/lib/logger', () => ({
  log: {
    warn: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

describe('useCurrentUser', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  const mockClerkUser = {
    id: 'clerk-user-id',
    emailAddresses: [{ emailAddress: '<EMAIL>' }],
    firstName: 'Test',
    lastName: 'User',
  };

  const mockUserData = {
    id: 'user-id',
    email: '<EMAIL>',
    role: 'student',
    firstName: 'Test',
    lastName: 'User',
  };

  it('should return loading state initially', () => {
    mockUseUser.mockReturnValue({
      user: null,
      isLoaded: false,
    });

    const { result } = renderHook(() => useCurrentUser());

    expect(result.current.loading).toBe(true);
    expect(result.current.user).toBe(null);
    expect(result.current.error).toBe(null);
  });

  it('should return null user when not authenticated', async () => {
    mockUseUser.mockReturnValue({
      user: null,
      isLoaded: true,
    });

    const { result } = renderHook(() => useCurrentUser());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.user).toBe(null);
      expect(result.current.lastKnownRole).toBe(null);
    });
  });

  it('should fetch user data successfully', async () => {
    mockUseUser.mockReturnValue({
      user: mockClerkUser,
      isLoaded: true,
    });

    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: mockUserData,
        }),
      } as Response);

    const { result } = renderHook(() => useCurrentUser());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.user).toEqual(mockUserData);
      expect(result.current.lastKnownRole).toBe('student');
      expect(result.current.isStudent).toBe(true);
      expect(result.current.isConsultant).toBe(false);
      expect(result.current.isAdmin).toBe(false);
    });

    expect(mockFetch).toHaveBeenCalledWith('/api/users/me', {
      signal: expect.any(AbortSignal),
      headers: {
        'Cache-Control': 'no-cache',
      },
    });
  });

  it('should handle API errors with retry logic', async () => {
    mockUseUser.mockReturnValue({
      user: mockClerkUser,
      isLoaded: true,
    });

    // Mock health check to return healthy
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          status: 'healthy',
        }),
      } as Response)
      // First attempt fails
      .mockRejectedValueOnce(new Error('Network error'))
      // Health check again
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          status: 'healthy',
        }),
      } as Response)
      // Second attempt succeeds
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: mockUserData,
        }),
      } as Response);

    const { result } = renderHook(() => useCurrentUser());

    // Initially loading
    expect(result.current.loading).toBe(true);
    expect(result.current.retryCount).toBe(0);

    // Wait for first failure and retry
    await waitFor(() => {
      expect(result.current.retryCount).toBe(1);
    });

    // Fast-forward timers to trigger retry
    jest.advanceTimersByTime(1000);

    // Wait for successful retry
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.user).toEqual(mockUserData);
      expect(result.current.error).toBe(null);
      expect(result.current.retryCount).toBe(0);
    });
  });

  it('should handle maximum retries exceeded', async () => {
    mockUseUser.mockReturnValue({
      user: mockClerkUser,
      isLoaded: true,
    });

    // Mock health check to return healthy
    mockFetch
      .mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          status: 'healthy',
        }),
      } as Response)
      // All user data requests fail
      .mockRejectedValue(new Error('Persistent network error'));

    const { result } = renderHook(() => useCurrentUser());

    // Fast-forward through all retries
    for (let i = 0; i < 4; i++) {
      jest.advanceTimersByTime(5000);
      await waitFor(() => {});
    }

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toContain('Failed to load user data after 4 attempts');
      expect(result.current.retryCount).toBe(0);
    });
  });

  it('should handle unhealthy API', async () => {
    mockUseUser.mockReturnValue({
      user: mockClerkUser,
      isLoaded: true,
    });

    // Mock health check to return unhealthy
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: false,
          status: 'unhealthy',
        }),
      } as Response)
      // User data request fails
      .mockRejectedValueOnce(new Error('Service unavailable'));

    const { result } = renderHook(() => useCurrentUser());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe('Service temporarily unavailable. Please try again in a moment.');
    });
  });

  it('should provide refetch functionality', async () => {
    mockUseUser.mockReturnValue({
      user: mockClerkUser,
      isLoaded: true,
    });

    mockFetch
      .mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: mockUserData,
        }),
      } as Response);

    const { result } = renderHook(() => useCurrentUser());

    await waitFor(() => {
      expect(result.current.user).toEqual(mockUserData);
    });

    // Clear previous calls
    mockFetch.mockClear();

    // Call refetch
    result.current.refetch();

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/users/me', expect.any(Object));
    });
  });

  it('should handle different user roles correctly', async () => {
    const adminUserData = { ...mockUserData, role: 'admin' };
    
    mockUseUser.mockReturnValue({
      user: mockClerkUser,
      isLoaded: true,
    });

    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: adminUserData,
        }),
      } as Response);

    const { result } = renderHook(() => useCurrentUser());

    await waitFor(() => {
      expect(result.current.user).toEqual(adminUserData);
      expect(result.current.isStudent).toBe(false);
      expect(result.current.isConsultant).toBe(false);
      expect(result.current.isAdmin).toBe(true);
      expect(result.current.lastKnownRole).toBe('admin');
    });
  });

  it('should cleanup resources on unmount', () => {
    mockUseUser.mockReturnValue({
      user: mockClerkUser,
      isLoaded: true,
    });

    const { unmount } = renderHook(() => useCurrentUser());

    // Unmount should not throw errors
    expect(() => unmount()).not.toThrow();
  });
});
