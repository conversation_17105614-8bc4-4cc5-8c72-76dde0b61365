const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

// Create Supabase client with service role
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixDatabase() {
  try {
    console.log('🔧 Fixing database RLS policies...');

    // Read the SQL fix file
    const sqlPath = path.join(
      __dirname,
      '..',
      'database',
      'fix-rls-policies.sql'
    );
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');

    // Split SQL into individual statements
    const statements = sqlContent
      .split(';')
      .map((stmt) => stmt.trim())
      .filter((stmt) => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📝 Executing ${statements.length} SQL statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(
        `   ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`
      );

      const { error } = await supabase.rpc('exec_sql', { sql: statement });

      if (error) {
        console.error(`❌ Error executing statement ${i + 1}:`, error);
        // Continue with other statements
      } else {
        console.log(`   ✅ Statement ${i + 1} executed successfully`);
      }
    }

    console.log('🎉 Database fix completed!');

    // Test the fix
    console.log('🧪 Testing database connection...');
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      console.error('❌ Database test failed:', error);
    } else {
      console.log('✅ Database connection test passed!');
    }
  } catch (error) {
    console.error('❌ Failed to fix database:', error);
    process.exit(1);
  }
}

// Alternative approach using direct SQL execution
async function fixDatabaseDirect() {
  try {
    console.log('🔧 Fixing database RLS policies (direct approach)...');

    // Drop problematic policies first
    const dropPolicies = [
      'DROP POLICY IF EXISTS "Admins can view all users" ON users',
      'DROP POLICY IF EXISTS "Admins can update all users" ON users',
      'DROP POLICY IF EXISTS "Admins can insert users" ON users',
      'DROP POLICY IF EXISTS "Admins can manage all student profiles" ON student_profiles',
      'DROP POLICY IF EXISTS "Admins can manage all consultants" ON consultants',
      'DROP POLICY IF EXISTS "Admins can manage schools" ON schools',
      'DROP POLICY IF EXISTS "Admins can manage all documents" ON documents',
      'DROP POLICY IF EXISTS "Admins can manage all relations" ON student_consultant_relations'
    ];

    console.log('🗑️  Dropping problematic policies...');
    for (const policy of dropPolicies) {
      const { error } = await supabase.rpc('exec_sql', { sql: policy });
      if (error && !error.message.includes('does not exist')) {
        console.error('Error dropping policy:', error);
      }
    }

    // Add service role policies
    const servicePolicies = [
      'CREATE POLICY "Service role can manage all users" ON users FOR ALL USING (auth.role() = \'service_role\')',
      'CREATE POLICY "Service role can manage all student profiles" ON student_profiles FOR ALL USING (auth.role() = \'service_role\')',
      'CREATE POLICY "Service role can manage all consultants" ON consultants FOR ALL USING (auth.role() = \'service_role\')',
      'CREATE POLICY "Service role can manage schools" ON schools FOR ALL USING (auth.role() = \'service_role\')',
      'CREATE POLICY "Service role can manage all documents" ON documents FOR ALL USING (auth.role() = \'service_role\')',
      'CREATE POLICY "Service role can manage all relations" ON student_consultant_relations FOR ALL USING (auth.role() = \'service_role\')'
    ];

    console.log('➕ Adding service role policies...');
    for (const policy of servicePolicies) {
      const { error } = await supabase.rpc('exec_sql', { sql: policy });
      if (error && !error.message.includes('already exists')) {
        console.error('Error creating policy:', error);
      }
    }

    console.log('🎉 Database fix completed!');
  } catch (error) {
    console.error('❌ Failed to fix database:', error);
    process.exit(1);
  }
}

// Run the fix
fixDatabaseDirect();
