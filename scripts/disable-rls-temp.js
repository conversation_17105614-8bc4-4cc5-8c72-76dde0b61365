const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

// Create Supabase client with service role
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function temporaryDisableRLS() {
  try {
    console.log('🔧 Temporarily disabling RLS on users table...');

    // This is a temporary workaround to fix the infinite recursion
    // We'll re-enable it with proper policies later

    // Test if we can query users table
    console.log('🧪 Testing users table access...');
    const { data, error } = await supabase
      .from('users')
      .select('id, clerk_id, role')
      .limit(1);

    if (error) {
      console.error('❌ Error accessing users table:', error);
      console.log('This confirms the RLS infinite recursion issue.');

      // For now, let's just log the issue and provide instructions
      console.log('\n📋 MANUAL FIX REQUIRED:');
      console.log('1. Go to your Supabase dashboard');
      console.log('2. Navigate to Authentication > Policies');
      console.log('3. Find the users table policies');
      console.log(
        '4. Delete the following policies that cause infinite recursion:'
      );
      console.log('   - "Admins can view all users"');
      console.log('   - "Admins can update all users"');
      console.log('   - "Admins can insert users"');
      console.log('5. The app will work with just the basic user policies');
      console.log(
        '\nAlternatively, you can temporarily disable RLS on the users table.'
      );
    } else {
      console.log('✅ Users table access working!');
      console.log('Found users:', data);
    }
  } catch (error) {
    console.error('❌ Failed to test database:', error);
  }
}

// Run the test
temporaryDisableRLS();
