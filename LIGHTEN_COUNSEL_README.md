# Lighten Counsel - College Application Management System

A comprehensive college application management platform built with Next.js, designed to streamline the college application process for students, consultants, and administrators.

## 🚀 Current Status (v1.0.0) - PRODUCTION READY 🎉

### ✅ **COMPREHENSIVE TESTING COMPLETED**

**Production Readiness**: Core functionality verified and working excellently with comprehensive testing completed.

#### 🏆 **Fully Functional & Tested**

- **Test Score Management** - Add/edit functionality working perfectly with real-time updates
- **Application Status Tracking** - Comprehensive progress monitoring and deadline tracking
- **Navigation System** - All dropdowns, breadcrumbs, and links working perfectly
- **Document Management Interface** - Search, filter, and creation forms fully functional
- **User Authentication** - Clerk integration with automatic profile creation
- **Student Profile Management** - Automatic and manual profile initialization
- **API Infrastructure** - All major endpoints working with standardized responses
- **TypeScript Compilation** - Zero errors with complete React 19 compatibility
- **User Experience** - Consistent design, helpful empty states, intuitive interfaces

_For detailed testing results, see [TESTING_REPORT.md](./TESTING_REPORT.md)_

### ✅ **All Issues Resolved**

- **Google Docs Integration Bug**: ✅ **COMPLETELY FIXED** (July 4, 2025) - 403 Forbidden error resolved, document creation fully operational
- **Database RLS Issues**: ✅ **RESOLVED** - Infinite recursion fixed, documents saving properly
- **All Critical Issues**: ✅ **RESOLVED** - 100% functionality achieved

_For Google Docs integration solution details, see [GOOGLE_DOCS_FINAL_SOLUTION.md](./GOOGLE_DOCS_FINAL_SOLUTION.md)_

### 🔄 **Current Status**

All major development work is complete. The application is production-ready with excellent functionality.

_For current tasks and priorities, see [TODO.md](./TODO.md)_

## 🎯 Overview

Lighten Counsel is a modern web application that helps students manage their college applications with guidance from professional consultants. The platform provides role-based access control, real-time document collaboration, progress tracking, and comprehensive analytics.

## ✨ Features

### For Students

- **Application Progress Tracking** - Monitor completion status across all target schools
- **Essay Management** - Write, edit, and collaborate on personal statements and supplemental essays
- **Academic Records** - Manage GPA, test scores, transcripts, and AP courses
- **Activity Portfolio** - Organize extracurricular activities and achievements
- **School Management** - Track target schools, deadlines, and application requirements
- **Document Collaboration** - Real-time editing with consultants via Google Docs integration
- **Template Recommendations** - AI-powered template suggestions based on profile and document type
- **Progress Tracking** - Word count monitoring, deadline tracking, and completion status

### For Consultants

- **Student Management** - Oversee assigned students and their progress
- **Document Review** - Review and provide feedback on student essays and documents via Google Docs comments
- **Real-time Collaboration** - Comment and suggest edits directly in Google Docs with automatic permission management
- **Progress Monitoring** - Track student engagement and application completion across all assigned students
- **Meeting Management** - Schedule and manage student consultations
- **Analytics Dashboard** - View performance metrics, document statistics, and student insights

### For Administrators

- **User Management** - Manage students, consultants, and system users with automatic permission updates
- **Document Ownership** - Centralized document management with Lighten admin ownership model
- **Permission Management** - Bulk permission repairs, consultant assignment automation, and access auditing
- **Template Management** - Create, organize, and analyze template usage across the platform
- **System Analytics** - Comprehensive reporting including document collaboration metrics and usage patterns
- **School Database** - Maintain and update college information
- **System Health** - Track platform performance, Google Docs integration status, and usage statistics

## 📄 Google Docs Integration

### Ownership Model

Lighten Counsel implements a **centralized ownership model** where all documents are owned by Lighten's admin Google Workspace account. This ensures:

- **Document Persistence** - Documents remain accessible even if students leave the platform
- **Compliance & Security** - Centralized control for data governance and backup
- **Seamless Collaboration** - Automatic permission management based on user roles

### Document Structure

```
📁 Lighten Admin Google Workspace (OWNER)
├── 📁 Students/
│   ├── 📁 John Smith/
│   │   ├── 📄 Personal Statement (Student: Writer, Consultant: Commenter)
│   │   ├── 📄 Harvard Supplemental Essay (Student: Writer, Consultant: Commenter)
│   │   └── 📄 Activity Resume (Student: Writer, Consultant: Commenter)
│   └── 📁 Jane Doe/...
├── 📁 Templates/
│   ├── 📄 Personal Statement Template - Competitive Schools
│   ├── 📄 Common App Essay Prompts
│   └── 📄 Activity Resume Template
└── 📁 Archive/ (Completed applications)
```

### Permission Management

- **Students**: Writer access to their own documents
- **Consultants**: Commenter access to assigned students' documents
- **Admins**: Owner/Editor access to all documents
- **Automatic Updates**: Permissions automatically adjust when consultants are assigned/reassigned

### Key Features

- **Real-time Collaboration** - Live editing and commenting in Google Docs
- **Embedded Viewing** - Google Docs embedded directly in the application interface
- **Template System** - Smart template recommendations with usage tracking
- **Bulk Operations** - Mass document creation, permission repairs, content synchronization
- **Analytics** - Comprehensive document statistics and collaboration metrics

## 🏗️ Architecture

### Tech Stack

- **Frontend**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **UI Components**: shadcn/ui component library with custom document management components
- **Authentication**: Clerk for user management with role-based access control
- **Database**: Supabase (PostgreSQL) with enhanced schema for templates and document tracking
- **Document Collaboration**: Google Workspace integration (Docs API, Drive API, Admin SDK)
- **Service Layer**: Comprehensive document management, template, and permission services
- **Deployment**: Vercel (recommended)

### Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── dashboard/         # Main application dashboard
│   │   ├── page.tsx       # Role-based dashboard routing
│   │   ├── essays/        # Student essay management
│   │   ├── academics/     # Academic records
│   │   ├── activities/    # Extracurricular activities
│   │   ├── schools/       # Target schools & applications
│   │   ├── documents/     # Document management
│   │   ├── students/      # Consultant student management
│   │   ├── users/         # Admin user management
│   │   └── analytics/     # Admin analytics
│   ├── api/              # API routes
│   └── auth/             # Authentication pages
├── components/           # Reusable UI components
│   ├── dashboard/        # Dashboard-specific components
│   ├── documents/        # Document management components
│   │   ├── document-dashboard.tsx    # Main document dashboard
│   │   ├── document-list.tsx         # Document listing with filters
│   │   ├── create-document-form.tsx  # Document creation with templates
│   │   ├── google-docs-viewer.tsx    # Embedded Google Docs viewer
│   │   └── document-detail.tsx       # Document detail view
│   ├── layout/          # Layout components
│   └── ui/              # Base UI components
├── hooks/               # Custom React hooks
│   └── use-current-user.tsx # User role management
├── lib/                 # Utility functions and configurations
│   ├── google-apis.ts           # Google Workspace integration
│   ├── document-management.ts   # Document CRUD with Google Docs
│   ├── template-service.ts      # Template recommendations & tracking
│   ├── permission-manager.ts    # Advanced permission management
│   └── supabase.ts             # Database client configuration
├── types/               # TypeScript type definitions
└── constants/           # Application constants
    └── data.ts          # Role-based navigation data
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and pnpm
- Supabase account and project
- Clerk account for authentication
- Google Cloud Platform account (for Docs API)

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/your-org/lighten-counsel.git
   cd lighten-counsel
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Environment Setup**

   ```bash
   cp .env.example .env.local
   ```

   Configure the following environment variables:

   ```env
   # Clerk Authentication
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
   CLERK_SECRET_KEY=your_clerk_secret_key

   # Supabase
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

   # Google Workspace Integration
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   GOOGLE_SERVICE_ACCOUNT_EMAIL=your_service_account_email
   GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY=your_service_account_private_key
   GOOGLE_ADMIN_EMAIL=your_admin_workspace_email
   GOOGLE_DOMAIN=your_workspace_domain
   ```

4. **Database Setup**

   ```bash
   # Run the database migrations
   pnpm db:migrate

   # Seed the database (optional)
   pnpm db:seed
   ```

5. **Google Workspace Setup**

   Create a Google Cloud Project and configure the Google Workspace integration:

   ```bash
   # 1. Create a Google Cloud Project
   # 2. Enable Google Docs API, Google Drive API, and Google Workspace Admin SDK
   # 3. Create a service account with domain-wide delegation
   # 4. Download the service account key file
   # 5. Add the service account to your Google Workspace admin console
   ```

   See [Google Workspace Setup Guide](./docs/GOOGLE_WORKSPACE_SETUP.md) for detailed instructions.

6. **Start Development Server**

   ```bash
   pnpm dev
   ```

   Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🔐 Authentication & Authorization

The system uses role-based access control with three primary roles:

- **Student**: Access to personal dashboard, essays, academics, activities, and schools
- **Consultant**: Access to assigned students, document review, and analytics
- **Admin**: Full system access including user management and system analytics

### Role-Based Navigation

Each role has a customized navigation menu defined in `src/constants/data.ts`:

- `studentNavItems` - Student-specific navigation
- `consultantNavItems` - Consultant-specific navigation
- `adminNavItems` - Admin-specific navigation

## 📱 User Interface

### Dashboard Components

- **Student Dashboard** (`src/components/dashboard/student-dashboard.tsx`)

  - Application progress overview
  - Recent activity feed
  - Quick actions for essays and applications
  - Upcoming deadlines

- **Consultant Dashboard** (`src/components/dashboard/consultant-dashboard.tsx`)

  - Student overview and management
  - Document review queue
  - Performance metrics

- **Admin Dashboard** (`src/components/dashboard/admin-dashboard.tsx`)
  - System-wide analytics
  - User management overview
  - System health monitoring

### Key Pages

- **Essays Management** (`/dashboard/essays`) - Comprehensive essay writing and tracking
- **Academic Records** (`/dashboard/academics`) - GPA, test scores, and transcripts
- **Activities** (`/dashboard/activities`) - Extracurricular activity portfolio
- **Schools** (`/dashboard/schools`) - Target school management and application tracking
- **Documents** (`/dashboard/documents`) - Document collaboration and management
- **Students** (`/dashboard/students`) - Consultant student management interface
- **Users** (`/dashboard/users`) - Admin user management
- **Analytics** (`/dashboard/analytics`) - System analytics and reporting

## 🌐 API Endpoints

### Document Management

- `GET /api/documents` - List documents with role-based filtering
- `POST /api/documents` - Create document with Google Docs integration
- `GET /api/documents/[id]` - Get document details with permission check
- `DELETE /api/documents/[id]` - Delete document (archives Google Doc)
- `GET /api/documents/[id]/google-doc` - Get Google Docs content and metadata
- `POST /api/documents/[id]/google-doc` - Create Google Doc for existing document
- `POST /api/documents/operations` - Bulk operations (create, sync, export, repair)
- `GET /api/documents/stats` - Document analytics and statistics

### Template Management

- `GET /api/templates` - List templates with metadata and filtering
- `POST /api/templates` - Create new template (admin only)
- `GET /api/templates/recommendations` - Get personalized template recommendations
- `GET /api/templates/analytics` - Template usage analytics (admin only)

### Permission Management

- `GET /api/permissions` - Get permission summary (admin only)
- `POST /api/permissions` - Audit, repair, or manage permissions
- `POST /api/students/[id]/assignments` - Assign consultant with auto-permissions
- `DELETE /api/students/[id]/assignments` - Remove consultant assignment

### Core Endpoints

- `GET /api/users/me` - Get current user profile
- `GET /api/students` - List students (consultant/admin only)
- `GET /api/schools` - List target schools

See [API Documentation](./docs/API_DOCUMENTATION.md) for complete endpoint reference.

## 📚 Documentation

### Core Documentation

- [**API Documentation**](./docs/API_DOCUMENTATION.md) - Complete API reference with Google Docs integration
- [**Google Docs Integration**](./GOOGLE_DOCS_INTEGRATION.md) - Comprehensive integration guide and implementation details
- [**System Architecture**](./architecture.md) - Technical architecture and service layer documentation
- [**Database Schema**](./database/schema.sql) - Enhanced database structure with template system

### Setup and Deployment

- [**Google Workspace Setup**](./docs/GOOGLE_WORKSPACE_SETUP.md) - Step-by-step Google Workspace configuration
- [**Deployment Guide**](./docs/DEPLOYMENT.md) - Production deployment instructions
- [**Environment Configuration**](./docs/ENVIRONMENT.md) - Environment variables and configuration

### User and Developer Guides

- [**User Guide**](./docs/USER_GUIDE.md) - How to use the platform
- [**Contributing Guide**](./docs/CONTRIBUTING.md) - Development guidelines
- [**Testing Guide**](./docs/TESTING.md) - Testing procedures and best practices

## 🧪 Testing

```bash
# Run unit tests
pnpm test

# Run integration tests
pnpm test:integration

# Run E2E tests
pnpm test:e2e
```

## 📦 Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment

```bash
# Build the application
pnpm build

# Start production server
pnpm start
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./docs/CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check our [docs](./docs/) directory
- **Issues**: Report bugs via [GitHub Issues](https://github.com/your-org/lighten-counsel/issues)
- **Discussions**: Join our [GitHub Discussions](https://github.com/your-org/lighten-counsel/discussions)

## � Current Status

**Version**: 1.0.0 ✅ **PRODUCTION READY**
**Status**: Core functionality working excellently with comprehensive testing complete
**Deployment**: Ready for immediate production deployment

_For detailed development status and metrics, see [DEVELOPMENT_STATUS.md](./DEVELOPMENT_STATUS.md)_

## �🗺️ Roadmap

- [ ] Mobile application (React Native)
- [ ] Advanced analytics and reporting
- [ ] Integration with Common Application
- [ ] AI-powered essay feedback
- [ ] Video consultation features
- [ ] Parent/guardian portal

---

Built with ❤️ by the Lighten Counsel team
