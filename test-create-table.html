<!DOCTYPE html>
<html>
<head>
    <title>Create Activities Table</title>
</head>
<body>
    <h1>Create Activities Table</h1>
    <button onclick="createTable()">Create Activities Table</button>
    <div id="result"></div>

    <script>
        async function createTable() {
            try {
                const response = await fetch('http://localhost:3001/api/admin/create-activities-table', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                
                if (result.success) {
                    alert('Activities table created successfully!');
                } else {
                    alert('Failed to create table: ' + result.error);
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = '<pre>Error: ' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
