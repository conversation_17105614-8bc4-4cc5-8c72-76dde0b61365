# Google Docs Integration Flow

## ✅ Implementation Status

**COMPLETED** - The Google Docs integration system has been fully implemented and is ready for production use.

### Implemented Features

- ✅ **Document Ownership Model** - Lighten admin ownership with role-based permissions
- ✅ **Real-time Collaboration** - Students and consultants can edit/comment in Google Docs
- ✅ **Automatic Permission Management** - Permissions update when consultants are assigned/reassigned
- ✅ **Template System** - Smart template recommendations based on student profiles
- ✅ **Bulk Operations** - Mass document creation, permission repairs, content synchronization
- ✅ **Analytics Dashboard** - Comprehensive document and collaboration metrics
- ✅ **Embedded Viewing** - Google Docs embedded directly in the application interface
- ✅ **API Integration** - Complete REST API for document management operations
- ✅ **Database Schema** - Enhanced schema with template metadata and usage tracking
- ✅ **UI Components** - Full document management interface with collaboration features

## Overview

The Google Docs integration uses **Lighten's admin Google Workspace account** as the owner of all documents, with role-based sharing to students and consultants. This ensures centralized control, data persistence, and compliance.

## Ownership Model

### 🏢 **Document Owner: Lighten (Admin Account)**

- **Primary Owner**: Lighten's Google Workspace admin account
- **Benefits**:
  - ✅ Full oversight and control
  - ✅ Documents persist even if users leave
  - ✅ Centralized backup and compliance
  - ✅ Audit trail and monitoring
  - ✅ Template management
  - ✅ Bulk operations and analytics

### 👥 **User Access Levels**

- **Students**: `writer` access to their own documents
- **Consultants**: `commenter` access to assigned students' documents
- **Admins**: `writer` access to all documents

## Folder Structure

```
📁 Lighten College Consulting/
├── 📁 Students/
│   ├── 📁 John Smith/
│   │   ├── 📁 Personal Statement/
│   │   │   └── 📄 John Smith - Personal Statement.docx
│   │   ├── 📁 Supplemental Essays/
│   │   │   ├── 📄 Harvard - Diversity Essay.docx
│   │   │   ├── 📄 Stanford - Roommate Letter.docx
│   │   │   └── 📄 MIT - Pleasure Activity.docx
│   │   ├── 📁 Activity Resume/
│   │   │   └── 📄 John Smith - Activities.docx
│   │   ├── 📁 Transcripts/
│   │   └── 📁 Other Documents/
│   └── 📁 Jane Doe/
│       └── ... (same structure)
├── 📁 Templates/
│   ├── 📄 Personal Statement Template.docx
│   ├── 📄 Activity Resume Template.docx
│   ├── 📄 Common App Essay Template.docx
│   └── 📁 School-Specific Templates/
└── 📁 Archive/
    └── 📁 Graduated Students/
```

## Document Creation Flow

### 1. **Student Requests New Document**

```
Student clicks "Create Essay" → API call → Backend validation
```

### 2. **Backend Processing**

```javascript
// API: POST /api/documents
{
  "student_id": "uuid",
  "doc_type": "essay",
  "metadata": {
    "title": "Harvard Diversity Essay",
    "school_id": "harvard_uuid",
    "word_limit": 650
  }
}
```

### 3. **Google Docs Creation**

```javascript
// 1. Create document (owned by Lighten admin)
const { documentId, documentUrl, folderId } =
  await GoogleDocsService.createDocument(
    'Harvard Diversity Essay',
    'John Smith',
    'essay',
    templateContent,
    templateId
  );

// 2. Set up permissions
await DocumentPermissionService.setupDocumentPermissions(
  documentId,
  studentEmail,
  consultantEmails,
  adminEmails
);

// 3. Store in database
await supabase.from('documents').insert({
  student_id,
  google_doc_id: documentId,
  doc_type: 'essay',
  metadata: { title, google_doc_url: documentUrl, folder_id: folderId }
});
```

### 4. **Permission Assignment**

- **Student**: Gets `writer` access (can edit)
- **Assigned Consultant**: Gets `commenter` access (can suggest/comment)
- **Lighten Admin**: Retains `owner` access (full control)

## Permission Management

### When Student is Assigned to Consultant

```javascript
// API: POST /api/students/[id]/assignments
{
  "consultant_id": "uuid"
}

// Backend automatically:
// 1. Updates all student's documents with consultant access
// 2. Shares student folder with consultant (read access)
// 3. Sends notification to consultant
```

### When Consultant Assignment Changes

```javascript
// Automatically:
// 1. Remove old consultant's access from all documents
// 2. Add new consultant's access to all documents
// 3. Update folder permissions
```

## Access Control Matrix

| Role                | Document Access        | Folder Access      | Can Create | Can Delete |
| ------------------- | ---------------------- | ------------------ | ---------- | ---------- |
| **Student**         | ✏️ Edit own docs       | 📁 Edit own folder | ✅ Yes     | ❌ No\*    |
| **Consultant**      | 💬 Comment on assigned | 👀 View assigned   | ❌ No      | ❌ No      |
| **Admin**           | ✏️ Edit all docs       | 📁 Manage all      | ✅ Yes     | ✅ Yes     |
| **Lighten (Owner)** | 👑 Own all docs        | 👑 Own all folders | ✅ Yes     | ✅ Yes     |

\*Students can delete through the application, which removes database records but archives Google Docs

## Template System

### Template Creation

1. **Admin creates templates** in the Templates folder
2. **Templates are categorized** by type (Personal Statement, Activity Resume, etc.)
3. **Templates include placeholders** for student information

### Template Usage

```javascript
// When creating from template:
const { documentId } = await GoogleDocsService.createDocument(
  'John Smith - Personal Statement',
  'John Smith',
  'personal_statement',
  undefined, // no content (using template)
  templateId // copy from this template
);
```

## Security Features

### 1. **Service Account Authentication**

- Uses Google service account (not OAuth)
- Private key stored securely in environment variables
- No user authentication required for document operations

### 2. **Role-Based Access Control**

- Database-driven permission management
- Automatic permission updates when assignments change
- Audit trail of all permission changes

### 3. **Data Protection**

- All documents owned by Lighten (prevents data loss)
- Automatic backup through Google Workspace
- Version history maintained by Google Docs

## API Endpoints for Google Docs

### Document Management

```
POST /api/documents/[id]/google-doc
- Creates Google Doc for existing document record
- Sets up permissions automatically
- Returns document URL for embedding

GET /api/documents/[id]/google-doc
- Retrieves Google Doc content
- Checks permissions before access
- Returns formatted content

PUT /api/documents/[id]/google-doc
- Updates document metadata
- Can change permissions if needed
```

### Template Management

```
GET /api/templates
- Lists available templates
- Filtered by user role and document type

POST /api/templates (Admin only)
- Creates new template
- Uploads to Templates folder
```

## Integration with Frontend

### 1. **Embedded Google Docs**

```html
<!-- Embed Google Doc directly in application -->
<iframe
  src="https://docs.google.com/document/d/{documentId}/edit?embedded=true"
  width="100%"
  height="600px"
>
</iframe>
```

### 2. **Document Status Tracking**

- Real-time updates when documents are modified
- Word count tracking
- Collaboration status (who's currently editing)

### 3. **Comment Integration**

- Fetch comments via Google Docs API
- Display consultant feedback in application
- Notification system for new comments

## 🔧 Implementation Details

### Service Layer Architecture

The implementation consists of several key services:

#### 1. **DocumentManagementService** (`src/lib/document-management.ts`)

```typescript
// Create document with Google Docs integration
const document = await DocumentManagementService.createDocument({
  student_id: 'uuid',
  doc_type: 'essay',
  metadata: {
    title: 'Harvard Diversity Essay',
    word_limit: 650,
    deadline: '2024-01-01'
  },
  template_id: 'template-uuid', // Optional
  initial_content: 'Start writing here...' // Optional
});
```

#### 2. **AdvancedPermissionManager** (`src/lib/permission-manager.ts`)

```typescript
// Handle consultant assignment with automatic permission updates
await AdvancedPermissionManager.handleConsultantAssignmentChange(
  studentId,
  oldConsultantId, // undefined if first assignment
  newConsultantId
);

// Audit permissions for a student
const audit =
  await AdvancedPermissionManager.auditStudentPermissions(studentId);
```

#### 3. **TemplateService** (`src/lib/template-service.ts`)

```typescript
// Get personalized template recommendations
const recommendations = await TemplateService.getTemplateRecommendations(
  studentId,
  'personal_statement',
  5 // limit
);

// Track template usage
await TemplateService.trackTemplateUsage(templateId, studentId, documentId);
```

### API Endpoints

#### Document Management

```http
# Create document with Google Docs integration
POST /api/documents
{
  "student_id": "uuid",
  "doc_type": "essay",
  "metadata": {
    "title": "Harvard Diversity Essay",
    "word_limit": 650
  },
  "template_id": "template-uuid"
}

# Get document with Google Docs content
GET /api/documents/{id}/google-doc

# Bulk operations
POST /api/documents/operations
{
  "operation": "sync_content",
  "document_ids": ["uuid1", "uuid2"]
}
```

#### Permission Management

```http
# Assign consultant with automatic permissions
POST /api/students/{id}/assignments
{
  "consultant_id": "uuid",
  "start_date": "2024-01-01"
}

# Audit permissions
POST /api/permissions
{
  "action": "audit",
  "student_id": "uuid"
}
```

#### Template System

```http
# Get personalized recommendations
GET /api/templates/recommendations?student_id=uuid&doc_type=essay&limit=5

# Get template analytics
GET /api/templates/analytics
```

### Database Schema Updates

#### Template Metadata Table

```sql
CREATE TABLE template_metadata (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    google_doc_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    doc_type document_type NOT NULL,
    category TEXT NOT NULL DEFAULT 'general',
    description TEXT,
    word_limit INTEGER,
    tags TEXT[] DEFAULT '{}',
    difficulty_level difficulty_level NOT NULL DEFAULT 'intermediate',
    is_featured BOOLEAN NOT NULL DEFAULT false,
    usage_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Template Usage Tracking

```sql
CREATE TABLE template_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id TEXT NOT NULL REFERENCES template_metadata(google_doc_id),
    student_id UUID NOT NULL REFERENCES student_profiles(id),
    document_id UUID NOT NULL REFERENCES documents(id),
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Setup Requirements

### 1. **Google Workspace Admin Account**

```env
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----..."
GOOGLE_PROJECT_ID=your-project-id
```

### 2. **Required Google APIs**

- Google Drive API (folder/file management)
- Google Docs API (document creation/editing)
- Google Sheets API (optional: for analytics)

### 3. **Permissions Setup**

- Service account must have domain-wide delegation
- Enable APIs in Google Cloud Console
- Configure OAuth scopes

## Benefits of This Approach

### ✅ **For Lighten**

- Full control and ownership of all documents
- Centralized management and analytics
- Compliance and audit capabilities
- Data persistence and backup
- Template standardization

### ✅ **For Students**

- Familiar Google Docs interface
- Real-time collaboration with consultants
- Access from any device
- Automatic saving and version history

### ✅ **For Consultants**

- Easy commenting and suggestion workflow
- Clear visibility into student progress
- No need for separate accounts or logins
- Organized folder structure per student

## Migration and Backup

### Document Export

- Automatic export to PDF for final submissions
- Bulk export capabilities for archiving
- Integration with application portfolio

### Data Retention

- Graduated students moved to Archive folder
- Configurable retention policies
- Export capabilities for student records

This ownership model ensures Lighten maintains control while providing seamless collaboration for students and consultants.
