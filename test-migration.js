// Simple test to call the migration API
const fetch = require('node-fetch');

async function testMigration() {
  try {
    const response = await fetch('http://localhost:3001/api/admin/migrate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // You'll need to add proper authentication headers here
      }
    });
    
    const result = await response.json();
    console.log('Migration result:', result);
  } catch (error) {
    console.error('Migration test failed:', error);
  }
}

testMigration();
