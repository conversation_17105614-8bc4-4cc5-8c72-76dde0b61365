/**
 * Simple API Test Script
 *
 * This script tests the basic functionality of the College Application System API.
 * Run this after setting up the backend to verify everything is working.
 *
 * Prerequisites:
 * 1. Backend is running on localhost:3000
 * 2. You have signed up and have a valid session
 * 3. You have set up at least one admin user
 *
 * Usage:
 * node tests/api-test.js
 */

const BASE_URL = 'http://localhost:3000/api';

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json'
    }
  };

  const response = await fetch(url, { ...defaultOptions, ...options });
  const data = await response.json();

  return {
    status: response.status,
    ok: response.ok,
    data
  };
}

// Test functions
async function testHealthCheck() {
  console.log('🔍 Testing API Health...');

  try {
    // Test if the API is responding
    const response = await fetch(`${BASE_URL.replace('/api', '')}/api/users`);

    if (response.status === 401) {
      console.log('✅ API is responding (authentication required as expected)');
      return true;
    } else {
      console.log('❌ Unexpected response from API');
      return false;
    }
  } catch (error) {
    console.log('❌ API is not responding:', error.message);
    return false;
  }
}

async function testDatabaseConnection() {
  console.log('🔍 Testing Database Connection...');

  try {
    // This will test if Supabase is properly configured
    // Note: This requires authentication, so it will fail if not logged in
    const result = await apiRequest('/users/me');

    if (result.status === 401) {
      console.log(
        '✅ Database connection is working (authentication required)'
      );
      return true;
    } else if (result.ok) {
      console.log(
        '✅ Database connection is working and user is authenticated'
      );
      console.log('   User data:', result.data.data);
      return true;
    } else {
      console.log('❌ Database connection issue:', result.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
    return false;
  }
}

async function testSchoolsEndpoint() {
  console.log('🔍 Testing Schools Endpoint...');

  try {
    const result = await apiRequest('/schools');

    if (result.status === 401) {
      console.log('✅ Schools endpoint is protected (authentication required)');
      return true;
    } else if (result.ok) {
      console.log('✅ Schools endpoint is working');
      console.log(`   Found ${result.data.data?.length || 0} schools`);
      return true;
    } else {
      console.log('❌ Schools endpoint error:', result.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Schools endpoint failed:', error.message);
    return false;
  }
}

async function testEnvironmentVariables() {
  console.log('🔍 Testing Environment Variables...');

  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY',
    'CLERK_SECRET_KEY',
    'GOOGLE_SERVICE_ACCOUNT_EMAIL',
    'GOOGLE_PRIVATE_KEY',
    'GOOGLE_PROJECT_ID'
  ];

  // Note: This is a client-side test, so we can only check public env vars
  const publicEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY'
  ];

  let allGood = true;

  for (const envVar of publicEnvVars) {
    if (process.env[envVar]) {
      console.log(`✅ ${envVar} is set`);
    } else {
      console.log(`❌ ${envVar} is missing`);
      allGood = false;
    }
  }

  if (allGood) {
    console.log('✅ Public environment variables are configured');
    console.log(
      'ℹ️  Note: Private environment variables cannot be checked from client-side'
    );
  }

  return allGood;
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting College Application System API Tests\n');

  const tests = [
    { name: 'Environment Variables', fn: testEnvironmentVariables },
    { name: 'API Health Check', fn: testHealthCheck },
    { name: 'Database Connection', fn: testDatabaseConnection },
    { name: 'Schools Endpoint', fn: testSchoolsEndpoint }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    console.log(`\n--- ${test.name} ---`);
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ Test failed with error: ${error.message}`);
      failed++;
    }
  }

  console.log('\n' + '='.repeat(50));
  console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);

  if (failed === 0) {
    console.log('🎉 All tests passed! Your backend is ready to go.');
  } else {
    console.log('⚠️  Some tests failed. Please check the setup guide.');
  }

  console.log('\n📚 Next Steps:');
  console.log('1. Sign up through the application UI');
  console.log('2. Change your role to "admin" in the Supabase dashboard');
  console.log('3. Test the full application workflow');
  console.log('4. Create sample data for testing');
}

// Run the tests
if (typeof window === 'undefined') {
  // Node.js environment
  runTests().catch(console.error);
} else {
  // Browser environment
  console.log('This test script is designed to run in Node.js');
  console.log('Please run: node tests/api-test.js');
}
