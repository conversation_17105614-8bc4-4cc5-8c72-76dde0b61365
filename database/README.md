# Database Setup Guide

This guide will help you set up the Supabase database for the College Application System.

## Prerequisites

1. Create a Supabase account at [https://supabase.com](https://supabase.com)
2. Create a new project in Supabase

## Setup Steps

### 1. Get Supabase Credentials

1. Go to your Supabase project dashboard
2. Navigate to Settings > API
3. Copy the following values:
   - Project URL
   - Anon (public) key
   - Service role (secret) key

### 2. Configure Environment Variables

1. Copy `env.example.txt` to `.env.local`
2. Fill in the Supabase configuration:

```env
NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### 3. Run Database Schema

1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Run the following SQL files in order:

#### Step 1: Create Schema
Copy and paste the contents of `database/schema.sql` into the SQL editor and run it.

#### Step 2: Set up Row Level Security
Copy and paste the contents of `database/rls-policies.sql` into the SQL editor and run it.

### 4. Verify Setup

After running the SQL scripts, you should see the following tables in your database:

- `users` - User accounts linked to Clerk authentication
- `student_profiles` - Student-specific information and academic records
- `consultants` - Consultant profiles and availability
- `schools` - School information and requirements
- `documents` - Document metadata and Google Docs integration
- `student_consultant_relations` - Relationships between students and consultants

## Database Schema Overview

### Users Table
- Links Clerk authentication with application roles
- Supports three roles: student, consultant, admin
- Stores basic profile information

### Student Profiles Table
- Detailed academic information (GPA, test scores, activities)
- Target schools and application status
- Linked to user account

### Consultants Table
- Consultant specialties and availability
- Linked to user account

### Schools Table
- School information and application requirements
- Shared across all users

### Documents Table
- Metadata for student documents
- Integration with Google Docs
- Linked to student profiles

### Student-Consultant Relations Table
- Manages assignments between students and consultants
- Tracks relationship status and dates

## Row Level Security (RLS)

The database uses Row Level Security to ensure data privacy:

- **Students** can only access their own data
- **Consultants** can access data for their assigned students
- **Admins** have full access to all data
- **Authentication** is handled through Clerk integration

## Next Steps

After setting up the database:

1. Test the connection by running the application
2. Create your first admin user through the application
3. Set up Google APIs for document management (see Google APIs setup guide)

## Troubleshooting

### Common Issues

1. **Connection Error**: Verify your environment variables are correct
2. **Permission Denied**: Ensure RLS policies are properly set up
3. **Missing Tables**: Run the schema.sql file completely

### Getting Help

If you encounter issues:
1. Check the Supabase logs in your project dashboard
2. Verify all environment variables are set correctly
3. Ensure the SQL scripts ran without errors
