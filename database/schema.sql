-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('student', 'consultant', 'admin');
CREATE TYPE document_type AS ENUM ('essay', 'personal_statement', 'transcript', 'activity_resume', 'other');
CREATE TYPE relation_status AS ENUM ('active', 'inactive');
CREATE TYPE difficulty_level AS ENUM ('beginner', 'intermediate', 'advanced');
CREATE TYPE test_type AS ENUM ('SAT', 'ACT', 'SAT_SUBJECT', 'TOEFL', 'IELTS', 'AP', 'Other');
CREATE TYPE mock_test_source AS ENUM ('practice_test', 'tutor_feedback', 'official_practice', 'mock_exam', 'prep_course');
CREATE TYPE meeting_type AS ENUM ('consultation', 'review', 'planning', 'check_in', 'first_meetup', 'follow_up', 'essay_review', 'application_strategy', 'college_decision');
CREATE TYPE meeting_status AS ENUM ('scheduled', 'completed', 'cancelled', 'no_show', 'rescheduled');
CREATE TYPE invitation_status AS ENUM ('pending', 'used', 'expired');
CREATE TYPE registration_status AS ENUM ('pending_approval', 'approved', 'rejected');
CREATE TYPE advisor_role_type AS ENUM ('main_advisor', 'essay_teacher', 'planning_assistant', 'subject_specialist');
CREATE TYPE activity_category AS ENUM ('academic', 'art', 'athletics', 'community_service', 'cultural', 'debate', 'journalism', 'music', 'religious', 'research', 'robotics', 'student_government', 'work', 'honors', 'other');

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clerk_id TEXT UNIQUE NOT NULL,
    email TEXT NOT NULL,
    role user_role NOT NULL DEFAULT 'student',
    profile_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Student profiles table
CREATE TABLE student_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    academic_info JSONB,
    target_schools JSONB,
    application_status JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Consultants table
CREATE TABLE consultants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    specialties TEXT[] NOT NULL DEFAULT '{}',
    availability JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Schools table
CREATE TABLE schools (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    details JSONB,
    application_requirements JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Documents table
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
    google_doc_id TEXT,
    doc_type document_type NOT NULL,
    metadata JSONB,
    -- New fields for Goal #1: Essay System Enhancement
    essay_type TEXT CHECK (essay_type IN ('main', 'supplemental')) DEFAULT NULL,
    schools JSONB DEFAULT '[]'::jsonb, -- For main essays: multiple schools
    school_id UUID REFERENCES schools(id) DEFAULT NULL, -- For supplemental: specific school
    source_essay_id UUID REFERENCES documents(id) DEFAULT NULL, -- For copied essays
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Mock Test Scores table (Goal #2: Mock Test Score Tracking)
CREATE TABLE mock_test_scores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
    exam_type test_type NOT NULL,
    test_date DATE NOT NULL,
    score INTEGER NOT NULL,
    max_score INTEGER NOT NULL,
    source mock_test_source NOT NULL,
    notes TEXT,
    document_url TEXT, -- Scanned results uploaded by consultant
    created_by UUID NOT NULL REFERENCES users(id), -- Student or consultant who created this
    tutor_feedback TEXT,
    improvement_areas TEXT[], -- Array of areas for improvement
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Meetings table (Goal #11: Meeting System)
CREATE TABLE meetings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    consultant_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    meeting_type meeting_type DEFAULT 'consultation',
    status meeting_status DEFAULT 'scheduled',
    meeting_url TEXT,
    location TEXT, -- For in-person meetings
    agenda TEXT,
    notes TEXT, -- Post-meeting notes
    action_items TEXT[], -- Array of action items
    next_meeting_suggested TIMESTAMP WITH TIME ZONE,
    created_by UUID NOT NULL REFERENCES users(id), -- Who scheduled it
    requested_by UUID REFERENCES users(id), -- If student requested
    confirmed_by_student BOOLEAN DEFAULT false, -- Student confirmation
    shared_documents TEXT[], -- Array of document IDs
    recording_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Invitations table (Goal #8: Invitation-Based Registration)
CREATE TABLE invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code TEXT UNIQUE NOT NULL,
    email TEXT, -- For specific email invitations
    role user_role NOT NULL,
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    used_by UUID REFERENCES users(id),
    status invitation_status DEFAULT 'pending',
    max_uses INTEGER DEFAULT 1, -- For general invitation codes
    current_uses INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Registration requests table (Goal #8: Admin confirmation workflow)
CREATE TABLE registration_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT NOT NULL,
    invitation_code TEXT NOT NULL REFERENCES invitations(code) ON DELETE CASCADE,
    role_requested user_role NOT NULL,
    profile_data JSONB,
    status registration_status DEFAULT 'pending_approval',
    admin_notes TEXT,
    approved_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Student-Consultant relations table (Goal #7: Single Main Advisor System)
CREATE TABLE student_consultant_relations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
    consultant_id UUID NOT NULL REFERENCES consultants(id) ON DELETE CASCADE,
    role_type advisor_role_type NOT NULL DEFAULT 'main_advisor',
    start_date DATE NOT NULL,
    end_date DATE,
    status relation_status NOT NULL DEFAULT 'active',
    notes TEXT, -- Notes about this specific assignment
    assigned_by UUID REFERENCES users(id), -- Admin who made the assignment
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(student_id, consultant_id, role_type)
);

-- Add constraint to ensure only one active main advisor per student
CREATE UNIQUE INDEX idx_single_main_advisor
ON student_consultant_relations (student_id)
WHERE role_type = 'main_advisor' AND status = 'active';

-- Template metadata table
CREATE TABLE template_metadata (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    google_doc_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    doc_type document_type NOT NULL,
    category TEXT NOT NULL DEFAULT 'general',
    description TEXT,
    word_limit INTEGER,
    tags TEXT[] DEFAULT '{}',
    difficulty_level difficulty_level NOT NULL DEFAULT 'intermediate',
    is_featured BOOLEAN NOT NULL DEFAULT false,
    usage_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Template usage tracking table
CREATE TABLE template_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id TEXT NOT NULL REFERENCES template_metadata(google_doc_id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_clerk_id ON users(clerk_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_student_profiles_user_id ON student_profiles(user_id);
CREATE INDEX idx_consultants_user_id ON consultants(user_id);
CREATE INDEX idx_documents_student_id ON documents(student_id);
CREATE INDEX idx_documents_doc_type ON documents(doc_type);
CREATE INDEX idx_student_consultant_relations_student_id ON student_consultant_relations(student_id);
CREATE INDEX idx_student_consultant_relations_consultant_id ON student_consultant_relations(consultant_id);
CREATE INDEX idx_student_consultant_relations_status ON student_consultant_relations(status);
CREATE INDEX idx_student_consultant_relations_role_type ON student_consultant_relations(role_type);
CREATE INDEX idx_student_consultant_relations_assigned_by ON student_consultant_relations(assigned_by);
CREATE INDEX idx_template_metadata_google_doc_id ON template_metadata(google_doc_id);
CREATE INDEX idx_template_metadata_doc_type ON template_metadata(doc_type);
CREATE INDEX idx_template_metadata_category ON template_metadata(category);
CREATE INDEX idx_template_metadata_is_featured ON template_metadata(is_featured);
CREATE INDEX idx_template_metadata_usage_count ON template_metadata(usage_count);
CREATE INDEX idx_template_usage_template_id ON template_usage(template_id);
CREATE INDEX idx_template_usage_student_id ON template_usage(student_id);
CREATE INDEX idx_template_usage_document_id ON template_usage(document_id);
CREATE INDEX idx_template_usage_used_at ON template_usage(used_at);
CREATE INDEX idx_meetings_student_id ON meetings(student_id);
CREATE INDEX idx_meetings_consultant_id ON meetings(consultant_id);
CREATE INDEX idx_meetings_status ON meetings(status);
CREATE INDEX idx_meetings_scheduled_at ON meetings(scheduled_at);
CREATE INDEX idx_meetings_meeting_type ON meetings(meeting_type);
CREATE INDEX idx_invitations_code ON invitations(code);
CREATE INDEX idx_invitations_email ON invitations(email);
CREATE INDEX idx_invitations_status ON invitations(status);
CREATE INDEX idx_invitations_expires_at ON invitations(expires_at);
CREATE INDEX idx_invitations_created_by ON invitations(created_by);
CREATE INDEX idx_registration_requests_email ON registration_requests(email);
CREATE INDEX idx_registration_requests_invitation_code ON registration_requests(invitation_code);
CREATE INDEX idx_registration_requests_status ON registration_requests(status);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_student_profiles_updated_at BEFORE UPDATE ON student_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_consultants_updated_at BEFORE UPDATE ON consultants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_schools_updated_at BEFORE UPDATE ON schools FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_student_consultant_relations_updated_at BEFORE UPDATE ON student_consultant_relations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_template_metadata_updated_at BEFORE UPDATE ON template_metadata FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_meetings_updated_at BEFORE UPDATE ON meetings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_invitations_updated_at BEFORE UPDATE ON invitations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_registration_requests_updated_at BEFORE UPDATE ON registration_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE consultants ENABLE ROW LEVEL SECURITY;
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_consultant_relations ENABLE ROW LEVEL SECURITY;
ALTER TABLE template_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE template_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE meetings ENABLE ROW LEVEL SECURITY;
ALTER TABLE invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE registration_requests ENABLE ROW LEVEL SECURITY;
