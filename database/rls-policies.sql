-- Row Level Security Policies

-- Users table policies
-- Note: Admin operations should use service role key to bypass RLS
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid()::text = clerk_id);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid()::text = clerk_id);

-- Service role bypass for admin operations
CREATE POLICY "Service role can manage all users" ON users
    FOR ALL USING (auth.role() = 'service_role');

-- Student profiles policies
CREATE POLICY "Students can view their own profile" ON student_profiles
    FOR SELECT USING (
        user_id IN (
            SELECT id FROM users WHERE clerk_id = auth.uid()::text
        )
    );

CREATE POLICY "Students can update their own profile" ON student_profiles
    FOR UPDATE USING (
        user_id IN (
            SELECT id FROM users WHERE clerk_id = auth.uid()::text
        )
    );

CREATE POLICY "Consultants can view assigned students" ON student_profiles
    FOR SELECT USING (
        id IN (
            SELECT scr.student_id 
            FROM student_consultant_relations scr
            JOIN consultants c ON c.id = scr.consultant_id
            JOIN users u ON u.id = c.user_id
            WHERE u.clerk_id = auth.uid()::text AND scr.status = 'active'
        )
    );

CREATE POLICY "Consultants can update assigned students" ON student_profiles
    FOR UPDATE USING (
        id IN (
            SELECT scr.student_id 
            FROM student_consultant_relations scr
            JOIN consultants c ON c.id = scr.consultant_id
            JOIN users u ON u.id = c.user_id
            WHERE u.clerk_id = auth.uid()::text AND scr.status = 'active'
        )
    );

CREATE POLICY "Service role can manage all student profiles" ON student_profiles
    FOR ALL USING (auth.role() = 'service_role');

-- Consultants table policies
CREATE POLICY "Consultants can view their own profile" ON consultants
    FOR SELECT USING (
        user_id IN (
            SELECT id FROM users WHERE clerk_id = auth.uid()::text
        )
    );

CREATE POLICY "Consultants can update their own profile" ON consultants
    FOR UPDATE USING (
        user_id IN (
            SELECT id FROM users WHERE clerk_id = auth.uid()::text
        )
    );

CREATE POLICY "Service role can manage all consultants" ON consultants
    FOR ALL USING (auth.role() = 'service_role');

-- Schools table policies (read-only for students and consultants)
CREATE POLICY "All authenticated users can view schools" ON schools
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Service role can manage schools" ON schools
    FOR ALL USING (auth.role() = 'service_role');

-- Documents table policies
CREATE POLICY "Students can manage their own documents" ON documents
    FOR ALL USING (
        student_id IN (
            SELECT sp.id 
            FROM student_profiles sp
            JOIN users u ON u.id = sp.user_id
            WHERE u.clerk_id = auth.uid()::text
        )
    );

CREATE POLICY "Consultants can view assigned students' documents" ON documents
    FOR SELECT USING (
        student_id IN (
            SELECT scr.student_id 
            FROM student_consultant_relations scr
            JOIN consultants c ON c.id = scr.consultant_id
            JOIN users u ON u.id = c.user_id
            WHERE u.clerk_id = auth.uid()::text AND scr.status = 'active'
        )
    );

CREATE POLICY "Consultants can update assigned students' documents" ON documents
    FOR UPDATE USING (
        student_id IN (
            SELECT scr.student_id 
            FROM student_consultant_relations scr
            JOIN consultants c ON c.id = scr.consultant_id
            JOIN users u ON u.id = c.user_id
            WHERE u.clerk_id = auth.uid()::text AND scr.status = 'active'
        )
    );

CREATE POLICY "Service role can manage all documents" ON documents
    FOR ALL USING (auth.role() = 'service_role');

-- Student-Consultant relations policies
CREATE POLICY "Students can view their consultant relations" ON student_consultant_relations
    FOR SELECT USING (
        student_id IN (
            SELECT sp.id 
            FROM student_profiles sp
            JOIN users u ON u.id = sp.user_id
            WHERE u.clerk_id = auth.uid()::text
        )
    );

CREATE POLICY "Consultants can view their student relations" ON student_consultant_relations
    FOR SELECT USING (
        consultant_id IN (
            SELECT c.id 
            FROM consultants c
            JOIN users u ON u.id = c.user_id
            WHERE u.clerk_id = auth.uid()::text
        )
    );

CREATE POLICY "Service role can manage all relations" ON student_consultant_relations
    FOR ALL USING (auth.role() = 'service_role');
