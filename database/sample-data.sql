-- Sample Data for College Application System
-- Run this after setting up the main schema and having at least one admin user

-- Insert sample schools
INSERT INTO schools (name, details, application_requirements) VALUES
(
  'Harvard University',
  '{
    "location": "Cambridge, MA",
    "type": "Private",
    "ranking": 1,
    "acceptance_rate": 3.4,
    "tuition": 54000,
    "website": "https://www.harvard.edu"
  }',
  '{
    "essays": [
      {
        "prompt": "Harvard has long recognized the importance of student body diversity of all kinds. We welcome you to write about distinctive aspects of your background, personal development or the intellectual interests you might bring to your Harvard classmates.",
        "word_limit": 650,
        "type": "personal_statement"
      }
    ],
    "test_scores_required": ["SAT", "ACT"],
    "transcript_required": true,
    "recommendation_letters": 2,
    "deadlines": {
      "early_action": "2024-11-01",
      "regular_decision": "2024-01-01"
    }
  }'
),
(
  'Stanford University',
  '{
    "location": "Stanford, CA",
    "type": "Private",
    "ranking": 2,
    "acceptance_rate": 3.9,
    "tuition": 56000,
    "website": "https://www.stanford.edu"
  }',
  '{
    "essays": [
      {
        "prompt": "The Stanford community is deeply curious and driven to learn in and out of the classroom. Reflect on an idea or experience that makes you genuinely excited about learning.",
        "word_limit": 650,
        "type": "personal_statement"
      },
      {
        "prompt": "Virtually all of Stanford''s undergraduates live on campus. Write a note to your future roommate that reveals something about you or that will help your roommate—and us—get to know you better.",
        "word_limit": 250,
        "type": "supplemental"
      }
    ],
    "test_scores_required": ["SAT", "ACT"],
    "transcript_required": true,
    "recommendation_letters": 2,
    "deadlines": {
      "early_action": "2024-11-01",
      "regular_decision": "2024-01-02"
    }
  }'
),
(
  'MIT',
  '{
    "location": "Cambridge, MA",
    "type": "Private",
    "ranking": 3,
    "acceptance_rate": 4.1,
    "tuition": 53000,
    "website": "https://www.mit.edu"
  }',
  '{
    "essays": [
      {
        "prompt": "We know you lead a busy life, full of activities, many of which are required of you. Tell us about something you do simply for the pleasure of it.",
        "word_limit": 250,
        "type": "personal_statement"
      }
    ],
    "test_scores_required": ["SAT", "ACT"],
    "transcript_required": true,
    "recommendation_letters": 2,
    "deadlines": {
      "early_action": "2024-11-01",
      "regular_decision": "2024-01-01"
    }
  }'
),
(
  'University of California, Berkeley',
  '{
    "location": "Berkeley, CA",
    "type": "Public",
    "ranking": 22,
    "acceptance_rate": 14.5,
    "tuition": 14000,
    "website": "https://www.berkeley.edu"
  }',
  '{
    "essays": [
      {
        "prompt": "Describe the world you come from — for example, your family, community or school — and tell us how your world has shaped your dreams and aspirations.",
        "word_limit": 350,
        "type": "personal_statement"
      }
    ],
    "test_scores_required": ["SAT", "ACT"],
    "transcript_required": true,
    "recommendation_letters": 1,
    "deadlines": {
      "regular_decision": "2024-11-30"
    }
  }'
),
(
  'Yale University',
  '{
    "location": "New Haven, CT",
    "type": "Private",
    "ranking": 4,
    "acceptance_rate": 4.6,
    "tuition": 55000,
    "website": "https://www.yale.edu"
  }',
  '{
    "essays": [
      {
        "prompt": "Students at Yale have time to explore their academic interests before committing to one or more major fields of study. Many students either modify their original academic direction or change their minds entirely. As of this moment, what academic areas seem to fit your interests or goals most comfortably?",
        "word_limit": 650,
        "type": "personal_statement"
      }
    ],
    "test_scores_required": ["SAT", "ACT"],
    "transcript_required": true,
    "recommendation_letters": 2,
    "deadlines": {
      "early_action": "2024-11-01",
      "regular_decision": "2024-01-02"
    }
  }'
);

-- Note: Sample users, student profiles, and consultants should be created through the application
-- to ensure proper Clerk integration and webhook processing.

-- Sample consultant specialties (for reference when creating consultant profiles)
/*
Common specialties:
- "Ivy League Applications"
- "STEM Programs"
- "Liberal Arts"
- "Pre-Med"
- "Engineering"
- "Business"
- "International Students"
- "Financial Aid"
- "Essay Writing"
- "Interview Preparation"
- "Test Prep"
- "UC System"
- "Community College Transfer"
*/

-- Sample academic info structure (for reference when creating student profiles)
/*
{
  "gpa": 3.8,
  "transcripts": [
    {
      "academic_year": "2023-2024",
      "quarter": "Fall",
      "gpa": 3.9,
      "courses": [
        {"name": "AP Calculus BC", "grade": "A", "credits": 1},
        {"name": "AP Physics C", "grade": "A-", "credits": 1},
        {"name": "AP English Literature", "grade": "B+", "credits": 1},
        {"name": "AP US History", "grade": "A", "credits": 1}
      ]
    }
  ],
  "test_scores": {
    "sat": [
      {"score": 1520, "date": "2024-03-15", "is_official": true, "is_highest": true}
    ],
    "act": [
      {"score": 34, "date": "2024-02-10", "is_official": true, "is_highest": true}
    ]
  },
  "ap_courses": [
    {"subject": "Calculus BC", "score": 5, "year": "2024"},
    {"subject": "Physics C: Mechanics", "score": 4, "year": "2024"}
  ],
  "activities": [
    {
      "name": "Math Club",
      "type": "Academic",
      "position": "President",
      "description": "Led weekly meetings and organized math competitions",
      "start_date": "2022-09-01",
      "end_date": "2024-06-01",
      "hours_per_week": 5,
      "weeks_per_year": 36
    }
  ]
}
*/
