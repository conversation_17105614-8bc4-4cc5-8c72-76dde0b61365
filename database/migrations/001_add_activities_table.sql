-- Migration: Add activities table with Common App structure
-- Goal #4: Activity Forms (Common App Structure)
-- Date: 2025-01-07

-- Activities table (Goal #4: Activity Forms with Common App Structure)
CREATE TABLE IF NOT EXISTS activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES student_profiles(id) ON DELETE CASCADE,
    
    -- Common App structure fields
    activity_type activity_category NOT NULL,
    organization_name TEXT NOT NULL,
    position_title TEXT,
    description TEXT, -- Max 150 words (Common App standard)
    participation_grade_levels TEXT[] DEFAULT '{}', -- Array of grade levels: 9, 10, 11, 12, Post-graduate
    
    -- Time commitment
    hours_per_week INTEGER NOT NULL DEFAULT 0,
    weeks_per_year INTEGER NOT NULL DEFAULT 0,
    
    -- Leadership and recognition
    leadership_role BOOLEAN DEFAULT FALSE,
    awards_recognition TEXT, -- Awards and recognition received
    
    -- File uploads (Goal #4: File upload system)
    file_uploads JSONB DEFAULT '{
        "certificates": [],
        "videos": [],
        "photos": []
    }'::jsonb,
    
    -- Legacy fields for backward compatibility
    name TEXT, -- Will be populated from organization_name + position_title
    category TEXT, -- Will be populated from activity_type
    organization TEXT, -- Will be populated from organization_name
    position TEXT, -- Will be populated from position_title
    years_active TEXT, -- Derived from participation_grade_levels
    achievements TEXT[], -- Will be populated from awards_recognition
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_activities_student_id ON activities(student_id);
CREATE INDEX IF NOT EXISTS idx_activities_activity_type ON activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_activities_leadership_role ON activities(leadership_role);

-- Create trigger for updated_at
CREATE TRIGGER update_activities_updated_at 
    BEFORE UPDATE ON activities 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for activities
CREATE POLICY "Students can view their own activities" ON activities
    FOR SELECT USING (
        student_id IN (
            SELECT id FROM student_profiles 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Students can insert their own activities" ON activities
    FOR INSERT WITH CHECK (
        student_id IN (
            SELECT id FROM student_profiles 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Students can update their own activities" ON activities
    FOR UPDATE USING (
        student_id IN (
            SELECT id FROM student_profiles 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Students can delete their own activities" ON activities
    FOR DELETE USING (
        student_id IN (
            SELECT id FROM student_profiles 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Consultants can view activities of their students" ON activities
    FOR SELECT USING (
        student_id IN (
            SELECT sp.id FROM student_profiles sp
            JOIN student_consultant_relations scr ON sp.id = scr.student_id
            JOIN consultants c ON scr.consultant_id = c.id
            WHERE c.user_id = auth.uid() AND scr.status = 'active'
        )
    );

CREATE POLICY "Consultants can update activities of their students" ON activities
    FOR UPDATE USING (
        student_id IN (
            SELECT sp.id FROM student_profiles sp
            JOIN student_consultant_relations scr ON sp.id = scr.student_id
            JOIN consultants c ON scr.consultant_id = c.id
            WHERE c.user_id = auth.uid() AND scr.status = 'active'
        )
    );

CREATE POLICY "Admins can manage all activities" ON activities
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
