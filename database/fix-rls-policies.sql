-- Fix RLS Policies - Remove circular dependencies
-- This script fixes the infinite recursion issue in RLS policies

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view their own profile" ON users;
DROP POLICY IF EXISTS "Users can update their own profile" ON users;
DROP POLICY IF EXISTS "Ad<PERSON> can view all users" ON users;
DROP POLICY IF EXISTS "Ad<PERSON> can update all users" ON users;
DROP POLICY IF EXISTS "Admins can insert users" ON users;
DROP POLICY IF EXISTS "Service role can manage all users" ON users;

DROP POLICY IF EXISTS "Ad<PERSON> can manage all student profiles" ON student_profiles;
DROP POLICY IF EXISTS "Service role can manage all student profiles" ON student_profiles;

DROP POLICY IF EXISTS "<PERSON><PERSON> can manage all consultants" ON consultants;
DROP POLICY IF EXISTS "Service role can manage all consultants" ON consultants;

DROP POLICY IF EXISTS "Admins can manage schools" ON schools;
DROP POLICY IF EXISTS "Service role can manage schools" ON schools;

DROP POLICY IF EXISTS "Ad<PERSON> can manage all documents" ON documents;
DROP POLICY IF EXISTS "Service role can manage all documents" ON documents;

DROP POLICY IF EXISTS "<PERSON><PERSON> can manage all relations" ON student_consultant_relations;
DROP POLICY IF EXISTS "Service role can manage all relations" ON student_consultant_relations;

-- Create new simplified policies without circular dependencies

-- Users table policies
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid()::text = clerk_id);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid()::text = clerk_id);

-- Service role bypass for admin operations
CREATE POLICY "Service role can manage all users" ON users
    FOR ALL USING (auth.role() = 'service_role');

-- Student profiles policies
CREATE POLICY "Service role can manage all student profiles" ON student_profiles
    FOR ALL USING (auth.role() = 'service_role');

-- Consultants table policies  
CREATE POLICY "Service role can manage all consultants" ON consultants
    FOR ALL USING (auth.role() = 'service_role');

-- Schools table policies
CREATE POLICY "Service role can manage schools" ON schools
    FOR ALL USING (auth.role() = 'service_role');

-- Documents table policies
CREATE POLICY "Service role can manage all documents" ON documents
    FOR ALL USING (auth.role() = 'service_role');

-- Student-Consultant relations policies
CREATE POLICY "Service role can manage all relations" ON student_consultant_relations
    FOR ALL USING (auth.role() = 'service_role');
