# Google Workspace Setup Guide

This guide walks you through setting up Google Workspace integration for Lighten Counsel's document management system.

## Prerequisites

- Google Workspace admin account
- Google Cloud Platform account
- Domain ownership verification

## Step 1: Create Google Cloud Project

1. **Go to Google Cloud Console**
   - Visit [Google Cloud Console](https://console.cloud.google.com/)
   - Sign in with your Google account

2. **Create New Project**
   ```bash
   # Project details
   Project Name: Lighten Counsel
   Project ID: lighten-counsel-[random-id]
   Organization: Your Organization
   ```

3. **Enable Required APIs**
   Navigate to "APIs & Services" > "Library" and enable:
   - Google Docs API
   - Google Drive API
   - Google Workspace Admin SDK
   - Google Sheets API (optional, for analytics)

## Step 2: Create Service Account

1. **Navigate to Service Accounts**
   - Go to "IAM & Admin" > "Service Accounts"
   - Click "Create Service Account"

2. **Service Account Details**
   ```
   Service Account Name: lighten-counsel-service
   Service Account ID: lighten-counsel-service
   Description: Service account for Lighten Counsel document management
   ```

3. **Grant Roles**
   Add the following roles:
   - Editor (for Google Drive operations)
   - Service Account User

4. **Create and Download Key**
   - Click on the created service account
   - Go to "Keys" tab
   - Click "Add Key" > "Create New Key"
   - Choose JSON format
   - Download and securely store the key file

## Step 3: Enable Domain-Wide Delegation

1. **Configure Service Account**
   - In the service account details, check "Enable Google Workspace Domain-wide Delegation"
   - Note the "Client ID" (you'll need this later)

2. **Set OAuth Scopes**
   The service account needs these scopes:
   ```
   https://www.googleapis.com/auth/documents
   https://www.googleapis.com/auth/drive
   https://www.googleapis.com/auth/admin.directory.user.readonly
   https://www.googleapis.com/auth/admin.directory.group.readonly
   ```

## Step 4: Configure Google Workspace Admin Console

1. **Access Admin Console**
   - Go to [Google Admin Console](https://admin.google.com/)
   - Sign in with your Workspace admin account

2. **Navigate to API Controls**
   - Go to "Security" > "API Controls"
   - Click "Domain-wide Delegation"

3. **Add Service Account**
   - Click "Add New"
   - Enter the Client ID from Step 3
   - Add the OAuth scopes listed above
   - Click "Authorize"

## Step 5: Set Up Folder Structure

1. **Create Main Folder**
   - In Google Drive, create a folder named "Lighten Counsel Documents"
   - Note the folder ID from the URL

2. **Create Subfolders**
   ```
   📁 Lighten Counsel Documents/
   ├── 📁 Students/
   ├── 📁 Templates/
   │   ├── 📁 Personal Statements/
   │   ├── 📁 Essays/
   │   ├── 📁 Activity Resumes/
   │   └── 📁 Other/
   └── 📁 Archive/
   ```

3. **Set Folder Permissions**
   - Share the main folder with your service account email
   - Grant "Editor" access

## Step 6: Configure Environment Variables

Add these variables to your `.env.local` file:

```env
# Google Workspace Configuration
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
GOOGLE_PROJECT_ID=your-project-id
GOOGLE_CLIENT_ID=your-client-id
GOOGLE_CLIENT_SECRET=your-client-secret

# Admin Configuration
GOOGLE_ADMIN_EMAIL=<EMAIL>
GOOGLE_DOMAIN=yourdomain.com

# Folder IDs
GOOGLE_MAIN_FOLDER_ID=your-main-folder-id
GOOGLE_STUDENTS_FOLDER_ID=your-students-folder-id
GOOGLE_TEMPLATES_FOLDER_ID=your-templates-folder-id
```

## Step 7: Test the Integration

1. **Run the Test Script**
   ```bash
   # Create a test script to verify the setup
   npm run test:google-integration
   ```

2. **Verify Permissions**
   - Test document creation
   - Test permission assignment
   - Test folder access

## Step 8: Create Initial Templates

1. **Personal Statement Template**
   - Create a Google Doc in the Templates/Personal Statements folder
   - Add placeholder content and formatting
   - Note the document ID

2. **Essay Templates**
   - Create templates for common essay types
   - Organize by category (competitive, general, etc.)

3. **Activity Resume Template**
   - Create a structured template for activities
   - Include sections for different activity types

## Step 9: Database Migration

Run the database migration to create the new template tables:

```bash
# Apply the enhanced schema
psql -d your_database -f database/schema.sql

# Or using your migration tool
npm run db:migrate
```

## Step 10: Initial Template Data

Insert initial template metadata into the database:

```sql
-- Example template metadata
INSERT INTO template_metadata (
    google_doc_id,
    name,
    doc_type,
    category,
    description,
    word_limit,
    difficulty_level,
    is_featured
) VALUES (
    'your-template-doc-id',
    'Personal Statement Template - Competitive Schools',
    'personal_statement',
    'competitive',
    'Template designed for competitive college applications',
    650,
    'advanced',
    true
);
```

## Troubleshooting

### Common Issues

1. **"Access Denied" Errors**
   - Verify domain-wide delegation is enabled
   - Check OAuth scopes are correctly configured
   - Ensure service account has proper permissions

2. **"Folder Not Found" Errors**
   - Verify folder IDs in environment variables
   - Check folder sharing permissions
   - Ensure service account has access to folders

3. **"Quota Exceeded" Errors**
   - Check Google API quotas in Cloud Console
   - Implement rate limiting in your application
   - Consider upgrading your Google Cloud plan

### Testing Commands

```bash
# Test Google Docs API connection
curl -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  "https://docs.googleapis.com/v1/documents/your-doc-id"

# Test Drive API connection
curl -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  "https://www.googleapis.com/drive/v3/files/your-folder-id"
```

## Security Best Practices

1. **Service Account Key Security**
   - Store the private key securely
   - Use environment variables, not hardcoded values
   - Rotate keys regularly

2. **Permission Management**
   - Follow principle of least privilege
   - Regularly audit service account permissions
   - Monitor API usage and access logs

3. **Data Protection**
   - Enable audit logging in Google Workspace
   - Implement proper backup procedures
   - Use encryption for sensitive data

## Monitoring and Maintenance

1. **Set Up Monitoring**
   - Monitor API usage and quotas
   - Set up alerts for errors or unusual activity
   - Track document creation and permission changes

2. **Regular Maintenance**
   - Review and clean up old documents
   - Update templates based on usage analytics
   - Audit permissions and access logs

3. **Backup Strategy**
   - Implement regular backups of important documents
   - Test restore procedures
   - Document recovery processes

## Support

For additional help:
- [Google Workspace Admin Help](https://support.google.com/a/)
- [Google Cloud Documentation](https://cloud.google.com/docs)
- [Google APIs Documentation](https://developers.google.com/docs)

---

**Next Steps**: After completing this setup, proceed to [Deployment Guide](./DEPLOYMENT.md) for production deployment instructions.
