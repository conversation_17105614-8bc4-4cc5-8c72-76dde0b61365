# Contributing to Lighten Counsel

We welcome contributions to the Lighten Counsel project! This guide will help you get started with contributing to our college application management platform.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contributing Guidelines](#contributing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Documentation](#documentation)

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct:

- **Be respectful**: Treat all contributors with respect and kindness
- **Be inclusive**: Welcome newcomers and help them get started
- **Be collaborative**: Work together to improve the project
- **Be constructive**: Provide helpful feedback and suggestions
- **Be professional**: Maintain a professional tone in all interactions

## Getting Started

### Prerequisites

Before contributing, ensure you have:

- Node.js 18+ installed
- pnpm package manager
- Git for version control
- A code editor (VS Code recommended)
- Basic knowledge of React, Next.js, and TypeScript

### Development Environment

1. **Fork the repository**
   ```bash
   # Fork the repo on GitHub, then clone your fork
   git clone https://github.com/your-username/lighten-counsel.git
   cd lighten-counsel
   ```

2. **Set up upstream remote**
   ```bash
   git remote add upstream https://github.com/original-org/lighten-counsel.git
   ```

3. **Install dependencies**
   ```bash
   pnpm install
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Fill in your development environment variables
   ```

5. **Start development server**
   ```bash
   pnpm dev
   ```

## Development Setup

### Project Structure

```
src/
├── app/                    # Next.js App Router pages
├── components/            # Reusable UI components
│   ├── ui/               # Base UI components (shadcn/ui)
│   ├── dashboard/        # Dashboard-specific components
│   └── layout/           # Layout components
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions and configurations
├── types/                # TypeScript type definitions
└── constants/            # Application constants
```

### Key Technologies

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, shadcn/ui
- **Authentication**: Clerk
- **Database**: Supabase (PostgreSQL)
- **State Management**: React hooks, Zustand (if needed)
- **Forms**: React Hook Form with Zod validation

## Contributing Guidelines

### Types of Contributions

We welcome various types of contributions:

1. **Bug Fixes**: Fix existing issues or bugs
2. **Feature Development**: Implement new features
3. **Documentation**: Improve or add documentation
4. **Testing**: Add or improve tests
5. **Performance**: Optimize existing code
6. **UI/UX**: Improve user interface and experience

### Before You Start

1. **Check existing issues**: Look for existing issues or feature requests
2. **Create an issue**: If none exists, create a new issue describing your contribution
3. **Discuss approach**: Comment on the issue to discuss your approach
4. **Get assignment**: Wait for maintainer approval before starting work

### Branch Naming Convention

Use descriptive branch names following this pattern:

```
type/short-description

Examples:
- feature/essay-collaboration
- bugfix/document-sharing-permissions
- docs/api-documentation-update
- refactor/dashboard-components
```

### Commit Message Format

Follow conventional commit format:

```
type(scope): description

Examples:
- feat(essays): add collaborative editing with Google Docs
- fix(auth): resolve role-based access control issue
- docs(api): update endpoint documentation
- refactor(components): extract reusable form components
- test(dashboard): add unit tests for student dashboard
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

## Pull Request Process

### 1. Prepare Your Changes

```bash
# Create a new branch
git checkout -b feature/your-feature-name

# Make your changes
# ... code, test, commit ...

# Keep your branch up to date
git fetch upstream
git rebase upstream/main
```

### 2. Test Your Changes

```bash
# Run tests
pnpm test

# Run linting
pnpm lint

# Check TypeScript
pnpm type-check

# Build the project
pnpm build
```

### 3. Create Pull Request

1. **Push your branch**
   ```bash
   git push origin feature/your-feature-name
   ```

2. **Create PR on GitHub**
   - Use a descriptive title
   - Fill out the PR template
   - Link related issues
   - Add screenshots for UI changes

### 4. PR Template

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Refactoring
- [ ] Performance improvement

## Related Issues
Fixes #(issue number)

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots for UI changes.

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Tests pass locally
- [ ] Documentation updated
- [ ] No breaking changes (or documented)
```

## Coding Standards

### TypeScript

- Use strict TypeScript configuration
- Define proper types for all props and functions
- Avoid `any` type unless absolutely necessary
- Use type imports when importing types

```typescript
// Good
import type { User } from '@/types/user'
import { Button } from '@/components/ui/button'

interface Props {
  user: User
  onSave: (data: UserData) => void
}

// Avoid
const user: any = getUserData()
```

### React Components

- Use functional components with hooks
- Follow naming conventions (PascalCase for components)
- Use proper prop destructuring
- Implement proper error boundaries

```typescript
// Good
interface StudentDashboardProps {
  student: Student
  onUpdate: (student: Student) => void
}

export function StudentDashboard({ student, onUpdate }: StudentDashboardProps) {
  // Component implementation
}

// File naming: student-dashboard.tsx
```

### Styling

- Use Tailwind CSS utility classes
- Follow mobile-first responsive design
- Use shadcn/ui components when possible
- Maintain consistent spacing and typography

```typescript
// Good
<div className="flex flex-col gap-4 p-6 md:flex-row md:gap-6">
  <Button variant="outline" size="sm">
    Cancel
  </Button>
</div>
```

### API Routes

- Use proper HTTP status codes
- Implement error handling
- Validate input data with Zod
- Follow RESTful conventions

```typescript
// Good
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const validatedData = createUserSchema.parse(body)
    
    const user = await createUser(validatedData)
    
    return NextResponse.json({ success: true, data: user }, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data' },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
```

## Testing

### Testing Strategy

1. **Unit Tests**: Test individual components and functions
2. **Integration Tests**: Test component interactions
3. **E2E Tests**: Test complete user workflows

### Writing Tests

```typescript
// Component test example
import { render, screen } from '@testing-library/react'
import { StudentDashboard } from './student-dashboard'

describe('StudentDashboard', () => {
  it('displays student name', () => {
    const mockStudent = { id: '1', name: 'John Doe' }
    
    render(<StudentDashboard student={mockStudent} onUpdate={jest.fn()} />)
    
    expect(screen.getByText('John Doe')).toBeInTheDocument()
  })
})
```

### Running Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage

# Run E2E tests
pnpm test:e2e
```

## Documentation

### Code Documentation

- Add JSDoc comments for complex functions
- Document component props with TypeScript interfaces
- Include usage examples for reusable components

```typescript
/**
 * Creates a new essay for a student
 * @param studentId - The ID of the student
 * @param essayData - The essay data to create
 * @returns Promise resolving to the created essay
 */
export async function createEssay(
  studentId: string,
  essayData: CreateEssayData
): Promise<Essay> {
  // Implementation
}
```

### README Updates

When adding new features:
- Update the main README.md
- Add feature documentation
- Update API documentation if applicable
- Include setup instructions for new dependencies

## Getting Help

### Resources

- **Documentation**: Check the `/docs` directory
- **Issues**: Browse existing GitHub issues
- **Discussions**: Join GitHub Discussions for questions
- **Code Review**: Request reviews from maintainers

### Communication

- **GitHub Issues**: For bug reports and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Pull Request Comments**: For code-specific discussions
- **Email**: For security-related issues

## Recognition

Contributors will be recognized in:
- GitHub contributors list
- Project README
- Release notes for significant contributions

## License

By contributing to Lighten Counsel, you agree that your contributions will be licensed under the same license as the project (MIT License).

---

Thank you for contributing to Lighten Counsel! Your efforts help make college applications more manageable for students everywhere.
