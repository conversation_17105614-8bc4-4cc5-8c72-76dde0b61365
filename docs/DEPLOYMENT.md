# Deployment Guide

This guide covers deploying the Lighten Counsel application to production environments.

## Prerequisites

Before deploying, ensure you have:

- Node.js 18+ installed
- Access to your hosting platform (Vercel recommended)
- Supabase project set up
- Clerk application configured
- Google Cloud Platform project (for Docs API)
- Domain name (optional but recommended)

## Environment Configuration

### Required Environment Variables

Create a `.env.production` file with the following variables:

```env
# Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_xxxxxxxxxx
CLERK_SECRET_KEY=sk_live_xxxxxxxxxx
CLERK_WEBHOOK_SECRET=whsec_xxxxxxxxxx

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Google APIs
GOOGLE_CLIENT_ID=xxxxxxxxxx.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-xxxxxxxxxx
GOOGLE_REDIRECT_URI=https://your-domain.com/api/auth/google/callback

# Database
DATABASE_URL=postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres

# Optional: Analytics and Monitoring
NEXT_PUBLIC_ANALYTICS_ID=G-XXXXXXXXXX
SENTRY_DSN=https://<EMAIL>/xxxxxxxxxx
```

## Vercel Deployment (Recommended)

### 1. Prepare Your Repository

```bash
# Ensure your code is committed and pushed
git add .
git commit -m "Prepare for production deployment"
git push origin main
```

### 2. Connect to Vercel

1. Visit [vercel.com](https://vercel.com) and sign in
2. Click "New Project"
3. Import your GitHub repository
4. Configure project settings:
   - **Framework Preset**: Next.js
   - **Root Directory**: `./` (if repository root)
   - **Build Command**: `pnpm build`
   - **Output Directory**: `.next`

### 3. Configure Environment Variables

In the Vercel dashboard:

1. Go to **Settings** → **Environment Variables**
2. Add all required environment variables
3. Set appropriate environments (Production, Preview, Development)

### 4. Configure Build Settings

```json
{
  "buildCommand": "pnpm build",
  "devCommand": "pnpm dev",
  "installCommand": "pnpm install",
  "outputDirectory": ".next"
}
```

### 5. Deploy

1. Click **Deploy** in Vercel dashboard
2. Monitor build logs for any issues
3. Test the deployed application

### 6. Custom Domain (Optional)

1. Go to **Settings** → **Domains**
2. Add your custom domain
3. Configure DNS records as instructed
4. Enable SSL (automatic with Vercel)

## Manual Deployment

### 1. Build the Application

```bash
# Install dependencies
pnpm install --frozen-lockfile

# Build for production
pnpm build

# Test the build locally
pnpm start
```

### 2. Server Setup

#### Using PM2 (Process Manager)

```bash
# Install PM2 globally
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'lighten-counsel',
    script: 'npm',
    args: 'start',
    cwd: '/path/to/your/app',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 'max',
    exec_mode: 'cluster',
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
EOF

# Start the application
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

#### Using Docker

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json pnpm-lock.yaml* ./
RUN corepack enable pnpm && pnpm i --frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1
RUN corepack enable pnpm && pnpm build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

```bash
# Build and run Docker container
docker build -t lighten-counsel .
docker run -p 3000:3000 --env-file .env.production lighten-counsel
```

## Database Setup

### 1. Supabase Configuration

#### Production Database Setup

1. **Create Production Project**:
   - Go to [supabase.com](https://supabase.com)
   - Create a new project for production
   - Choose appropriate region and pricing tier

2. **Run Migrations**:
   ```bash
   # Install Supabase CLI
   npm install -g supabase

   # Login to Supabase
   supabase login

   # Link to your project
   supabase link --project-ref your-project-ref

   # Push database schema
   supabase db push
   ```

3. **Configure Row Level Security**:
   ```sql
   -- Enable RLS on all tables
   ALTER TABLE users ENABLE ROW LEVEL SECURITY;
   ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
   ALTER TABLE essays ENABLE ROW LEVEL SECURITY;
   -- ... (enable for all tables)

   -- Apply security policies (see DATABASE.md for complete policies)
   ```

4. **Set Up Backups**:
   - Configure automated backups in Supabase dashboard
   - Set up additional backup strategies if needed

### 2. Data Migration

If migrating from development:

```bash
# Export development data
supabase db dump --data-only > development_data.sql

# Import to production (be careful!)
psql "postgresql://postgres:[password]@db.your-project.supabase.co:5432/postgres" < development_data.sql
```

## Third-Party Service Configuration

### 1. Clerk Authentication

#### Production Setup

1. **Create Production Application**:
   - Go to Clerk dashboard
   - Create new application for production
   - Configure authentication methods

2. **Configure Domains**:
   - Add production domain to allowed origins
   - Set up redirect URLs
   - Configure webhook endpoints

3. **Set Up Webhooks**:
   ```bash
   # Webhook endpoint for user events
   https://your-domain.com/api/webhooks/clerk
   ```

### 2. Google Cloud Platform

#### Enable APIs

1. **Google Docs API**:
   - Enable in Google Cloud Console
   - Create service account credentials
   - Configure OAuth consent screen

2. **Configure OAuth**:
   - Add production domain to authorized origins
   - Set up redirect URIs
   - Configure scopes for document access

## SSL/TLS Configuration

### Vercel (Automatic)

Vercel automatically provides SSL certificates for all deployments.

### Manual Setup

#### Using Let's Encrypt with Nginx

```nginx
# /etc/nginx/sites-available/lighten-counsel
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Monitoring and Logging

### 1. Application Monitoring

#### Sentry Integration

```bash
# Install Sentry
pnpm add @sentry/nextjs

# Configure Sentry
# sentry.client.config.js
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  tracesSampleRate: 1.0,
});
```

#### Vercel Analytics

```bash
# Install Vercel Analytics
pnpm add @vercel/analytics

# Add to _app.tsx
import { Analytics } from '@vercel/analytics/react';

export default function App({ Component, pageProps }) {
  return (
    <>
      <Component {...pageProps} />
      <Analytics />
    </>
  );
}
```

### 2. Database Monitoring

- Monitor Supabase dashboard for performance metrics
- Set up alerts for high CPU/memory usage
- Monitor connection pool usage

### 3. Log Management

```bash
# PM2 log management
pm2 logs lighten-counsel
pm2 flush  # Clear logs
pm2 install pm2-logrotate  # Automatic log rotation
```

## Performance Optimization

### 1. Next.js Optimizations

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    optimizeCss: true,
  },
  images: {
    domains: ['your-domain.com'],
    formats: ['image/webp', 'image/avif'],
  },
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
}

module.exports = nextConfig
```

### 2. CDN Configuration

If using a CDN:
- Configure caching headers
- Set up static asset optimization
- Enable compression

## Security Checklist

### Pre-Deployment Security

- [ ] Environment variables secured
- [ ] Database RLS policies implemented
- [ ] API rate limiting configured
- [ ] CORS properly configured
- [ ] Authentication flows tested
- [ ] Input validation implemented
- [ ] SQL injection prevention verified
- [ ] XSS protection enabled

### Post-Deployment Security

- [ ] SSL certificate installed and working
- [ ] Security headers configured
- [ ] Regular security updates scheduled
- [ ] Backup and recovery procedures tested
- [ ] Monitoring and alerting set up
- [ ] Incident response plan documented

## Rollback Procedures

### Vercel Rollback

1. Go to Vercel dashboard
2. Navigate to **Deployments**
3. Find previous working deployment
4. Click **Promote to Production**

### Manual Rollback

```bash
# Using PM2
pm2 stop lighten-counsel
# Deploy previous version
pm2 start ecosystem.config.js

# Using Docker
docker stop lighten-counsel
docker run -d --name lighten-counsel-backup previous-image-tag
```

## Maintenance

### Regular Tasks

1. **Weekly**:
   - Review application logs
   - Check performance metrics
   - Update dependencies (security patches)

2. **Monthly**:
   - Database maintenance and optimization
   - Review and rotate API keys
   - Performance analysis and optimization

3. **Quarterly**:
   - Security audit
   - Backup and recovery testing
   - Capacity planning review

### Update Procedures

```bash
# Update dependencies
pnpm update

# Test updates
pnpm test
pnpm build

# Deploy updates
git add .
git commit -m "Update dependencies"
git push origin main
```

---

For additional deployment support, consult the platform-specific documentation or contact the development team.
