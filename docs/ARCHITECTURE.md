# System Architecture

This document describes the technical architecture of the Lighten Counsel college application management system.

## Overview

Lighten Counsel is built as a modern web application using a serverless architecture with the following key principles:

- **Role-based Access Control**: Different interfaces for students, consultants, and administrators
- **Real-time Collaboration**: Google Docs integration for document editing
- **Scalable Infrastructure**: Serverless deployment with automatic scaling
- **Security First**: End-to-end encryption and comprehensive access controls
- **Mobile Responsive**: Progressive web app capabilities

## High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │   API Gateway   │    │   External APIs │
│                 │    │                 │    │                 │
│ • Web Browser   │◄──►│ • Next.js API   │◄──►│ • Google Docs   │
│ • Mobile Web    │    │ • Authentication│    │ • Clerk Auth    │
│ • PWA           │    │ • Rate Limiting │    │ • Email Service │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│                 │    │                 │    │                 │
│ • Next.js 15    │    │ • API Routes    │    │ • Supabase      │
│ • React 19      │    │ • Server Actions│    │ • PostgreSQL    │
│ • TypeScript    │    │ • Middleware    │    │ • Row Level     │
│ • Tailwind CSS  │    │ • Webhooks      │    │   Security      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Frontend Architecture

### Component Hierarchy

```
App
├── Layout
│   ├── Header
│   │   ├── Navigation
│   │   ├── UserMenu
│   │   └── Notifications
│   ├── Sidebar
│   │   ├── RoleBasedNavigation
│   │   └── QuickActions
│   └── Footer
├── Dashboard (Role-specific)
│   ├── StudentDashboard
│   │   ├── ProgressOverview
│   │   ├── RecentActivity
│   │   ├── UpcomingDeadlines
│   │   └── QuickActions
│   ├── ConsultantDashboard
│   │   ├── StudentOverview
│   │   ├── ReviewQueue
│   │   ├── PerformanceMetrics
│   │   └── MeetingSchedule
│   └── AdminDashboard
│       ├── SystemMetrics
│       ├── UserManagement
│       ├── Analytics
│       └── SystemHealth
└── Feature Modules
    ├── Essays
    ├── Documents
    ├── Academics
    ├── Activities
    ├── Schools
    └── Users
```

### State Management

```typescript
// Global State (Zustand)
interface AppState {
  user: User | null
  theme: 'light' | 'dark'
  notifications: Notification[]
  sidebarCollapsed: boolean
}

// Local State (React Hooks)
interface ComponentState {
  loading: boolean
  error: string | null
  data: any
}

// Server State (React Query/SWR)
interface ServerState {
  essays: Essay[]
  documents: Document[]
  students: Student[]
  // Cached and synchronized with server
}
```

### Routing Structure

```
/
├── /dashboard
│   ├── / (role-based redirect)
│   ├── /essays
│   │   ├── /
│   │   ├── /new
│   │   └── /[id]
│   ├── /documents
│   │   ├── /
│   │   ├── /new
│   │   └── /[id]
│   ├── /academics
│   │   ├── /gpa
│   │   ├── /test-scores
│   │   └── /transcripts
│   ├── /activities
│   ├── /schools
│   ├── /students (consultant/admin)
│   ├── /users (admin)
│   └── /analytics (admin)
├── /api
│   ├── /auth
│   ├── /users
│   ├── /essays
│   ├── /documents
│   ├── /schools
│   └── /webhooks
└── /auth
    ├── /sign-in
    ├── /sign-up
    └── /callback
```

## Backend Architecture

### API Layer

```typescript
// API Route Structure
/api
├── /auth
│   ├── /me (GET, PATCH)
│   └── /session (GET)
├── /users
│   ├── / (GET, POST) [admin]
│   ├── /[id] (GET, PATCH, DELETE) [admin]
│   └── /me (GET, PATCH)
├── /students
│   ├── / (GET) [consultant/admin]
│   ├── /[id] (GET, PATCH) [consultant/admin]
│   └── /[id]/assign (POST) [admin]
├── /essays
│   ├── / (GET, POST)
│   ├── /[id] (GET, PATCH, DELETE)
│   └── /[id]/share (POST)
├── /documents
│   ├── / (GET, POST)
│   ├── /[id] (GET, PATCH, DELETE)
│   ├── /[id]/share (POST)
│   └── /[id]/collaborate (POST)
└── /webhooks
    ├── /clerk (POST)
    └── /google-docs (POST)
```

### Middleware Stack

```typescript
// Request Processing Pipeline
Request
  ↓
1. CORS Middleware
  ↓
2. Rate Limiting
  ↓
3. Authentication (Clerk)
  ↓
4. Authorization (Role-based)
  ↓
5. Input Validation (Zod)
  ↓
6. Business Logic
  ↓
7. Database Operations
  ↓
8. Response Formatting
  ↓
Response
```

### Authentication Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant A as App
    participant CL as Clerk
    participant DB as Database

    C->>A: Access protected route
    A->>CL: Verify session token
    CL->>A: Return user data
    A->>DB: Query user profile
    DB->>A: Return user with role
    A->>A: Check permissions
    A->>C: Render role-based UI
```

## Database Architecture

### Entity Relationship Diagram

```
Users (1) ──── (M) Student_Consultant_Assignments (M) ──── (1) Users
  │                                                           │
  │ (1)                                                    (1) │
  │                                                           │
  ▼ (M)                                                    ▼ (M)
Documents                                               Essays
  │                                                           │
  │ (1)                                                    (1) │
  │                                                           │
  ▼ (M)                                                    ▼ (M)
Document_Shares                                         Feedback
  │
  │ (M)
  │
  ▼ (1)
Users

Users (1) ──── (M) Academic_Records
Users (1) ──── (M) Activities  
Users (1) ──── (M) Target_Schools
Users (1) ──── (M) Meetings
```

### Data Access Patterns

```typescript
// Repository Pattern
interface UserRepository {
  findById(id: string): Promise<User | null>
  findByRole(role: UserRole): Promise<User[]>
  create(data: CreateUserData): Promise<User>
  update(id: string, data: UpdateUserData): Promise<User>
  delete(id: string): Promise<void>
}

// Service Layer
class UserService {
  constructor(private userRepo: UserRepository) {}
  
  async assignConsultant(studentId: string, consultantId: string): Promise<void> {
    // Business logic for assignment
  }
}

// Controller Layer
export async function POST(request: Request) {
  const userService = new UserService(new SupabaseUserRepository())
  // Handle request
}
```

## Security Architecture

### Authentication & Authorization

```typescript
// Role-based Access Control
enum UserRole {
  STUDENT = 'student',
  CONSULTANT = 'consultant',
  ADMIN = 'admin'
}

// Permission Matrix
const permissions = {
  [UserRole.STUDENT]: [
    'read:own_profile',
    'update:own_profile',
    'create:own_essays',
    'read:own_documents',
    'share:own_documents'
  ],
  [UserRole.CONSULTANT]: [
    'read:assigned_students',
    'read:student_documents',
    'create:feedback',
    'update:student_progress'
  ],
  [UserRole.ADMIN]: [
    'read:all_users',
    'create:users',
    'update:users',
    'delete:users',
    'read:system_analytics'
  ]
}
```

### Data Protection

```sql
-- Row Level Security Policies
CREATE POLICY "Users can view own data" ON users
  FOR SELECT USING (clerk_id = auth.jwt() ->> 'sub');

CREATE POLICY "Consultants can view assigned students" ON users
  FOR SELECT USING (
    role = 'student' AND id IN (
      SELECT student_id FROM student_consultant_assignments 
      WHERE consultant_id = get_current_user_id()
      AND status = 'active'
    )
  );
```

### API Security

```typescript
// Rate Limiting
const rateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
})

// Input Validation
const createEssaySchema = z.object({
  title: z.string().min(1).max(200),
  type: z.enum(['personal_statement', 'supplemental']),
  content: z.string().optional(),
  maxWords: z.number().positive().optional()
})
```

## Integration Architecture

### Google Docs Integration

```typescript
// Google Docs Service
class GoogleDocsService {
  async createDocument(title: string, content?: string): Promise<GoogleDoc> {
    // Create document via Google Docs API
  }
  
  async shareDocument(docId: string, email: string, role: 'reader' | 'writer'): Promise<void> {
    // Share document with user
  }
  
  async getDocumentContent(docId: string): Promise<string> {
    // Retrieve document content
  }
}

// Webhook Handler
export async function POST(request: Request) {
  const event = await request.json()
  
  if (event.type === 'document.updated') {
    await updateDocumentMetadata(event.documentId, {
      lastModified: new Date(),
      wordCount: event.wordCount
    })
  }
}
```

### Email Integration

```typescript
// Email Service
class EmailService {
  async sendDeadlineReminder(student: Student, deadline: Date): Promise<void> {
    // Send email notification
  }
  
  async sendDocumentShared(recipient: string, document: Document): Promise<void> {
    // Notify about shared document
  }
}
```

## Performance Architecture

### Caching Strategy

```typescript
// Multi-level Caching
1. Browser Cache (Static Assets)
   - Images, CSS, JS files
   - Service Worker for offline support

2. CDN Cache (Vercel Edge)
   - API responses
   - Static pages

3. Application Cache (Redis/Memory)
   - User sessions
   - Frequently accessed data

4. Database Cache (Supabase)
   - Query result caching
   - Connection pooling
```

### Optimization Techniques

```typescript
// Code Splitting
const EssayEditor = lazy(() => import('./essay-editor'))
const DocumentViewer = lazy(() => import('./document-viewer'))

// Image Optimization
import Image from 'next/image'

<Image
  src="/profile.jpg"
  alt="Profile"
  width={100}
  height={100}
  priority={false}
  placeholder="blur"
/>

// API Response Optimization
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '1')
  const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)
  
  // Implement pagination and field selection
}
```

## Monitoring & Observability

### Logging Architecture

```typescript
// Structured Logging
import { logger } from '@/lib/logger'

logger.info('User created essay', {
  userId: user.id,
  essayId: essay.id,
  essayType: essay.type,
  timestamp: new Date().toISOString()
})

logger.error('Failed to save document', {
  error: error.message,
  userId: user.id,
  documentId: document.id,
  stack: error.stack
})
```

### Metrics Collection

```typescript
// Performance Metrics
const metrics = {
  'api.response_time': responseTime,
  'database.query_time': queryTime,
  'user.action': actionType,
  'error.count': errorCount
}

// Health Checks
export async function GET() {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      database: await checkDatabaseHealth(),
      auth: await checkAuthHealth(),
      storage: await checkStorageHealth()
    }
  }
  
  return Response.json(health)
}
```

## Deployment Architecture

### Infrastructure as Code

```yaml
# vercel.json
{
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "regions": ["iad1"],
  "env": {
    "NODE_ENV": "production"
  }
}
```

### CI/CD Pipeline

```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: pnpm install
      - run: pnpm test
      - run: pnpm build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

This architecture provides a scalable, secure, and maintainable foundation for the Lighten Counsel platform, supporting the complex workflows of college application management while ensuring excellent user experience across all roles.
