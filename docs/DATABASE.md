# Database Schema Documentation

This document describes the database schema for the Lighten Counsel application using Supabase (PostgreSQL).

## Overview

The database is designed to support a multi-tenant college application management system with role-based access control. It uses Row Level Security (RLS) policies to ensure data isolation and security.

## Tables

### users
Stores user account information and profiles.

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  clerk_id TEXT UNIQUE NOT NULL,
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  role user_role NOT NULL DEFAULT 'student',
  status user_status NOT NULL DEFAULT 'active',
  profile JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enum types
CREATE TYPE user_role AS ENUM ('student', 'consultant', 'admin');
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended');
```

**Profile JSONB Structure:**
```json
{
  "grade": "12th Grade",
  "gpa": 3.85,
  "phone": "******-0123",
  "address": {
    "street": "123 Main St",
    "city": "Anytown",
    "state": "CA",
    "zip": "12345"
  },
  "parentContact": {
    "name": "Jane Doe",
    "email": "<EMAIL>",
    "phone": "******-0124"
  }
}
```

### student_consultant_assignments
Maps students to their assigned consultants.

```sql
CREATE TABLE student_consultant_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  consultant_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  assigned_by UUID REFERENCES users(id),
  status assignment_status DEFAULT 'active',
  notes TEXT,
  UNIQUE(student_id, consultant_id)
);

CREATE TYPE assignment_status AS ENUM ('active', 'inactive', 'completed');
```

### documents
Stores document metadata and Google Docs integration.

```sql
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  type document_type NOT NULL,
  status document_status DEFAULT 'draft',
  owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  google_doc_id TEXT,
  google_doc_url TEXT,
  word_count INTEGER DEFAULT 0,
  max_words INTEGER,
  content TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE document_type AS ENUM (
  'personal_statement',
  'supplemental_essay',
  'recommendation_letter',
  'resume',
  'transcript',
  'other'
);

CREATE TYPE document_status AS ENUM (
  'draft',
  'in_progress',
  'review',
  'completed',
  'archived'
);
```

**Metadata JSONB Structure:**
```json
{
  "targetSchools": ["Harvard University", "Stanford University"],
  "prompt": "Why do you want to attend this university?",
  "deadline": "2024-11-01",
  "priority": "high",
  "tags": ["personal", "leadership"],
  "version": 1
}
```

### document_shares
Manages document sharing permissions.

```sql
CREATE TABLE document_shares (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
  shared_with_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  permission share_permission DEFAULT 'viewer',
  shared_by UUID NOT NULL REFERENCES users(id),
  shared_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(document_id, shared_with_id)
);

CREATE TYPE share_permission AS ENUM ('viewer', 'commenter', 'editor');
```

### essays
Specialized table for essay management.

```sql
CREATE TABLE essays (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  type essay_type NOT NULL,
  prompt TEXT,
  content TEXT,
  word_count INTEGER DEFAULT 0,
  max_words INTEGER,
  status essay_status DEFAULT 'not_started',
  target_schools TEXT[] DEFAULT '{}',
  deadline DATE,
  priority priority_level DEFAULT 'medium',
  feedback JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE essay_type AS ENUM (
  'personal_statement',
  'supplemental',
  'why_school',
  'why_major',
  'diversity',
  'challenge',
  'activity',
  'other'
);

CREATE TYPE essay_status AS ENUM (
  'not_started',
  'in_progress',
  'first_draft',
  'revision',
  'final_review',
  'completed'
);

CREATE TYPE priority_level AS ENUM ('low', 'medium', 'high', 'urgent');
```

### academic_records
Stores student academic information.

```sql
CREATE TABLE academic_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  gpa_cumulative DECIMAL(3,2),
  gpa_weighted DECIMAL(3,2),
  class_rank INTEGER,
  class_size INTEGER,
  test_scores JSONB DEFAULT '{}',
  ap_courses JSONB DEFAULT '[]',
  honors_courses JSONB DEFAULT '[]',
  transcripts JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(student_id)
);
```

**Test Scores JSONB Structure:**
```json
{
  "sat": {
    "total": 1450,
    "math": 750,
    "verbal": 700,
    "date": "2024-10-01",
    "attempts": 2
  },
  "act": {
    "composite": 32,
    "english": 34,
    "math": 30,
    "reading": 33,
    "science": 31,
    "date": "2024-09-01"
  },
  "ap_exams": [
    {
      "subject": "Calculus BC",
      "score": 5,
      "year": 2024
    }
  ]
}
```

### activities
Stores extracurricular activities and achievements.

```sql
CREATE TABLE activities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  category activity_category NOT NULL,
  organization TEXT,
  position TEXT,
  description TEXT,
  years_active TEXT,
  hours_per_week INTEGER,
  weeks_per_year INTEGER,
  achievements TEXT[],
  leadership_role BOOLEAN DEFAULT FALSE,
  awards JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE activity_category AS ENUM (
  'academic',
  'arts',
  'athletics',
  'community_service',
  'employment',
  'leadership',
  'music',
  'research',
  'other'
);
```

### target_schools
Manages student's target schools and application status.

```sql
CREATE TABLE target_schools (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  school_name TEXT NOT NULL,
  school_type school_type,
  location TEXT,
  admission_rate DECIMAL(4,2),
  application_round application_round,
  application_deadline DATE,
  application_status application_status DEFAULT 'not_started',
  decision_date DATE,
  decision_result decision_result,
  requirements JSONB DEFAULT '{}',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE school_type AS ENUM ('public', 'private', 'community');
CREATE TYPE application_round AS ENUM ('early_decision', 'early_action', 'regular_decision');
CREATE TYPE application_status AS ENUM (
  'not_started',
  'in_progress',
  'submitted',
  'under_review',
  'interview_scheduled',
  'decision_pending'
);
CREATE TYPE decision_result AS ENUM ('accepted', 'rejected', 'waitlisted', 'deferred');
```

### meetings
Tracks consultant-student meetings and appointments.

```sql
CREATE TABLE meetings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  consultant_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
  duration_minutes INTEGER DEFAULT 60,
  meeting_type meeting_type DEFAULT 'consultation',
  status meeting_status DEFAULT 'scheduled',
  meeting_url TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE meeting_type AS ENUM ('consultation', 'review', 'planning', 'check_in');
CREATE TYPE meeting_status AS ENUM ('scheduled', 'completed', 'cancelled', 'no_show');
```

### feedback
Stores feedback and comments on documents and essays.

```sql
CREATE TABLE feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
  essay_id UUID REFERENCES essays(id) ON DELETE CASCADE,
  author_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  type feedback_type DEFAULT 'comment',
  status feedback_status DEFAULT 'active',
  parent_id UUID REFERENCES feedback(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CHECK (document_id IS NOT NULL OR essay_id IS NOT NULL)
);

CREATE TYPE feedback_type AS ENUM ('comment', 'suggestion', 'approval', 'revision_request');
CREATE TYPE feedback_status AS ENUM ('active', 'resolved', 'archived');
```

## Indexes

```sql
-- Performance indexes
CREATE INDEX idx_users_clerk_id ON users(clerk_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_documents_owner_id ON documents(owner_id);
CREATE INDEX idx_documents_type ON documents(type);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_essays_student_id ON essays(student_id);
CREATE INDEX idx_essays_status ON essays(status);
CREATE INDEX idx_activities_student_id ON activities(student_id);
CREATE INDEX idx_target_schools_student_id ON target_schools(student_id);
CREATE INDEX idx_meetings_student_id ON meetings(student_id);
CREATE INDEX idx_meetings_consultant_id ON meetings(consultant_id);
CREATE INDEX idx_feedback_document_id ON feedback(document_id);
CREATE INDEX idx_feedback_essay_id ON feedback(essay_id);

-- Composite indexes
CREATE INDEX idx_student_consultant_active ON student_consultant_assignments(student_id, consultant_id) 
WHERE status = 'active';
```

## Row Level Security (RLS) Policies

### Users Table
```sql
-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (clerk_id = auth.jwt() ->> 'sub');

-- Consultants can view assigned students
CREATE POLICY "Consultants can view assigned students" ON users
  FOR SELECT USING (
    role = 'student' AND id IN (
      SELECT student_id FROM student_consultant_assignments 
      WHERE consultant_id = (SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub')
      AND status = 'active'
    )
  );

-- Admins can view all users
CREATE POLICY "Admins can view all users" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE clerk_id = auth.jwt() ->> 'sub' 
      AND role = 'admin'
    )
  );
```

### Documents Table
```sql
-- Users can manage their own documents
CREATE POLICY "Users can manage own documents" ON documents
  FOR ALL USING (owner_id = (SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub'));

-- Users can view documents shared with them
CREATE POLICY "Users can view shared documents" ON documents
  FOR SELECT USING (
    id IN (
      SELECT document_id FROM document_shares 
      WHERE shared_with_id = (SELECT id FROM users WHERE clerk_id = auth.jwt() ->> 'sub')
    )
  );
```

## Functions and Triggers

### Update Timestamp Trigger
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to all tables with updated_at column
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ... (apply to other tables)
```

### Word Count Update Function
```sql
CREATE OR REPLACE FUNCTION update_word_count()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.content IS NOT NULL THEN
    NEW.word_count = array_length(string_to_array(trim(NEW.content), ' '), 1);
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_document_word_count BEFORE INSERT OR UPDATE ON documents
  FOR EACH ROW EXECUTE FUNCTION update_word_count();

CREATE TRIGGER update_essay_word_count BEFORE INSERT OR UPDATE ON essays
  FOR EACH ROW EXECUTE FUNCTION update_word_count();
```

## Backup and Maintenance

### Automated Backups
Supabase provides automated daily backups. For additional security:

1. Set up weekly full database dumps
2. Store backups in multiple locations
3. Test backup restoration procedures regularly

### Maintenance Tasks
- Monitor table sizes and performance
- Update statistics regularly
- Review and optimize slow queries
- Clean up old data based on retention policies
