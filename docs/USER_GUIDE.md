# User Guide - Lighten Counsel

This guide provides comprehensive instructions for using the Lighten Counsel college application management platform.

## Getting Started

### Account Setup

1. **Sign Up**: Visit the platform and create an account using your email
2. **Profile Completion**: Complete your profile with basic information
3. **Role Assignment**: Your role (Student, Consultant, or Admin) will be assigned by an administrator

### First Login

After logging in, you'll be directed to your role-specific dashboard:
- **Students**: Personal application dashboard
- **Consultants**: Student management interface
- **Admins**: System overview and management tools

## For Students

### Dashboard Overview

Your dashboard provides a comprehensive view of your college application progress:

- **Progress Overview**: Visual representation of your application completion
- **Recent Activity**: Latest updates on your essays, documents, and applications
- **Upcoming Deadlines**: Important dates for applications and essays
- **Quick Actions**: Fast access to create new essays or update information

### Managing Essays

#### Creating a New Essay

1. Navigate to **Essays** from the sidebar
2. Click **"New Essay"**
3. Fill in the essay details:
   - **Title**: Give your essay a descriptive name
   - **Type**: Select from Personal Statement, Supplemental, Why School, etc.
   - **Prompt**: Enter the essay prompt or question
   - **Word Limit**: Set the maximum word count
   - **Target Schools**: Select which schools this essay is for
   - **Deadline**: Set the submission deadline

#### Writing and Editing

- **Google Docs Integration**: Essays automatically create Google Docs for collaborative editing
- **Word Count Tracking**: Real-time word count with visual indicators
- **Status Updates**: Track progress from "Not Started" to "Completed"
- **Collaboration**: Share with your consultant for feedback and review

#### Essay Status Workflow

1. **Not Started**: Essay created but no content written
2. **In Progress**: Actively writing the essay
3. **First Draft**: Initial version completed
4. **Revision**: Making changes based on feedback
5. **Final Review**: Last review before submission
6. **Completed**: Essay finalized and ready for submission

### Academic Records

#### GPA and Grades

1. Go to **Academics** → **GPA & Grades**
2. Enter your:
   - Cumulative GPA
   - Weighted GPA (if applicable)
   - Class rank and class size

#### Test Scores

1. Navigate to **Academics** → **Test Scores**
2. Add your standardized test scores:
   - **SAT**: Total score, Math, and Verbal sections
   - **ACT**: Composite and individual section scores
   - **AP Exams**: Subject and scores
   - **Other Tests**: TOEFL, IELTS, Subject Tests

#### Transcripts

1. Go to **Academics** → **Transcripts**
2. Upload official transcripts
3. Add course information and grades

### Activities and Achievements

#### Adding Activities

1. Navigate to **Activities**
2. Click **"Add Activity"**
3. Complete the activity form:
   - **Name**: Activity title
   - **Category**: Academic, Arts, Athletics, Community Service, etc.
   - **Organization**: School, club, or organization name
   - **Position**: Your role or title
   - **Years Active**: Duration of participation
   - **Time Commitment**: Hours per week and weeks per year
   - **Description**: Detailed description of your involvement
   - **Achievements**: Notable accomplishments or awards

#### Activity Categories

- **Academic**: Honor societies, academic competitions
- **Arts**: Theater, visual arts, creative writing
- **Athletics**: Sports teams, individual sports
- **Community Service**: Volunteer work, service projects
- **Employment**: Part-time jobs, internships
- **Leadership**: Student government, club leadership
- **Music**: Band, orchestra, choir
- **Research**: Independent research, lab work

### Target Schools Management

#### Adding Schools

1. Go to **Schools**
2. Click **"Add School"**
3. Enter school information:
   - **School Name**: Full university name
   - **Location**: City and state
   - **School Type**: Public, Private, Community
   - **Application Round**: Early Decision, Early Action, Regular Decision
   - **Application Deadline**: Submission deadline
   - **Requirements**: Specific application requirements

#### Application Tracking

Track your application status for each school:
- **Not Started**: Haven't begun the application
- **In Progress**: Actively working on application
- **Submitted**: Application submitted
- **Under Review**: School is reviewing your application
- **Interview Scheduled**: Interview arranged
- **Decision Pending**: Waiting for admission decision

### Document Management

#### Creating Documents

1. Navigate to **Documents**
2. Click **"New Document"**
3. Choose document type:
   - Personal Statement
   - Supplemental Essay
   - Resume
   - Recommendation Letter
   - Other

#### Collaboration Features

- **Sharing**: Share documents with your consultant
- **Comments**: Receive feedback and suggestions
- **Version History**: Track changes and revisions
- **Real-time Editing**: Collaborate simultaneously

## For Consultants

### Student Management

#### Student Overview

Your dashboard shows:
- **Assigned Students**: List of students under your guidance
- **Progress Summary**: Overall progress across all students
- **Urgent Items**: Students needing immediate attention
- **Recent Activity**: Latest updates from your students

#### Managing Student Assignments

1. View your assigned students in the **Students** section
2. Click on a student to view their detailed profile
3. Monitor their progress across all application components
4. Provide guidance and feedback as needed

### Document Review

#### Review Queue

1. Navigate to **Documents** → **Review Queue**
2. See all documents awaiting your review
3. Priority indicators help you focus on urgent items
4. Filter by student, document type, or deadline

#### Providing Feedback

1. Open a document for review
2. Use Google Docs commenting features
3. Provide constructive feedback and suggestions
4. Update document status after review

### Student Progress Monitoring

#### Progress Analytics

- **Individual Student Progress**: Detailed view of each student's advancement
- **Completion Rates**: Track essay and application completion
- **Deadline Management**: Monitor upcoming deadlines
- **Engagement Metrics**: Student activity and responsiveness

#### Meeting Management

1. Schedule meetings with students
2. Set agenda and objectives
3. Track meeting outcomes and action items
4. Follow up on commitments

## For Administrators

### User Management

#### Adding Users

1. Go to **Users** → **Add User**
2. Enter user information:
   - Name and email
   - Role assignment
   - Initial permissions

#### Managing Roles

- **Students**: Access to personal dashboard and application tools
- **Consultants**: Student management and review capabilities
- **Admins**: Full system access and management

#### Consultant Assignments

1. Navigate to **Users** → **Assignments**
2. Assign students to consultants
3. Monitor workload distribution
4. Reassign as needed

### System Analytics

#### Dashboard Metrics

- **User Statistics**: Total users by role and status
- **Application Progress**: System-wide completion rates
- **Document Activity**: Creation and collaboration metrics
- **Performance Indicators**: Key system health metrics

#### Reporting

Generate reports on:
- Student progress and outcomes
- Consultant performance and workload
- System usage and engagement
- Application success rates

### System Configuration

#### School Database

1. Maintain comprehensive school information
2. Update admission requirements and deadlines
3. Add new schools as needed
4. Manage school categories and types

#### Template Management

1. Create and maintain essay templates
2. Update prompts and requirements
3. Manage document templates
4. Version control for templates

## Tips and Best Practices

### For Students

1. **Start Early**: Begin your applications well before deadlines
2. **Stay Organized**: Use the platform's organizational tools
3. **Communicate Regularly**: Keep in touch with your consultant
4. **Track Progress**: Regularly update your application status
5. **Backup Important Work**: Save copies of important documents

### For Consultants

1. **Regular Check-ins**: Schedule consistent meetings with students
2. **Timely Feedback**: Provide prompt responses to student work
3. **Set Clear Expectations**: Communicate deadlines and requirements
4. **Monitor Progress**: Use analytics to track student advancement
5. **Encourage Independence**: Help students develop self-management skills

### For Administrators

1. **Monitor System Health**: Regularly check performance metrics
2. **User Support**: Provide timely assistance to users
3. **Data Security**: Ensure proper backup and security measures
4. **Training**: Provide adequate training for new users
5. **Continuous Improvement**: Gather feedback and implement improvements

## Troubleshooting

### Common Issues

#### Login Problems
- Clear browser cache and cookies
- Check email for verification links
- Contact support if issues persist

#### Document Access Issues
- Verify sharing permissions
- Check Google account integration
- Ensure proper role assignments

#### Performance Issues
- Check internet connection
- Try different browser
- Clear browser cache
- Contact support for persistent issues

### Getting Help

1. **In-App Help**: Use the help section within the platform
2. **Documentation**: Refer to this user guide and API documentation
3. **Support Tickets**: Submit support requests for technical issues
4. **Training Sessions**: Attend scheduled training sessions
5. **Community Forums**: Participate in user discussions

## Security and Privacy

### Data Protection

- All data is encrypted in transit and at rest
- Regular security audits and updates
- Compliance with educational privacy standards
- Secure authentication through Clerk

### Best Practices

1. **Strong Passwords**: Use complex, unique passwords
2. **Regular Logout**: Log out when finished using the platform
3. **Secure Networks**: Use trusted internet connections
4. **Privacy Settings**: Review and adjust sharing permissions
5. **Report Issues**: Immediately report any security concerns

---

For additional support, contact our help desk or refer to the technical documentation.
